package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/sendemail/env"
)

const (
	devPort        = ":5002"            // Port of the local fiber service in dev mode.
	serviceName    = "email-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"            // Version of the service.
)

type (
	Event struct {
		GenEmailID    uint   `json:"generatedEmailID" validate:"required"`
		EmailAddress  string `json:"senderEmailAddress" validate:"required"`
		EmailProvider string `json:"emailProvider" validate:"oneof=gmail outlook"`
	}
)

var (
	getUserByEmail = userDB.GetByEmail
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}
	log.Debug(ctx, "IN DEBUG MODE")

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)
	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.AppEnv == "dev" {
		panic(runLocalServer())
	}

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

func handlerWithLogging(ctx context.Context, request *Event) (result *events.LambdaFunctionURLResponse, err error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		log.Info(ctx, "received AWS Step Function Request", zap.Any("request", request))

		var path string

		if request.EmailProvider == "gmail" {
			path = "/email/gmail"
		}

		if request.EmailProvider == "outlook" {
			path = "/email/outlook"
		}

		payload, err := json.Marshal(request)
		if err != nil {
			log.Error(ctx, "error marshaling email payload to JSON", zap.Error(err))
			return
		}

		event := events.LambdaFunctionURLRequest{
			Body:    string(payload),
			RawPath: path,
		}

		if result, err = handler(ctx, event); err != nil {
			return
		}

		log.Info(ctx, "returning AWS Step Function Response", zap.Any("response", result))
	})

	return
}

func handler(ctx context.Context, event events.LambdaFunctionURLRequest) (*events.LambdaFunctionURLResponse, error) {
	switch event.RawPath {
	case "/email/gmail":
		return sendGmailMessage(ctx, event)

	case "/email/outlook":
		return sendOutlookMessage(ctx, event)

	default:
		log.WarnNoSentry(ctx, "invalid request path")
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}, nil
	}
}

func sendGmailMessage(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (_ *events.LambdaFunctionURLResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "sendGmailMessage", nil)
	defer func() {
		// NOTE: Error logged here so it contains zap metadata (ENGB-2300)
		if err != nil {
			log.Error(ctx, "failed to send Gmail message", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	var payload Event
	if err := json.Unmarshal([]byte(event.Body), &payload); err != nil {
		log.Error(ctx, "sendGmailMessage: error unmarshaling json body", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	ctx = log.With(ctx, zap.String("userEmail", payload.EmailAddress), zap.Uint("genEmailID", payload.GenEmailID))

	user, err := getUserByEmail(ctx, payload.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "email lambda sendGmailMessage: user does not exist")
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "user not found"}, nil
		}

		return nil, fmt.Errorf("user '%s' lookup failed: %w", payload.EmailAddress, err)
	}

	if user.EmailAddress != payload.EmailAddress {
		return &events.LambdaFunctionURLResponse{
			StatusCode: http.StatusUnauthorized,
			Body:       "user not authorized",
		}, nil
	}

	generatedEmail, err := genEmailDB.GetEmail(ctx, payload.GenEmailID)
	if err != nil {
		log.Error(ctx, "error retrieving generated email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	if !isWithinCurrentHour(generatedEmail.ScheduleSend.Time) {
		log.Info(ctx, "schedule send time of generated email has changed",
			zap.Uint("generated_email_id", generatedEmail.ID),
			zap.String("sender_email_address", user.EmailAddress),
			zap.Uint("service_id", user.ServiceID))

		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusUnprocessableEntity}, err
	}

	client, err := gmailclient.New(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, &user)
	if err != nil {
		log.Error(ctx, "error creating Google client", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	signature, err := client.GetSignature(ctx, user.EmailAddress)
	if err != nil {
		log.Warn(ctx, "error getting user's signature, failing open", zap.Error(err))
	}

	generatedEmail.Body = strings.ReplaceAll(generatedEmail.Body, "\n", "<br>")

	_, err = client.SendMessage(ctx, generatedEmail, signature, []gmailclient.NewAttachment{})
	if err != nil {
		log.Error(ctx, "error sending generated Gmail email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	if err := genEmailDB.Update(ctx, generatedEmail); err != nil {
		log.Error(ctx, "error updating status of Gmail email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}, nil
}

func sendOutlookMessage(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (_ *events.LambdaFunctionURLResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "sendOutlookMessage", nil)
	defer func() {
		// NOTE: Error logged here so it contains zap metadata (ENGB-2300)
		if err != nil {
			log.Error(ctx, "failed to send Outlook message", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	var payload Event
	if err := json.Unmarshal([]byte(event.Body), &payload); err != nil {
		log.Error(ctx, "sendOutlookMessage: error unmarshaling json body", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}
	ctx = log.With(ctx, zap.String("userEmail", payload.EmailAddress), zap.Uint("genEmailID", payload.GenEmailID))

	generatedEmail, err := genEmailDB.GetEmail(ctx, payload.GenEmailID)
	if err != nil {
		log.Error(ctx, "error retrieving generated email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	user, err := getUserByEmail(ctx, payload.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "email lambda sendOutlookMessage: user does not exist")
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "user not found"}, nil
		}

		return nil, fmt.Errorf("user '%s' lookup failed: %w", payload.EmailAddress, err)
	}

	if !isWithinCurrentHour(generatedEmail.ScheduleSend.Time) {
		log.Info(ctx, "schedule send time of generated email has changed",
			zap.Uint("generated_email_id", generatedEmail.ID),
			zap.String("sender_email_address", user.EmailAddress),
			zap.Uint("service_id", user.ServiceID))

		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusUnprocessableEntity}, err
	}

	client, err := msclient.New(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		log.Error(ctx, "error creating Microsoft client", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	generatedEmail.Body = strings.ReplaceAll(generatedEmail.Body, "\n", "<br>")

	msg := msclient.Message{
		CreatedDateTime:      time.Now(),
		LastModifiedDateTime: time.Now(),
		// NOTE: Do not remove empty fields; MS throws an error
		HasAttachments:    false,
		InternetMessageID: "",
		WebLink:           "",
		Categories:        []string{},
		Subject:           generatedEmail.Subject,
		Body: &msclient.Body{
			ContentType: "html",
			Content:     generatedEmail.Body + "\r\n" + user.EmailSignature,
		},
		Sender: msclient.Sender{
			EmailAddress: msclient.EmailAddress{
				Address: user.EmailAddress,
			},
		},
		From: msclient.From{
			EmailAddress: msclient.EmailAddress{
				Address: user.EmailAddress,
			},
		},
		ToRecipients: []msclient.RecipientCollection{
			{
				EmailAddress: msclient.EmailAddress{Name: "", Address: generatedEmail.Recipients[0]},
			},
		},
		// cc, bcc, replyTo cannot be null
		CcRecipients:            make([]msclient.RecipientCollection, len(generatedEmail.CC)),
		BccRecipients:           []msclient.RecipientCollection{},
		ReplyTo:                 []msclient.RecipientCollection{},
		Importance:              "normal",
		InferenceClassification: "other",
		Flag:                    msclient.Flag{FlagStatus: "notFlagged"},
	}

	for i, ccAddress := range generatedEmail.CC {
		msg.CcRecipients[i] = msclient.RecipientCollection{
			EmailAddress: msclient.EmailAddress{Name: "", Address: ccAddress},
		}
	}

	if err := client.DraftMessage(ctx, &msg); err != nil {
		log.Error(ctx, "error drafting generated Outlook email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	log.Debug(ctx, "generated Outlook message ID", zap.String("msgID", msg.ID))

	if err = client.SendMessage(ctx, &msg); err != nil {
		log.Error(ctx, "error sending generated Outlook email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	generatedEmail.SentAt = models.NullTime{Time: time.Now(), Valid: true}
	generatedEmail.Status = models.SentStatus
	if err := genEmailDB.Update(ctx, generatedEmail); err != nil {
		log.Error(ctx, "error updating status of Outlook email", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}, err
	}

	return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}, nil
}

func isWithinCurrentHour(t time.Time) bool {
	now := time.Now()
	startOfHour := now.Truncate(time.Hour)
	endOfHour := startOfHour.Add(time.Hour)

	return t.After(startOfHour) && t.Before(endOfHour)
}
