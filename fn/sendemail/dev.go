package main

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
)

type (
	LocalEvent struct {
		Email       Event  `json:"email" validate:"required"`
		TriggerTime string `json:"triggerTime"`
	}
)

func runLocalServer() error {
	app := fiber.New()

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/email", fiberHandler)

	return app.Listen(devPort)
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	var body LocalEvent
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	log.Info(ctx, "sending event to beacon-send-email handler", zap.Any("event", body))

	var path string

	if body.Email.EmailProvider == "gmail" {
		path = "/email/gmail"
	}

	if body.Email.EmailProvider == "outlook" {
		path = "/email/outlook"
	}

	payload, err := json.Marshal(body.Email)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	event := events.LambdaFunctionURLRequest{
		Body:    string(payload),
		RawPath: path,
	}

	if body.TriggerTime != "" {
		triggerTime, err := time.Parse(time.RFC3339, body.TriggerTime)
		if err != nil {
			return c.Status(http.StatusBadRequest).SendString("invalid trigger time format")
		}

		now := time.Now()
		if triggerTime.After(now) {
			duration := triggerTime.Sub(now)
			log.Info(ctx,
				"waiting until trigger time",
				zap.Time("triggerTime", triggerTime),
				zap.Duration("duration", duration),
			)
			time.Sleep(duration)
		} else {
			log.Info(ctx, "trigger time is in the past, executing immediately")
		}
	} else {
		log.Info(ctx, "trigger time not provided, executing immediately")
	}

	result, err := handler(ctx, event)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(result.StatusCode).SendString(result.Body)
}
