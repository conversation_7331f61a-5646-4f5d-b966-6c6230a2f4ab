package email

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/common/util/oauth"
	"github.com/drumkitai/drumkit/fn/onprem/env"
	"github.com/drumkitai/drumkit/fn/onprem/routes/webhook"
)

type (
	IngestBody struct {
		ThreadID      string               `json:"threadId"`
		EmailProvider models.EmailProvider `json:"emailProvider"`
		UserID        uint                 `json:"userId"`
	}

	IngestResponse struct {
		Status string `json:"status"`
	}
)

func Ingest(c *fiber.Ctx) error {
	var body IngestBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(),
		zap.String("threadId", body.ThreadID),
		zap.String("emailProvider", string(body.EmailProvider)),
		zap.Uint("userId", body.UserID),
	)

	log.Info(ctx, "Receiving manual email ingestion request")

	user, err := onpremuser.GetByID(ctx, body.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("user not found")
		}

		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	threadEmails, err := emailDB.GetAllOnPremEmailsByThreadID(ctx, body.ThreadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("associated emails to thread not found")
		}

		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	processedEmails := make(map[string]bool, len(threadEmails))
	var processingErrors []error

	for _, threadEmail := range threadEmails {
		if processedEmails[threadEmail.ExternalID] {
			continue
		}

		msg, err := getMessage(ctx, body.EmailProvider, threadEmail.ExternalID, &user)
		if err != nil {
			log.Error(
				ctx,
				"failed to get message",
				zap.String("externalID", threadEmail.ExternalID),
				zap.Error(err),
			)
			processingErrors = append(processingErrors, err)
			continue
		}

		if err := processMessage(ctx, &user, msg); err != nil {
			log.Error(
				ctx,
				"failed to process message",
				zap.String("externalID", threadEmail.ExternalID),
				zap.Error(err),
			)
			processingErrors = append(processingErrors, err)
			continue
		}

		processedEmails[threadEmail.ExternalID] = true
	}

	if len(processingErrors) == len(threadEmails) {
		return c.Status(http.StatusInternalServerError).SendString("failed to process all emails")
	}

	return c.Status(http.StatusCreated).JSON(IngestResponse{
		Status: "ingested",
	})
}

func getMessage(
	ctx context.Context,
	provider models.EmailProvider,
	messageID string,
	user *models.OnPremUser,
) (emails.MessageInterface, error) {

	encryptionKey := []byte(env.Vars.EncryptionKey)

	switch provider {
	case models.OutlookEmailProvider:
		client, err := msclient.New(
			ctx,
			env.Vars.MicrosoftClientID,
			env.Vars.MicrosoftClientSecret,
			user,
			oauth.WithEncryptionKey(&encryptionKey),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create Outlook client: %w", err)
		}

		msg, err := client.GetMessageByID(
			ctx,
			messageID,
			msclient.WithContentType(msclient.HTMLContentType),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get Outlook message: %w", err)
		}

		return &emails.OutlookMessage{Message: &msg}, nil

	case models.GmailEmailProvider:
		client, err := gmailclient.New[models.UserAccessor](
			ctx,
			env.Vars.GoogleClientID,
			env.Vars.GoogleClientSecret,
			user,
			oauth.WithEncryptionKey(&encryptionKey),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create Gmail client: %w", err)
		}

		msg, err := client.GetMessage(ctx, messageID)
		if err != nil {
			return nil, fmt.Errorf("failed to get Gmail message: %w", err)
		}

		return &emails.GmailMessage{Message: msg}, nil

	default:
		return nil, fmt.Errorf("unsupported email provider: %s", provider)
	}
}

func processMessage(ctx context.Context, user *models.OnPremUser, msg emails.MessageInterface) error {
	email := models.OnPremEmail{
		Account:          user.EmailAddress,
		UserID:           user.ID,
		ExternalID:       msg.GetID(),
		RFCMessageID:     msg.GetRFCMessageID(),
		SentAt:           msg.GetInternalDate(),
		ThreadID:         msg.GetThreadID(),
		ThreadReferences: msg.GetThreadReferences(),
	}

	if err := emailDB.Create(ctx, &email); err != nil {
		return fmt.Errorf("failed to create email: %w", err)
	}

	payload, err := emails.PrepareEmailPayload(
		ctx,
		msg,
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithExternalUserID(user.ID),
		emails.WithExternalEmailID(email.ID),
		emails.WithOnPrem(true),
	)
	if err != nil {
		return fmt.Errorf("failed to prepare email payload: %w", err)
	}

	payload.Label = string(emails.TruckListLabel)

	if err = webhook.ForwardEmailToDrumkit(ctx, webhook.Payload{Email: payload}); err != nil {
		return fmt.Errorf("failed to forward email to drumkit: %w", err)
	}

	return nil
}
