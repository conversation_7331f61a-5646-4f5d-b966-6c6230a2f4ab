package user

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/oauth"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

type (
	SignupGoogleBody struct {
		api.GoogleAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}
)

func SignupGoogle(c *fiber.Ctx) error {
	var body SignupGoogleBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("hostedDomain", body.HostedDomain))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		ctx = log.With(ctx, zap.String("userEmail", body.DevEmail))

		if err := onpremuser.Create(ctx, &models.OnPremUser{
			EmailAddress:       body.DevEmail,
			GmailLastHistoryID: 1,
		}); err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}
		return c.Status(http.StatusCreated).SendString("user created successfully")
	}

	log.Info(ctx, "received google signup request")

	resp, err := api.CallBackFromGoogle(ctx, body.GoogleAuthCodeRequest)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx, zap.String("userEmail", resp.UserInfo.Email))

	email := resp.UserInfo.Email

	// Block signup requests from *@gmail.com, except Google's official oauth test account
	if strings.HasSuffix(email, "@gmail.com") && email != "<EMAIL>" {
		// Request is not from a GSuite account (block gmail.com signups)
		log.Error(ctx, "rejecting signup request from gmail account")

		return c.Status(http.StatusUnauthorized).SendString("gsuite domain required")
	}

	user := models.OnPremUser{
		EmailAddress:  resp.UserInfo.Email,
		EmailProvider: models.GmailEmailProvider,
		Name:          resp.UserInfo.Name,
		PortalDomain:  env.Vars.PortalDomain,
	}

	user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
		ctx,
		resp.AccessToken,
		resp.RefreshToken,
		[]byte(env.Vars.EncryptionKey),
	)
	if err != nil {
		log.Error(ctx, "gmail token encryption failed", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	user.TokenExpiry = resp.ExpTime

	userFound, err := onpremuser.GetByEmail(ctx, resp.UserInfo.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = userFound.ID

	if err := watchGmailInbox(ctx, &user, []byte(env.Vars.EncryptionKey)); err != nil {
		// Fail-open. If initial watch fails, manually trigger Lambda for the user
		log.Error(ctx, "watch gmail inbox failed", zap.Error(err))
	}

	if userFound.ID == 0 {
		err = onpremuser.Create(ctx, &user)
	} else {
		err = onpremuser.Update(ctx, user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	payload := SignupPayload{
		User:     user,
		DevEmail: body.DevEmail,
	}

	drumkitUserInfo, err := forwardSignedUpUser(ctx, payload)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(DrumkitSignupResponse{
		AccessToken:     drumkitUserInfo.AccessToken,
		Email:           drumkitUserInfo.Email,
		ServiceID:       drumkitUserInfo.ServiceID,
		GroupID:         drumkitUserInfo.GroupID,
		TokenExpiration: drumkitUserInfo.TokenExpiration,
		TokenType:       drumkitUserInfo.TokenType,
	})
}

func watchGmailInbox[T models.UserAccessor](ctx context.Context, user T, encryptionKey []byte) error {
	client, err := gmailclient.New(
		ctx,
		env.Vars.GoogleClientID,
		env.Vars.GoogleClientSecret,
		user,
		oauth.WithEncryptionKey(&encryptionKey),
	)
	if err != nil {
		return fmt.Errorf("error creating Gmail service for %s: %w", user.GetEmailAddress(), err)
	}

	req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}
	watchResp, err := client.WatchInbox(ctx, req)
	if err != nil {
		return fmt.Errorf("error watching %s inbox after onboarding: %w", user.GetEmailAddress(), err)
	}

	user.SetWebhookExpiration(time.UnixMilli(watchResp.Expiration))
	log.Info(ctx, "watch inbox successful", zap.Time("expiration", user.GetWebhookExpiration()))

	// Ignore current history ID returned in watchResp because:
	// 1) For new users, we backfill the last 14 days (see ingestion/main.go)
	// 2) For existing users, keep historical ID so we can process old emails

	return nil
}
