package webhook

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/util/oauth"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/onprem/env"
	helpers "github.com/drumkitai/drumkit/fn/onprem/util"
)

type (
	OutlookWebhookBody struct {
		Value []Value `json:"value"`
	}

	Value struct {
		SubscriptionID                 string       `json:"subscriptionId"`
		SubscriptionExpirationDateTime string       `json:"subscriptionExpirationDateTime"`
		ChangeType                     string       `json:"changeType"`
		Resource                       string       `json:"resource"`
		ResourceData                   ResourceData `json:"resourceData"`
		ClientState                    string       `json:"clientState"`
		TenantID                       string       `json:"tenantId"`
	}

	ResourceData struct {
		ODataType string `json:"@odata.type"`
		ODataID   string `json:"@odata.id"`
		ODataEtag string `json:"@odata.etag"`
		// Not immutable by default; add IdType="ImmutableId" to request header for immutable IDs.
		// https://learn.microsoft.com/en-us/graph/outlook-immutable-id
		ID string `json:"id"`
	}
)

var (
	// Unit tests can replace these functions
	getUserByOutlookIDs       = onpremuser.GetByOutlookIDs
	updateUser                = onpremuser.Update
	updateOutlookBackfillLock = onpremuser.UpdateBackfillLock
	dbGetEmailFunc            = emailDB.GetOnPremEmailByExternalID
	outlookConstructor        = msclient.New[models.UserAccessor]
)

func ProcessOutlookInboxWebhook(c *fiber.Ctx) error {
	// Handle Outlook subscription validation
	if token := c.Query("validationToken"); token != "" {
		log.Info(c.UserContext(), "received Outlook validation request - confirming subscription")

		return c.Status(http.StatusOK).SendString(token)
	}

	var body OutlookWebhookBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	if count := len(body.Value); count > 1 {
		log.Info(ctx, "webhook contains more than 1 item", zap.Int("count", count))
	}

	for _, v := range body.Value {
		if err := processMsg(ctx, v); err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	return c.SendStatus(http.StatusOK)
}

// TODO: Deduplicate this logic with Outlook ingestion handler
func processMsg(ctx context.Context, v Value) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "processMsgOutlook", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "failed to process webhook", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	userExternalID, err := extractUserID(v.Resource)
	if err != nil {
		return err
	}

	encryptionKey := []byte(env.Vars.EncryptionKey)

	user, err := getUserByOutlookIDs(ctx, userExternalID, v.ClientState)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// NotFound can occur if the user exists but not that subscriptionID,
			// meaning there are duplicate subs.
			stopErr := stopWatchingInbox(ctx, v.SubscriptionID, userExternalID, &encryptionKey)
			if stopErr == nil {
				log.Info(ctx, "deleted unknown subscription", zap.String("subID", v.SubscriptionID))

				return nil
			} else if !errors.Is(stopErr, gorm.ErrRecordNotFound) {
				// NOTE: Subscriptions are short-lived, so if this fails, it will expire by itself
				// in <3 days.
				log.WarnNoSentry(ctx, "error canceling subscription", zap.Error(stopErr))
			}
		}

		return fmt.Errorf("unable to find user with externalID %s and clientState %s: %w",
			userExternalID, v.ClientState, err)
	}

	ctx = log.With(
		ctx,
		zap.String("emailAddress", user.EmailAddress),
		zap.String("resource", v.Resource),
		zap.String("emailProvider", "outlook"),
	)
	sentry.SetUser(ctx, &user)

	client, err := outlookConstructor(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		&user,
		oauth.WithEncryptionKey(&encryptionKey),
	)
	if err != nil {
		return err
	}

	// Subscriptions are short-lived; we must re-subscribe
	if hoursLeft := time.Until(user.WebhookExpiration).Hours(); hoursLeft < 24 {
		rewatchInbox(ctx, &user, client)
	}

	msgs := make([]msclient.Message, 1)
	msgs[0], err = client.GetMessageByID(
		ctx,
		v.ResourceData.ID,
		msclient.WithContentType(msclient.HTMLContentType),
	)
	if err != nil {
		return fmt.Errorf("client.ByMessageId.Get(%s) failed: %w", v.ResourceData.ID, err)
	}

	backfillMsgs, err := getMsgsToBackfill(ctx, &user, client)
	// keep track of if we're running a backfill or not, for later processing logic
	backfilling := false
	if err != nil {
		log.Error(ctx, "backfill error", zap.Error(err))
	} else if len(backfillMsgs) > 0 {
		backfilling = true
		// Unlike Gmail, if there's a backfill already in progress, we should still process the new message
		// in this new webhook as the backfill's L14D query may not have included it & Outlook won't retry
		msgs = append(backfillMsgs, msgs...)

		defer func() {
			log.Info(ctx, "removing backfill lock")

			if err = updateOutlookBackfillLock(ctx, &user, models.NullTime{}); err != nil {
				log.Error(ctx, "error removing DB backfill lock", zap.Error(err))
			}
		}()
	}

	log.Info(ctx, "processing messages", zap.Int("count", len(msgs)))

	for _, msg := range msgs {
		// Add filter check before processing
		if !msclient.ShouldProcessMessage(ctx, msg, user.EmailAddress) {
			continue
		}

		if env.Vars.SkipDuplicates {
			_, err = dbGetEmailFunc(ctx, msg.ID)
			if err == nil {
				log.Info(ctx, "skipping duplicate message", zap.String("duplicateMsg", msg.ID))
				continue
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.ErrorNoSentry(ctx, "error querying email", zap.Error(err))
			}
		}

		v := msg
		outlookMsg := emails.OutlookMessage{Message: &v}

		email := models.OnPremEmail{
			Account:          user.EmailAddress,
			UserID:           user.ID,
			ExternalID:       outlookMsg.GetID(),
			RFCMessageID:     outlookMsg.GetRFCMessageID(),
			SentAt:           outlookMsg.GetInternalDate(),
			ThreadID:         outlookMsg.GetThreadID(),
			ThreadReferences: outlookMsg.GetThreadReferences(),
		}
		err := emailDB.Create(ctx, &email)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to store email in DB",
				zap.Error(err),
				zap.String("msgId", outlookMsg.GetID()),
			)
		}

		payload, err := emails.PrepareEmailPayload(
			ctx,
			&outlookMsg,
			emails.WithEmailAddress(user.EmailAddress),
			emails.WithExternalUserID(user.ID),
			emails.WithExternalEmailID(email.ID),
			emails.WithOnPrem(true),
		)
		if err != nil {
			log.Error(
				ctx,
				"failed to prepare email payload to send to drumkit",
				zap.Uint("externalEmailId", email.ID),
				zap.Error(err),
			)
		}

		// Use the full email body (including inline replies) when classifying
		labels, approach, _, _ := emails.Classify(ctx, *payload)
		log.Info(ctx, "email labels", zap.Strings("labels", labels))
		// TODO: the customer should be able to specify the email labels via an environment variable
		// that are okay to forward to drumkit
		if slices.Contains(labels, string(emails.TruckListLabel)) {
			payload.Label = string(emails.TruckListLabel)
			payload.ClassificationMethod = approach

			err := ForwardEmailToDrumkit(ctx, Payload{Email: payload})
			if err != nil {
				return err
			}
		}

		// when backfilling only, sleep for 100 milliseconds after each iteration to slow down processing
		if backfilling {
			time.Sleep(100 * time.Millisecond)
		}
	}

	// if backfilling, log complete msg for easy searching in cloudwatch
	if backfilling {
		log.Info(ctx, fmt.Sprintf("backfill complete, processed %d msgs from user", len(msgs)))
	}

	return nil
}

func extractUserID(input string) (string, error) {
	parts := strings.Split(input, "/")

	var userID string
	found := false

	for i, part := range parts {
		if strings.EqualFold(part, "Users") && i+1 < len(parts) {
			userID = parts[i+1]
			found = true
			break
		}
	}

	if !found {
		return "", fmt.Errorf("userID not found in the input string")
	}

	return userID, nil
}

func rewatchInbox(ctx context.Context, user *models.OnPremUser, client msclient.Client) {
	updatedSub, err := client.RewatchInbox(ctx, "", user)
	if err != nil {
		log.Warn(ctx, "error re-subscribing to Outlook inbox", zap.Error(err))
		return
	}
	log.Info(ctx, fmt.Sprint("successfully re-subscribed to inbox until ", updatedSub.ExpirationDateTime))

	user.WebhookExpiration = updatedSub.ExpirationDateTime
	if err = updateUser(ctx, *user); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(ctx, "error updating subscription expiration", zap.Error(err))
	}

	payload := UpdatedUserPayload{
		ID:                user.ID,
		EmailAddress:      user.EmailAddress,
		WebhookExpiration: user.WebhookExpiration,
	}

	if err := forwardUpdatedUserToDrumkit(ctx, payload); err != nil {
		log.WarnNoSentry(ctx, "error updating user's webhook expiration in drumkit", zap.Error(err))
	}
}

func stopWatchingInbox(ctx context.Context, subID, userExternalID string, encryptionKey *[]byte) error {
	user, err := onpremuser.GetByExternalID(ctx, userExternalID)
	if err != nil {
		return err
	}

	client, err := outlookConstructor(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		&user,
		oauth.WithEncryptionKey(encryptionKey),
	)
	if err != nil {
		return err
	}

	return client.StopWatchingInbox(ctx, subID)
}

func getMsgsToBackfill(
	ctx context.Context,
	user *models.OnPremUser,
	client msclient.Client,
) (msgs []msclient.Message, err error) {

	isNew := user.CreatedAt.Equal(user.UpdatedAt)
	isStale := time.Since(user.UpdatedAt).Hours() > 72

	if !isNew && !isStale {
		return msgs, nil
	}

	if !helpers.ShouldBackfill(ctx, *user) {
		// Backfill already in progress; process just the 1 message from the webhook
		log.Info(ctx, fmt.Sprintf("backfill in progress since %s", user.BackfillStartTime.Time))
		return msgs, nil
	}

	if isNew {
		log.Info(ctx, "backfilling new user")
	} else {
		log.Info(ctx, "backfilling stale user", zap.Any("time_since_update", time.Since(user.UpdatedAt).Hours()))
	}

	backfillStartTime := models.NullTime{Time: time.Now(), Valid: true}
	if err = updateOutlookBackfillLock(ctx, user, backfillStartTime); err != nil {
		return msgs, fmt.Errorf("error acquiring DB lock to backfill: %w", err)
	}

	msgs, err = client.ListMessagesAfterDate(ctx, time.Now().Add(-time.Duration(env.Vars.BackfillHours)*time.Hour))
	if err != nil {
		return msgs, fmt.Errorf("error getting msgs from last %d hours: %w", env.Vars.BackfillHours, err)
	}

	return msgs, nil
}
