package webhook

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

type (
	Payload struct {
		Email *models.IngestedEmail
	}

	UpdatedUserPayload struct {
		ID                uint      `json:"id"`
		EmailAddress      string    `json:"emailAddress"`
		WebhookExpiration time.Time `json:"webhookExpiration"`
	}
)

func ForwardEmailToDrumkit(ctx context.Context, payload Payload) error {
	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(
			ctx,
			"failed to json marshal email payload to send to drumkit",
			zap.Uint("externalEmailId", payload.Email.ExternalEmailID),
			zap.Error(err),
		)

		return err
	}

	webhookURL := env.Vars.DrumkitProcessorURL + "/onprem/email"

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		webhookURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send to drumkit",
			zap.Uint("externalEmailId", payload.Email.ExternalEmailID),
			zap.Error(err),
		)

		return err
	}

	generatedAccessToken, err := jwt.NewAccessToken(
		payload.Email.Account,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithIsOnPrem(true),
		jwt.WithIssuer(env.Vars.Domain),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt for drumkit requests failed", zap.Error(err))
		return err
	}

	headers := map[string]string{
		"Accept":        "application/json",
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + generatedAccessToken,
	}

	//nolint:bodyclose // False positive; body already closed
	respBody, resp, err := httputil.PostBytesWithToken(
		ctx,
		models.Integration{Name: "Drumkit-OnPrem"},
		*req.URL,
		payload,
		headers,
		nil,
		"",
	)

	if err != nil {
		log.Error(
			ctx,
			"failed to send http request to drumkit",
			zap.Uint("externalEmailId", payload.Email.ExternalEmailID),
			zap.Error(err),
		)

		return err
	}

	if resp.StatusCode >= http.StatusBadRequest {
		log.Error(
			ctx,
			"drumkit processor returned error",
			zap.Uint("externalEmailId", payload.Email.ExternalEmailID),
			zap.String("responseBody", string(respBody)),
			zap.Int("responseStatus", resp.StatusCode),
			zap.String("drumkitProcessorURL", req.URL.String()),
		)

		return fmt.Errorf("drumkit processor returned error")
	}

	log.Info(
		ctx,
		"successfully forwarded email to drumkit",
		zap.Uint("externalEmailId", payload.Email.ExternalEmailID),
		zap.String("responseBody", string(respBody)),
		zap.Int("responseStatus", resp.StatusCode),
		zap.String("drumkitProcessorURL", req.URL.String()),
	)

	return nil
}

func forwardUpdatedUserToDrumkit(ctx context.Context, payload UpdatedUserPayload) error {
	updatedUserURL := env.Vars.DrumkitAPIURL + "/onprem/user/update"

	ctx = log.With(
		ctx,
		zap.Uint("userID", payload.ID),
		zap.String("emailAddress", payload.EmailAddress),
		zap.String("updatedUserURL", updatedUserURL),
	)

	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(ctx, "failed to json marshal updated user payload to send to drumkit", zap.Error(err))

		return err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		updatedUserURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Error(ctx, "failed to construct http request to send to drumkit", zap.Error(err))

		return err
	}

	generatedAccessToken, err := jwt.NewAccessToken(
		payload.EmailAddress,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithIsOnPrem(true),
		jwt.WithIssuer(env.Vars.Domain),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt for drumkit request to update user failed", zap.Error(err))
		return err
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+generatedAccessToken)

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(ctx, "failed to send http request to update user to drumkit", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= http.StatusBadRequest {
		log.Error(ctx, "failed to forward updated user to drumkit", zap.Int("responseStatus", resp.StatusCode))
		return fmt.Errorf("failed to update onprem user in drumkit")
	}

	log.Info(ctx, "successfully forwarded email to drumkit", zap.Int("responseStatus", resp.StatusCode))
	return nil
}
