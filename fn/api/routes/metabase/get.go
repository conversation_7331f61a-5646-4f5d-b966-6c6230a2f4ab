package metabase

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	metabaseDashboardDB "github.com/drumkitai/drumkit/common/rds/metabase_dashboard"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetDashboardsPath struct {
		ServiceID uint `path:"serviceId" validate:"required"`
	}

	GetChartPath struct {
		ID        uint `path:"id" validate:"required"`
		ServiceID uint `path:"serviceId" validate:"required"`
	}
)

// GetDashboards retrieves all Metabase dashboards for a service
func GetDashboards(c *fiber.Ctx) error {
	var path GetDashboardsPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	userServiceID := middleware.ServiceIDFromContext(c)
	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	_, err := rds.GetServiceByID(ctx, path.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).
				SendString(fmt.Sprintf("service with ID %d not found", path.ServiceID))
		}

		log.Error(ctx, "error fetching service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	dateRangeStr := c.Query("dateRange")
	var dateRange DateRange
	if dateRangeStr != "" {
		if err := json.Unmarshal([]byte(dateRangeStr), &dateRange); err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid dateRange query param",
				zap.String("value", dateRangeStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid dateRange query parameter")
		}
	}

	var userID uint

	userIDStr := c.Query("userId")
	if userIDStr != "" {
		parsedID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid userId query param",
				zap.String("userID", userIDStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid userId query parameter")
		}

		userID = uint(parsedID)
	}

	var customerID uint
	customerIDStr := c.Query("customerId")

	if customerIDStr != "" {
		parsedID, err := strconv.ParseUint(customerIDStr, 10, 32)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid customerId query param",
				zap.String("customerID", customerIDStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid customerId query parameter")
		}

		customerID = uint(parsedID)
	}

	var userName string
	if userID != 0 {
		user, err := userDB.GetByID(ctx, userID)
		if err != nil {
			log.Error(ctx, "error fetching user", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		userName = user.Name
	}

	var customerName string
	if customerID != 0 {
		customer, err := tmsCustomerDB.GetCustomerNameByID(ctx, customerID)
		if err != nil {
			log.Error(ctx, "error fetching customer", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		customerName = customer
	}

	dashboards, err := metabaseDashboardDB.GetByServiceID(ctx, path.ServiceID)
	if err != nil {
		log.Error(ctx, "error fetching dashboards", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	response := DashboardsResponse{
		Dashboards: make([]DashboardResponse, len(dashboards)),
	}

	for i, dashboard := range dashboards {
		iframeURL := generateIFrameURL(
			ctx,
			dashboard.DashboardID,
			dashboard.QuestionID,
			dateRange,
			userName,
			customerName,
		)

		response.Dashboards[i] = DashboardResponse{
			ID:          dashboard.ID,
			URL:         dashboard.URL,
			QuestionID:  dashboard.QuestionID,
			DashboardID: dashboard.DashboardID,
			UUID:        dashboard.UUID,
			Name:        dashboard.Name,
			Description: dashboard.Description,
			ServiceID:   dashboard.ServiceID,
			UserID:      dashboard.UserID,
			CreatedAt:   dashboard.CreatedAt.String(),
			UpdatedAt:   dashboard.UpdatedAt.String(),
			IFrameURL:   iframeURL,
		}
	}

	return c.Status(http.StatusOK).JSON(response)
}

func GetDashboardByID(c *fiber.Ctx) error {
	var path GetChartPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	userServiceID := middleware.ServiceIDFromContext(c)
	if userServiceID != path.ServiceID {
		return c.SendStatus(http.StatusForbidden)
	}

	var dateRange DateRange

	dateRangeStr := c.Query("dateRange")
	if dateRangeStr != "" {
		if err := json.Unmarshal([]byte(dateRangeStr), &dateRange); err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid dateRange query param",
				zap.String("dateRange", dateRangeStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid dateRange query parameter")
		}
	}

	var userID uint

	userIDStr := c.Query("userId")
	if userIDStr != "" {
		parsedID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid userId query param",
				zap.String("userID", userIDStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid userId query parameter")
		}

		userID = uint(parsedID)
	}

	var customerID uint

	customerIDStr := c.Query("customerId")
	if customerIDStr != "" {
		parsedID, err := strconv.ParseUint(customerIDStr, 10, 32)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"invalid customerId query param",
				zap.String("customerID", customerIDStr),
				zap.Error(err),
			)

			return c.Status(http.StatusBadRequest).SendString("invalid customerId query parameter")
		}
		customerID = uint(parsedID)
	}

	var userName string
	if userID != 0 {
		user, err := userDB.GetByID(ctx, userID)
		if err != nil {
			log.Error(ctx, "error fetching user", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		userName = user.Name
	}

	var customerName string
	if customerID != 0 {
		customer, err := tmsCustomerDB.GetCustomerNameByID(ctx, customerID)
		if err != nil {
			log.Error(ctx, "error fetching customer", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		customerName = customer
	}

	dashboard, err := metabaseDashboardDB.GetByID(ctx, path.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).
				SendString(fmt.Sprintf("dashboard ID %d not found", path.ID))
		}

		log.Error(ctx, "error fetching dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if dashboard.ServiceID != userServiceID {
		return c.SendStatus(http.StatusForbidden)
	}

	iframeURL := generateIFrameURL(
		ctx,
		dashboard.DashboardID,
		dashboard.QuestionID,
		dateRange,
		userName,
		customerName,
	)

	response := DashboardResponse{
		ID:          dashboard.ID,
		URL:         dashboard.URL,
		QuestionID:  dashboard.QuestionID,
		DashboardID: dashboard.DashboardID,
		UUID:        dashboard.UUID,
		Name:        dashboard.Name,
		Description: dashboard.Description,
		ServiceID:   dashboard.ServiceID,
		UserID:      dashboard.UserID,
		CreatedAt:   dashboard.CreatedAt.String(),
		UpdatedAt:   dashboard.UpdatedAt.String(),
		IFrameURL:   iframeURL,
	}

	return c.Status(http.StatusOK).JSON(response)
}
