package metabase

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	metabaseDashboardDB "github.com/drumkitai/drumkit/common/rds/metabase_dashboard"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	UpdateDashboardPath struct {
		DashboardID uint `path:"dashboardId" validate:"required"`
	}

	UpdateDashboardBody struct {
		URL         string `json:"url"`
		QuestionID  int    `json:"questionId"`
		DashboardID int    `json:"dashboardId"`
		Name        string `json:"name"`
		Description string `json:"description"`
		UserID      *uint  `json:"userId"`
	}
)

// AdminUpdateDashboard updates an existing Metabase dashboard
func AdminUpdateDashboard(c *fiber.Ctx) error {
	var path UpdateDashboardPath
	var body UpdateDashboardBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path), zap.Any("requestBody", body))

	// Only allow Drumkit admins to update dashboards
	email := middleware.ClaimsFromContext(c).Email
	if err := isDrumkitAdmin(email); err != nil {
		log.WarnNoSentry(ctx, "unauthorized: non-drumkit user trying to update dashboard")
		return c.SendStatus(http.StatusUnauthorized)
	}

	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", email))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	serviceID := middleware.ServiceIDFromContext(c)
	if user.ServiceID != serviceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			serviceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	dashboard, err := metabaseDashboardDB.GetByID(ctx, path.DashboardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).
				SendString(fmt.Sprintf("dashboard ID %d not found", path.DashboardID))
		}

		log.Error(ctx, "error fetching dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if user.ServiceID != dashboard.ServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match dashboard service ID %d",
			user.ServiceID,
			dashboard.ServiceID,
		)

		return c.SendStatus(http.StatusForbidden)
	}

	if body.URL != "" {
		dashboard.URL = body.URL
		uuid := extractUUIDFromURL(body.URL)
		if uuid == "" {
			return c.Status(http.StatusBadRequest).SendString("invalid Metabase dashboard URL format")
		}
		dashboard.UUID = uuid
	}

	if body.Name != "" {
		dashboard.Name = body.Name
	}

	if body.QuestionID != 0 {
		dashboard.QuestionID = body.QuestionID
	}

	if body.DashboardID != 0 {
		dashboard.DashboardID = body.DashboardID
	}

	if body.Description != "" {
		dashboard.Description = body.Description
	}

	if body.UserID != nil {
		dashboard.UserID = body.UserID
	}

	if err := metabaseDashboardDB.Update(ctx, &dashboard); err != nil {
		log.Error(ctx, "error updating dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(DashboardResponse{
		ID:          dashboard.ID,
		URL:         dashboard.URL,
		UUID:        dashboard.UUID,
		Name:        dashboard.Name,
		Description: dashboard.Description,
		ServiceID:   dashboard.ServiceID,
		UserID:      dashboard.UserID,
		QuestionID:  dashboard.QuestionID,
		DashboardID: dashboard.DashboardID,
		CreatedAt:   dashboard.CreatedAt.String(),
		UpdatedAt:   dashboard.UpdatedAt.String(),
	})
}
