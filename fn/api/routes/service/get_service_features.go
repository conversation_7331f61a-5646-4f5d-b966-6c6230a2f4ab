package service

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/config"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	UserConfigurations struct {
		DefaultPriceMargin     *float64                       `json:"defaultPriceMargin"`
		DefaultPriceMarginType *models.DefaultPriceMarginType `json:"defaultPriceMarginType"`
	}

	GetServiceFeaturesPath struct {
		ServiceID uint `json:"service_id"`
	}

	GetServiceFeaturesResponse struct {
		ID                    uint                     `json:"id"`
		TMSIntegrations       []MiniIntegration        `json:"tmsIntegrations"`
		QuotingIntegrations   []MiniIntegration        `json:"quotingIntegrations"`
		SchedulerIntegrations []MiniIntegration        `json:"schedulerIntegrations"`
		Configurations        UserConfigurations       `json:"configurations"`
		QuickQuoteConfig      *models.QuickQuoteConfig `json:"quickQuoteConfig"`
		models.FeatureFlags
		CarrierQuoteConfig *models.CarrierQuoteConfigOptions `json:"carrierQuoteConfig,omitempty"`
	}

	MiniIntegration struct {
		ID     uint                   `json:"id"`
		Name   models.IntegrationName `json:"name"`
		Tenant string                 `json:"tenant"`
	}
)

func GetServiceFeatures(c *fiber.Ctx) error {
	var path GetServiceFeaturesPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		log.Error(
			c.UserContext(),
			fmt.Sprintf("error validating service id %d as param for fetching feature flags", path.ServiceID),
			zap.Error(err),
		)
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	// Preload for access to QuickQuoteConfig
	service, err := rds.GetServiceWithPreload(ctx, path.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(
				ctx,
				fmt.Sprintf("couldn't find service with id %d to fetch its feature flags", path.ServiceID),
				zap.Error(err),
			)
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("service with id %d not found", path.ServiceID),
			)
		}
		log.Error(ctx, "serviceDB query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)
	if userServiceID != service.ID {
		return c.SendStatus(http.StatusForbidden)
	}

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	dbUpdateNeeded := false
	if !service.IsLoadViewEnabled {
		shouldEnable := shouldEnableLoadView(service)
		if shouldEnable != service.IsLoadViewEnabled {
			log.WarnNoSentry(ctx, "load view disabled but load view tabs enabled, correcting")

			service.IsLoadViewEnabled = shouldEnable
			dbUpdateNeeded = true
		}

	}

	if !service.IsQuoteViewEnabled {
		shouldEnable := shouldEnableQuoteView(service)
		if shouldEnable != service.IsQuoteViewEnabled {
			log.WarnNoSentry(ctx, "quote view disabled but quote view tabs enabled, correcting")

			service.IsQuoteViewEnabled = shouldEnable
			dbUpdateNeeded = true
		}
	}

	if dbUpdateNeeded {
		if err := rds.Update(ctx, &service); err != nil {
			log.WarnNoSentry(ctx, "error updating service in DB with corrected flags", zap.Error(err))
		}
	}

	// Optimization; only fetch TMS integrations if needed (e.g. if load building or quote submission is enabled)
	// TODO: Cache in FE
	var miniTMSes []MiniIntegration
	if service.IsLoadBuildingEnabled || service.IsTMSQuoteSubmissionEnabled ||
		service.IsQuickQuoteEnabled || service.IsCarrierNetworkQuotingEnabled || service.IsTMSLaneHistoryEnabled {
		tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting TMS integrations", zap.Error(err))
		}
		for _, i := range tmsIntegrations {
			miniTMSes = append(miniTMSes, MiniIntegration{ID: i.ID, Name: i.Name, Tenant: i.Tenant})
		}
	}

	miniQuotingIntegrations := []MiniIntegration{}
	if service.IsQuickQuoteEnabled || service.IsCarrierNetworkQuotingEnabled || service.IsQuoteLaneHistoryEnabled {
		quotingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting pricing integrations", zap.Error(err))
		}
		for _, i := range quotingIntegrations {
			miniQuotingIntegrations = append(miniQuotingIntegrations, MiniIntegration{ID: i.ID, Name: i.Name})
		}
	}

	miniSchedulerIntegrations := []MiniIntegration{}
	if service.IsAppointmentSchedulingEnabled {
		schedulerIntegrations, err := integrationDB.GetAllSchedulerByServiceIDAndUserID(ctx, userID, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting pricing integrations", zap.Error(err))
		}
		for _, i := range schedulerIntegrations {
			miniSchedulerIntegrations = append(miniSchedulerIntegrations, MiniIntegration{ID: i.ID, Name: i.Name})
		}
	}

	var carrierQuoteConfig *models.CarrierQuoteConfigOptions
	if service.IsCarrierNetworkQuotingEnabled {
		cqc, err := config.GetCarrierQuoteConfigByServiceID(ctx, service.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting CarrierQuoteConfig",
				zap.Uint("serviceID", service.ID),
				zap.Error(err),
			)
		} else {
			carrierQuoteConfig = &cqc.CarrierQuoteConfigOptions
		}
	}

	return c.Status(http.StatusOK).JSON(GetServiceFeaturesResponse{
		ID:                  service.ID,
		FeatureFlags:        service.FeatureFlags,
		TMSIntegrations:     miniTMSes,
		QuotingIntegrations: miniQuotingIntegrations,
		Configurations: UserConfigurations{
			DefaultPriceMargin:     user.DefaultPriceMargin,
			DefaultPriceMarginType: user.DefaultPriceMarginType,
		},
		SchedulerIntegrations: miniSchedulerIntegrations,
		QuickQuoteConfig:      service.QuickQuoteConfig,
		CarrierQuoteConfig:    carrierQuoteConfig,
	})
}

func shouldEnableLoadView(service models.Service) bool {
	return service.IsAppointmentSchedulingEnabled ||
		service.IsTrackAndTraceEnabled ||
		service.IsCarrierVerificationEnabled
}

func shouldEnableQuoteView(service models.Service) bool {
	return service.IsQuickQuoteEnabled ||
		service.IsCarrierNetworkQuotingEnabled ||
		service.IsLoadBuildingEnabled ||
		service.IsTruckListEnabled
}
