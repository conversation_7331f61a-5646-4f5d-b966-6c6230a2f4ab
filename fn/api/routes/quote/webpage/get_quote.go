package quotepublic

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/integrations/crm/hubspot"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/fn/api/env"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	QuickQuotePath struct {
		Nickname string `json:"nickname"`
	}

	QuickQuoteBody struct {
		// Contact Info
		FirstName   string `json:"firstName" validate:"required"`
		LastName    string `json:"lastName" validate:"required"`
		CompanyName string `json:"companyName" validate:"required"`
		// e.g. (1)6174681234; only US and CAN rn
		Phone string `json:"phone" validate:"required,numeric,min=10,max=11"`
		Email string `json:"email" validate:"required,email"`

		// Load info
		TransportType models.TransportType              `json:"transportType" validate:"oneof=VAN REEFER FLATBED"`
		Stops         []greenscreens.RatePredictionStop `json:"stops" validate:"min=2,dive"`
		PickupDate    time.Time                         `json:"pickupDate" validate:"required"`
		DeliveryDate  time.Time                         `json:"deliveryDate" validate:"required"`
		WeightLbs     float64                           `json:"weightLbs" validate:"omitempty"` // cargo weight
		Commodity     string                            `json:"commodity" validate:"omitempty"`
		// Currency currently hard-coded to USD
		// Currency      string                            `json:"currency" validate:"oneof=CAD USD"`
		// Fake, hidden field to prevent bot spam that circumvent email domain check (i.e. a competitor wrote a script)
		DOB string `json:"dob"`
	}

	QuickQuoteResponse struct {
		ID                uint                              `json:"id"`
		Stops             []greenscreens.RatePredictionStop `json:"stops"`
		MinTargetSellCost float64                           `json:"minTargetSellCost"`
		MaxTargetSellCost float64                           `json:"maxTargetSellCost"`
		Currency          string                            `json:"currency"`
	}
)

func GetQuickQuote(c *fiber.Ctx) error {
	var path QuickQuotePath
	var body QuickQuoteBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))

	if body.DOB != "" {
		return c.SendStatus(http.StatusBadRequest)
	}

	if isInvalidDomain(body.Email) {
		return c.Status(http.StatusBadRequest).SendString("Invalid email.")
	}

	service, err := rds.GetServiceByNicknameWithPreload(ctx, path.Nickname)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusUnauthorized).SendString(
				fmt.Sprintf("service %s does not exist", path.Nickname))
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := getQuickQuote(ctx, &body, service)
	if err != nil {
		if errors.Is(err, quote.ErrNoConfidentQuote) {
			log.WarnNoSentry(ctx, err.Error())
			return c.Status(http.StatusUnprocessableEntity).
				SendString(err.Error())
		}
		log.Error(ctx, "getQuickQuote failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

// The quick quote is a min-max range determined by the lower target buy rate of either the lane rate
// prediction or network lane rate prediction IFF the confidence level is above 75. The minimum of the range
// is the target buy rate + 7% markup and the maximum of the range is the target buy rate + 10% markup.
func getQuickQuote(
	ctx context.Context,
	body *QuickQuoteBody,
	service models.Service,
) (*QuickQuoteResponse, error) {

	var targetSellCost, minTargetSellCost, maxTargetSellCost float64

	integrations, err := integrationDB.GetPricingByServiceID(ctx, service.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching pricing integration: %w", err)
	}

	var integration models.Integration
	for _, i := range integrations {
		if i.Name == models.Greenscreens {
			integration = i
			break
		}
	}

	if integration.ID == 0 {
		return nil, fmt.Errorf("no Greenscreens integration found for service")
	}

	var stops []models.Stop
	for i, item := range body.Stops {
		stop := models.Stop{
			Order: i,
			Address: models.Address{
				Country: "US",
				State:   item.State,
				City:    item.City,
				Zip:     item.Zip,
			},
		}

		if item.City == "" || item.State == "" {
			zipResp, err := util.LookupCityStateByZipcode(ctx, item.Zip, env.Vars.USPSUserID)
			if err != nil {
				return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
			}
			stop.Address.State = zipResp.ZipCode.State
			stop.Address.City = zipResp.ZipCode.City
		}

		stops = append(stops, stop)
	}

	laneRatePrediction, networkLaneRatePrediction, ratePredictionStops, err := quote.
		FetchGreenscreensInfo(ctx, service, integration, stops, body.Email, body.TransportType, body.PickupDate)
	if err != nil {
		return nil, err
	}

	// Always submit to HubSpot for Wicker Park's record keeping
	defer func() {
		if env.Vars.AppEnv != "prod" {
			log.Info(ctx, "skipping Hubspot lead submission for non-prod env")
			return
		}
		hubSpotErr := submitHubspotForm(ctx, service.ID, body,
			laneRatePrediction, networkLaneRatePrediction,
			&targetSellCost, &minTargetSellCost, &maxTargetSellCost)

		if hubSpotErr != nil {
			// Fail-open
			log.Error(ctx, "hubspot error", zap.Error(hubSpotErr))
		}
	}()

	selectedRatePrediction, _ := quote.GetGSMostConfidentQuote(laneRatePrediction, networkLaneRatePrediction)

	targetSellCost = selectedRatePrediction.TargetBuyRate * selectedRatePrediction.Distance
	minTargetSellCost = 1.07 * targetSellCost
	maxTargetSellCost = 1.10 * targetSellCost

	quoteRecord := quote.CreateQuoteRecord(
		ctx,
		service,
		0,
		0,
		"",
		0,
		quote.ParseGSStops(body.Stops),
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		*selectedRatePrediction,
		targetSellCost,
		minTargetSellCost,
		maxTargetSellCost,
		models.GreenscreensSource,
		models.GSMostConfidentType,
		nil,
	)

	return &QuickQuoteResponse{
		ID:                quoteRecord.ID,
		Stops:             ratePredictionStops,
		Currency:          "USD", // This is hardcoded when creating DB quote record
		MinTargetSellCost: minTargetSellCost,
		MaxTargetSellCost: maxTargetSellCost,
	}, nil
}

func submitHubspotForm(
	ctx context.Context,
	serviceID uint,
	body *QuickQuoteBody,
	lanePred, networkPred *greenscreens.RatePredictionDetail,
	targetSellCost, minTargetSellCost, maxTargetSellCost *float64,
) error {
	integration, err := integrationDB.GetByName(ctx, serviceID, models.HubSpot)
	if err != nil {
		return fmt.Errorf("integrationDB query error: %w", err)
	}

	laneRateQuote := lanePred.TargetBuyRate * lanePred.Distance
	networkLaneRateQuote := networkPred.TargetBuyRate * networkPred.Distance

	hubspotForm := &hubspot.FormSubmission{
		Fields: []hubspot.Field{
			{ObjectTypeID: "0-1", Name: "firstname", Value: body.FirstName},
			{ObjectTypeID: "0-1", Name: "lastname", Value: body.LastName},
			{ObjectTypeID: "0-1", Name: "email", Value: body.Email},
			{ObjectTypeID: "0-1", Name: "phone", Value: body.Phone},
			{ObjectTypeID: "0-1", Name: "company", Value: body.CompanyName},
			{ObjectTypeID: "0-1", Name: "transport_type", Value: string(body.TransportType)},
			{ObjectTypeID: "0-1", Name: "currency", Value: "USD"},
			{ObjectTypeID: "0-1", Name: "pickup_zip_code", Value: body.Stops[0].Zip},
			{ObjectTypeID: "0-1", Name: "pickup_date", Value: body.PickupDate.Format(time.DateOnly)},
			{ObjectTypeID: "0-1", Name: "dropoff_zip_code",
				Value: body.Stops[len(body.Stops)-1].Zip},
			{ObjectTypeID: "0-1", Name: "dropoff_date", Value: body.DeliveryDate.Format(time.DateOnly)},
			{ObjectTypeID: "0-1", Name: "lane_rate_quote",
				Value: strconv.FormatFloat(laneRateQuote, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "gs_lane_quote_cl",
				Value: strconv.FormatFloat(float64(lanePred.ConfidenceLevel)/100, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "gs_network_rate",
				Value: strconv.FormatFloat(networkLaneRateQuote, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "gs_network_quote_cl",
				Value: strconv.FormatFloat(float64(networkPred.ConfidenceLevel)/100, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "gs_target_rate",
				Value: strconv.FormatFloat(*targetSellCost, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "min_target_sell",
				Value: strconv.FormatFloat(*minTargetSellCost, 'f', -1, 64)},
			{ObjectTypeID: "0-1", Name: "max_target_sell",
				Value: strconv.FormatFloat(*maxTargetSellCost, 'f', -1, 64)},
		}}

	hubspotClient := hubspot.New(ctx, integration)
	// IDs retrieved from https://shorturl.at/oJX34 -> Share -> Embed
	_, err = hubspotClient.SubmitForm(ctx, "************************************", "20164079", hubspotForm)
	if err != nil {
		return fmt.Errorf("submission request failed: %w", err)
	}

	return nil
}

func isInvalidDomain(email string) bool {
	domain := strings.Split(email, "@")[1]

	_, excluded := excludedDomains[domain]
	_, isCompetitor := competitorDomains[domain]

	return excluded || isCompetitor
}
