package quoteprivate

import (
	"testing"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

func TestBuildCarrierEmailMapping(t *testing.T) {

	carrier1MultipleEmails := models.LocationWithDistance{
		TMSLocation: models.TMSLocation{
			Emails: pq.StringArray{"<EMAIL>", "<EMAIL>"},
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: "Carrier1",
			},
		},
	}

	carrier1SingleEmail := models.LocationWithDistance{
		TMSLocation: models.TMSLocation{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:  "Carrier1",
				Email: "<EMAIL>",
			},
		},
	}

	carrier2SingleEmail := models.LocationWithDistance{
		TMSLocation: models.TMSLocation{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:  "Carrier2",
				Email: "<EMAIL>",
			},
		},
	}

	carrierWithTMSCarrier := models.LocationWithDistance{
		TMSLocation: models.TMSLocation{
			Emails: pq.StringArray{"<EMAIL>"},
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: "CarrierLocationName",
			},
			TMSCarrier: models.TMSCarrier{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: "CarrierTMSName",
				},
			},
		},
	}

	tests := []struct {
		name           string
		carriers       []models.LocationWithDistance
		selectedEmails []string
		wantNameMap    map[string]string
		wantEmailMap   map[string][]string
	}{
		{
			name:           "single carrier with multiple emails - all selected",
			carriers:       []models.LocationWithDistance{carrier1MultipleEmails},
			selectedEmails: []string{"<EMAIL>", "<EMAIL>"},
			wantEmailMap: map[string][]string{
				carrier1MultipleEmails.Name: {"<EMAIL>", "<EMAIL>"},
			},
		},
		{
			name:           "single carrier with multiple emails - partial selection",
			carriers:       []models.LocationWithDistance{carrier1MultipleEmails},
			selectedEmails: []string{"<EMAIL>"},
			wantEmailMap: map[string][]string{
				carrier1MultipleEmails.Name: {"<EMAIL>"},
			},
		},
		{
			name:           "multiple carriers with single emails",
			carriers:       []models.LocationWithDistance{carrier1SingleEmail, carrier2SingleEmail},
			selectedEmails: []string{"<EMAIL>", "<EMAIL>"},
			wantEmailMap: map[string][]string{
				carrier1SingleEmail.Name: {"<EMAIL>"},
				carrier2SingleEmail.Name: {"<EMAIL>"},
			},
		},
		{
			name:           "carrier with TMS carrier name",
			carriers:       []models.LocationWithDistance{carrierWithTMSCarrier},
			selectedEmails: []string{"<EMAIL>"},
			wantEmailMap: map[string][]string{
				carrierWithTMSCarrier.TMSCarrier.Name: {"<EMAIL>"},
			},
		},
		{
			name:           "unselected carriers are excluded",
			carriers:       []models.LocationWithDistance{carrier1MultipleEmails, carrier2SingleEmail},
			selectedEmails: []string{"<EMAIL>"},
			wantEmailMap: map[string][]string{
				carrier1MultipleEmails.Name: {"<EMAIL>"},
			},
		},
		{
			name:           "empty carriers",
			carriers:       []models.LocationWithDistance{},
			selectedEmails: []string{"<EMAIL>"},
			wantNameMap:    map[string]string{},
			wantEmailMap:   map[string][]string{},
		},
		{
			name:           "empty selected emails",
			carriers:       []models.LocationWithDistance{carrier1SingleEmail, carrier2SingleEmail},
			selectedEmails: []string{},
			wantEmailMap:   map[string][]string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotEmailMap := buildCarrierEmailMapping(tt.carriers, tt.selectedEmails)

			normalizedGotMap := make(map[string][]string)
			for k, v := range gotEmailMap {
				normalizedGotMap[util.Or(k.TMSCarrier.Name, k.Name)] = v
			}
			assert.Equal(t, tt.wantEmailMap, normalizedGotMap)
		})
	}
}
