package quoteprivate

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/customer/trident"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/util"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	LaneRateBody struct {
		EmailID          uint                 `json:"emailId"`
		ThreadID         string               `json:"threadId"`
		QuoteRequestID   uint                 `json:"quoteRequestId"`
		TransportType    models.TransportType `json:"transportType"`
		OriginDate       time.Time            `json:"originDate"`
		OriginZip        string               `json:"originZip"`
		OriginCity       string               `json:"originCity"`
		OriginState      string               `json:"originState"`
		DestinationDate  time.Time            `json:"destinationDate"`
		DestinationZip   string               `json:"destinationZip"`
		DestinationCity  string               `json:"destinationCity"`
		DestinationState string               `json:"destinationState"`
	}

	LaneRateResponse struct {
		RateType string `json:"rateType"`
		// Per Trip data
		RatePerTrip float64 `json:"ratePerTrip"`
		HighPerTrip float64 `json:"highPerTrip"`
		LowPerTrip  float64 `json:"lowPerTrip"`
		// Per Mile data
		RatePerMile float64 `json:"ratePerMile"`
		HighPerMile float64 `json:"highPerMile"`
		LowPerMile  float64 `json:"lowPerMile"`
		// Timeframe and geographical data
		Timeframe       string `json:"timeframe"`
		Companies       int64  `json:"companies"`
		Reports         int64  `json:"reports"`
		OriginName      string `json:"originName"`
		OriginType      string `json:"originType"`
		DestinationName string `json:"destinationName"`
		DestinationType string `json:"destinationType"`
	}
)

const (
	TridentTransportID = 562
)

func GetLaneRateFromService(c *fiber.Ctx) error {
	var body LaneRateBody
	var userID uint

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := c.UserContext()
	log.With(ctx, zap.String("serviceID", fmt.Sprint(userServiceID)))

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsGetLaneRateFromServiceEnabled {
		log.Error(ctx, "getting lane rate from service is not enabled")
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	email := middleware.ClaimsFromContext(c).Email
	userFound, err := userDB.GetByEmail(ctx, email)
	if err == nil {
		userID = userFound.ID
	}

	// no feature flag here because multiple services may use this feature in the future so we need
	// a different way to enable it for each service
	if service.ID != TridentTransportID {
		log.Warn(ctx, "skipping lane rate from service since service is not Trident")
		return c.Status(http.StatusBadRequest).SendString("Unrecognized Service")
	}

	// first, need to make sure zip codes aren't empty
	if body.OriginZip == "" {
		originZip, err := util.LookupZipCodeByCityState(ctx, body.OriginCity, body.OriginState)
		if err != nil {
			log.Error(
				ctx, "error looking up origin zip code",
				zap.Error(err),
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}
		if len(originZip) == 0 {
			log.Error(ctx, "could not find any zip code for origin city/state",
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}
		body.OriginZip = originZip
	}

	if body.DestinationZip == "" {
		destZip, err := util.LookupZipCodeByCityState(ctx, body.DestinationCity, body.DestinationState)
		if err != nil {
			log.Error(ctx,
				"error looking up destination zip code",
				zap.Error(err),
				zap.String("destinationCity", body.DestinationCity),
				zap.String("destinationState", body.DestinationState),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
		if len(destZip) == 0 {
			log.Error(ctx, "could not find any zip code for destination city/state",
				zap.String("destinationCity", body.DestinationCity),
				zap.String("destinationState", body.DestinationState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}
		body.DestinationZip = destZip
	}

	// If pickup date isn't provided, set it to:
	// - today + 1 day if delivery date also isn't provided
	// - delivery date - 1 day otherwise, assuming 1 day delivery
	if body.OriginDate.IsZero() {
		if !body.DestinationDate.IsZero() {
			body.OriginDate = body.DestinationDate.AddDate(0, 0, -1)
		} else {
			body.OriginDate = time.Now().AddDate(0, 0, 1)
		}
	}

	// If delivery date isn't provided, set it to:
	// - today + 2 days if pickup date also isn't provided
	// - pickup date + 1 day otherwise, assuming 1 day delivery
	if body.DestinationDate.IsZero() {
		if !body.OriginDate.IsZero() {
			body.DestinationDate = body.OriginDate.AddDate(0, 0, 1)
		} else {
			body.DestinationDate = time.Now().AddDate(0, 0, 2)
		}
	}

	integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
	if err != nil {
		log.Error(ctx, "integrationDB.GetByServiceIDAndType: %w", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client, err := trident.New(ctx, integration)
	if err != nil {
		log.Error(ctx, "error creating trident client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := client.GetLaneRate(ctx, trident.LaneRateRequest{
		OriginZip:      body.OriginZip,
		DestinationZip: body.DestinationZip,
		Equipment:      string(body.TransportType),
	})
	if err != nil {
		log.Error(ctx, "unable to get Trident lane rate", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("Trident's DAT integration wasn't able to get a lane rate")
	}

	numericalRateData, err := trident.ParseCalculateLaneRate(ctx, resp)
	if err != nil {
		log.Error(ctx, "unable to convert Trident lane rate numerical data", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("Trident's DAT integration wasn't able to convert lane rate numerical data")
	}

	rateDate := quote.RateData{
		ExternalID:      "",
		TargetBuyRate:   numericalRateData.RatePerTrip,
		LowBuyRate:      numericalRateData.LowPerTrip,
		HighBuyRate:     numericalRateData.HighPerTrip,
		StartBuyRate:    0,
		FuelRate:        numericalRateData.AvgFuelPerTrip,
		Distance:        numericalRateData.Mileage,
		ConfidenceLevel: numericalRateData.Confidence,
	}

	// TODO: support many stops
	stops := []models.Stop{
		{
			Order: 0,
			Address: models.Address{
				City:  body.OriginCity,
				State: body.OriginState,
				Zip:   body.OriginZip,
			},
		},
		{
			Order: 1,
			Address: models.Address{
				City:  body.DestinationCity,
				State: body.DestinationState,
				Zip:   body.DestinationZip,
			},
		},
	}

	datMetadata := models.QuoteDATMetadata{
		DATTimeframe:       resp.Timeframe,
		DATOriginName:      resp.OriginName,
		DATOriginType:      resp.OriginType,
		DATDestinationName: resp.DestinationName,
		DATDestinationType: resp.DestinationType,
	}

	quoteRecord := quote.CreateQuoteRecord(
		ctx,
		service,
		userID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.OriginDate,
		body.DestinationDate,
		rateDate,
		numericalRateData.RatePerTrip,
		numericalRateData.LowPerTrip,
		numericalRateData.HighPerTrip,
		models.DATSource,
		models.DATSpotType,
		&datMetadata,
	)
	if quoteRecord == nil {
		log.Error(ctx, "unable to create quote record for Trident DAT", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("error while creating quote for Trident's DAT integration")
	}

	companies, err := resp.Companies.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus company count to int64", zap.Error(err))
	}

	reports, err := resp.Reports.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus report count to int64", zap.Error(err))
	}

	return c.Status(http.StatusOK).JSON(LaneRateResponse{
		RateType:  resp.RateType,
		Companies: companies,
		Reports:   reports,
		// Per Trip data
		RatePerTrip: numericalRateData.RatePerTrip,
		LowPerTrip:  numericalRateData.LowPerTrip,
		HighPerTrip: numericalRateData.HighPerTrip,
		// Per Mile data
		RatePerMile: numericalRateData.RatePerMile,
		LowPerMile:  numericalRateData.LowPerMile,
		HighPerMile: numericalRateData.HighPerMile,
		// Timeframe and geographical data
		Timeframe:       resp.Timeframe,
		OriginName:      resp.OriginName,
		OriginType:      resp.OriginType,
		DestinationName: resp.DestinationName,
		DestinationType: resp.DestinationType,
	})
}
