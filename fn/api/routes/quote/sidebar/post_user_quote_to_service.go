package quoteprivate

import (
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/customer/trident"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	tmscustomer "github.com/drumkitai/drumkit/common/rds/tms_customers"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	UserQuoteBody struct {
		QuoteRequestID uint           `json:"quoteRequestId"`
		GSQuoteID      string         `json:"gsQuoteID"`
		Stops          []trident.Stop `json:"stops"`
		CarrierCost    float64        `json:"carrierCost"`
		Margin         float64        `json:"margin"`
		MarginType     string         `json:"marginType"`
		TargetSell     float64        `json:"targetSell"`
		DraftResponse  string         `json:"draftResponse"`
	}

	UserQuoteParams struct {
		EmailID uint `json:"emailID"`
	}
)

func PostUserQuoteToService(c *fiber.Ctx) error {
	var body UserQuoteBody
	var params UserQuoteParams

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := c.UserContext()

	if env.Vars.AppEnv != "prod" {
		log.Info(ctx, "user quote submission to service is disabled in dev")
		return c.SendStatus(http.StatusBadRequest)
	}

	emailAddress := middleware.ClaimsFromContext(c).Email
	user, err := userDB.GetByEmail(ctx, emailAddress)
	if err != nil {
		log.Error(ctx, "error fetching user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsQuoteSubmissionToServiceEnabled {
		log.Error(ctx, "quote submission to service is not enabled")
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := api.Parse(c, &params, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	var customerEmail string
	if params.EmailID != 0 {
		log.Info(ctx, "using v1 PostUserQuoteToService", zap.Uint("emailID", params.EmailID))

		email, err := emailDB.GetByID(ctx, params.EmailID)
		if err != nil {
			log.Error(ctx, "error fetching email", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
		customerEmail = email.Sender

	} else if body.QuoteRequestID != 0 {
		log.Info(ctx, "using v2 PostUserQuoteToService", zap.Uint("quoteRequestID", body.QuoteRequestID))

		// get the quote request in order to determine the customer email
		quoteRequest, err := quoteRequestDB.GetRequestByID(ctx, body.QuoteRequestID)
		if err != nil {
			log.Error(ctx, "error fetching quote request", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		if quoteRequest.AppliedRequest.CustomerID != 0 {
			customerEmail, err = tmscustomer.GetCustomerEmailByID(ctx, quoteRequest.AppliedRequest.CustomerID)
			if err != nil {
				log.Error(ctx, "error fetching customer email", zap.Error(err))
				return c.SendStatus(http.StatusInternalServerError)
			}
		} else if quoteRequest.EmailID != 0 {
			email, err := emailDB.GetByID(ctx, quoteRequest.EmailID)
			if err != nil {
				log.Error(ctx, "error fetching email", zap.Error(err))
				return c.SendStatus(http.StatusInternalServerError)
			}
			customerEmail = email.Sender
		}
	}

	integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID,
		models.CustomerType)
	if err != nil {
		err = fmt.Errorf("integrationDB.GetByServiceIDAndType: %w", err)
		return err
	}

	client, err := trident.New(ctx, integration)
	if err != nil {
		log.Error(ctx, "error creating trident client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	submission := trident.UserSubmission{
		ID:            uuid.NewString(),
		UserEmail:     user.EmailAddress,
		CustomerEmail: customerEmail,
		GSQuoteID:     body.GSQuoteID,
		Stops:         body.Stops,
		CarrierCost:   body.CarrierCost,
		Margin:        body.Margin,
		MarginType:    body.MarginType,
		TargetSell:    body.TargetSell,
		DraftResponse: body.DraftResponse,
	}

	resp, err := client.PostUserQuote(ctx, submission)
	if err != nil {
		log.Error(ctx, "error posting quote", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}
	log.Info(ctx, "successfully posted user quote to service", zap.Any("response", resp))

	return c.SendStatus(http.StatusOK)
}
