package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/integrations/pricing/truckstop"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailTemplateDB "github.com/drumkitai/drumkit/common/rds/emailtemplates"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/fn/api/env"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

const (
	QuoteCountryUS     QuoteCountryName = "US"
	QuoteCountryCanada QuoteCountryName = "CA"
)

type (
	QuoteCountryName string

	Stop struct {
		Order   int              `json:"order,omitempty"`
		Country QuoteCountryName `json:"country,omitempty"`
		State   string           `json:"state"`
		City    string           `json:"city"`
		Zip     string           `json:"zip" validate:"required_without_all=City State"`
	}

	RateValues struct {
		Target float64 `json:"target,omitempty"` // Maps to: TargetBuy, PredictedRate, TargetPerTrip
		Low    float64 `json:"low,omitempty"`    // Maps to: LowerRate, LowPerTrip
		High   float64 `json:"high,omitempty"`   // Maps to: UpperRate, HighPerTrip

		// Per-mile rates (when available)
		TargetPerMile float64 `json:"targetPerMile,omitempty"`
		LowPerMile    float64 `json:"lowPerMile,omitempty"`
		HighPerMile   float64 `json:"highPerMile,omitempty"`
	}

	Quote struct {
		ID     uint                     `json:"id"`
		Source models.QuoteSource       `json:"source"`         // Such as "Greenscreens", "DAT" or "TruckStop"
		Type   models.QuoteTypeInSource `json:"type,omitempty"` // Such as "GS Buy Power" or "GS Network"

		// Common fields across sources
		Rates    RateValues `json:"rates"`
		Distance float64    `json:"distance,omitempty"`

		// Provider-specific metadata
		Metadata map[string]any `json:"metadata,omitempty"`
	}

	Configuration struct {
		LowConfidenceThreshold    int    `json:"lowConfidenceThreshold,omitempty"`
		MediumConfidenceThreshold int    `json:"mediumConfidenceThreshold,omitempty"`
		BelowThresholdMessage     string `json:"belowThresholdMessage,omitempty"`
		DefaultPercentMargin      int    `json:"defaultPercentMargin,omitempty"`
		DefaultFlatMargin         int    `json:"defaultFlatMargin,omitempty"`
	}

	QuickQuotePrivateBody struct {
		LoadID string `json:"loadId"` // Load info
		//nolint:lll
		TransportType models.TransportType `json:"transportType" validate:"oneof=VAN REEFER FLATBED 'BOX TRUCK' HOTSHOT SPRINTER"`
		// TODO: Keep the old Stops backwards compatible until users have upgraded.
		Stops []Stop `json:"stops" validate:"min=2,dive"`
		// NOTE: This is the new Stops array that supports >2 stops.
		NewStops []models.Stop `json:"newStops" validate:"omitempty,min=2,dive"`
		// Optional; If not provided, use today + 1 day for compatibility
		PickupDate time.Time `json:"pickupDate"`
		// Optional; If not provided, use today + 2 days for compatibility
		DeliveryDate         time.Time `json:"deliveryDate"`
		CustomerName         string    `json:"customerName"` // This is the customer external TMS ID
		EmailID              uint      `json:"emailID"`      // Email the user was looking at during submission
		ThreadID             string    `json:"threadID"`     // Thread from the email related Email
		QuoteRequestID       uint      `json:"quoteRequestId"`
		SelectedQuickQuoteID *uint     `json:"selectedQuickQuoteId,omitempty"`
	}

	QuickQuoteEmailTemplateResponse struct {
		Subject string `json:"subject"`
		Body    string `json:"body"`
	}

	QuickQuotePrivateResponse struct {
		QuoteRequestID uint `json:"quoteRequestId"`

		Stops            []Stop                 `json:"stops"`
		SelectedRateName quote.SelectedRateName `json:"selectedRateName"` // Which rate does Drumkit default to?
		// HotShot and Box Truck are proxied with Flatbed and Van, respectively, in Greenscreens.
		// Use the following to inform users of any proxies used to complete request.
		InputtedTransportType  models.TransportType `json:"inputtedTransportType"`
		SubmittedTransportType models.TransportType `json:"submittedTransportType"`

		Configuration Configuration `json:"configuration"`

		Quotes []Quote `json:"quotes"`

		// QuoteReplyDraftTemplate is the email template for the reply to the email used for quoting.
		QuoteReplyDraftTemplate QuickQuoteEmailTemplateResponse `json:"quoteReplyDraftTemplate,omitempty"`
	}

	ErrorData struct {
		Error string `json:"error"`
	}
)

func GetQuickQuoteV2(c *fiber.Ctx) error {
	var body QuickQuotePrivateBody
	var err error
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userFound, err := userDB.GetByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// If the customer name (external TMS ID) is provided, we must associate it with the quote request.
	var customer models.TMSCustomer
	if body.CustomerName != "" {
		tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
		if err != nil {
			log.Error(ctx, "error fetching TMS integrations", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		if len(tmsIntegrations) == 0 {
			log.Error(ctx, "no TMS integrations found")
			return c.SendStatus(http.StatusInternalServerError)
		}

		customer, err = tmsCustomerDB.GetByExternalTMSID(ctx, tmsIntegrations[0].ID, body.CustomerName)
		if err != nil {
			log.Error(ctx, "error fetching customer", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// If pickup date isn't provided, set it to:
	// - today + 1 day if delivery date also isn't provided
	// - delivery date - 1 day otherwise, assuming 1 day delivery
	if body.PickupDate.IsZero() {
		if !body.DeliveryDate.IsZero() {
			body.PickupDate = body.DeliveryDate.AddDate(0, 0, -1)
		} else {
			body.PickupDate = time.Now().AddDate(0, 0, 1)
		}
	}

	// If delivery date isn't provided, set it to:
	// - today + 2 days if pickup date also isn't provided
	// - pickup date + 1 day otherwise, assuming 1 day delivery
	if body.DeliveryDate.IsZero() {
		if !body.PickupDate.IsZero() {
			body.DeliveryDate = body.PickupDate.AddDate(0, 0, 1)
		} else {
			body.DeliveryDate = time.Now().AddDate(0, 0, 2)
		}
	}

	resp, err := getQuickQuote(ctx, &body, service, userFound.EmailAddress, userFound, customer)
	if err != nil {
		originStr, destStr := getOriginDestinationStrFromStops(body.Stops)

		log.Error(
			ctx,
			"getQuickQuote failed",
			zap.String("origin", originStr),
			zap.String("destination", destStr),
			zap.Error(err),
		)

		return c.Status(http.StatusServiceUnavailable).JSON(ErrorData{
			Error: err.Error(),
		})
	}

	// if getPrivateQuickQuote succeeds, update the parent quote request status to inFlight and store the
	// suggested quote request fields as applied fields.
	pickupLocation := models.Address{
		City:  resp.Stops[0].City,
		State: resp.Stops[0].State,
		Zip:   resp.Stops[0].Zip,
	}

	dropoffLocation := models.Address{
		City:  resp.Stops[1].City,
		State: resp.Stops[1].State,
		Zip:   resp.Stops[1].Zip,
	}

	appliedFields := models.QuoteLoadInfo{
		CustomerID:       customer.ID,
		TransportType:    resp.InputtedTransportType,
		PickupLocation:   pickupLocation,
		PickupDate:       models.NullTime{Time: body.PickupDate, Valid: true},
		DeliveryLocation: dropoffLocation,
		DeliveryDate:     models.NullTime{Time: body.DeliveryDate, Valid: true},
		Stops:            body.NewStops,
	}

	err = quoteRequestDB.UpdateQuoteRequestStatus(
		ctx,
		body.QuoteRequestID,
		&appliedFields,
		&userFound,
		models.InFlight,
		nil,
		body.SelectedQuickQuoteID,
	)
	if err != nil {
		log.Error(ctx, "failed to update quote request status", zap.Error(err))
	}

	// Quote requests are typically referenced via a clicked suggestion in the inbox UI.
	// However, since users can now access the sidebar from outside the inbox
	// (where no email suggestions exist), we include the quote request ID in the
	// response to maintain frontend reference tracking.
	resp.QuoteRequestID = body.QuoteRequestID

	var lowConfidenceThreshold int
	if service.QuickQuoteConfig != nil {
		lowConfidenceThreshold = service.QuickQuoteConfig.LowConfidenceThreshold
	}

	// Confidence values are specific for Greenscreens, so if confidence is low for all GS rates
	// and service doesn't omit low confidence warning, add threshold message to the response.
	showLowConfWarning := showLowConfidenceWarning(lowConfidenceThreshold, resp.Quotes)
	if showLowConfWarning && !service.QuickQuoteConfig.OmitUnderLowThreshold {
		resp.Configuration.BelowThresholdMessage = service.QuickQuoteConfig.BelowThresholdMessage
	}

	quoteReplyTemplate, err := getQuoteReplyTemplate(ctx, userFound)
	if err != nil {
		// Log error but fail-open, as we still want to return the default template.
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no quote reply template found, using default template", zap.Error(err))
		} else {
			log.Error(ctx, "error fetching quote reply template", zap.Error(err))
		}
	}

	resp.QuoteReplyDraftTemplate = QuickQuoteEmailTemplateResponse{
		Subject: quoteReplyTemplate.Subject,
		Body:    quoteReplyTemplate.Body,
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func mapTransportType(transportType models.TransportType) string {
	// Greenscreens does not support HOTSHOT, BOX TRUCK, or SPRINTER,
	// so these types are proxied with Flatbed and Van respectively
	switch t := strings.ToUpper(string(transportType)); t {
	case "HOTSHOT":
		return "FLATBED"
	case "BOX TRUCK":
		return "VAN"
	case "SPRINTER":
		return "VAN"
	default:
		return t
	}
}

// The quick quote is a min-max range determined by the lower target buy rate of either the lane rate
// prediction or network lane rate prediction IFF the confidence level is above 75. The minimum of the range
// is the target buy rate + 7% markup and the maximum of the range is the target buy rate + 10% markup.
func getQuickQuote(
	ctx context.Context,
	body *QuickQuotePrivateBody,
	service models.Service,
	email string,
	user models.User,
	customer models.TMSCustomer,
) (*QuickQuotePrivateResponse, error) {
	var ratePredictionStops []greenscreens.RatePredictionStop
	var stops []models.Stop
	transportTypeInput := mapTransportType(body.TransportType)

	pricingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, service.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching pricing integration: %w", err)
	}
	if len(pricingIntegrations) == 0 {
		return nil, fmt.Errorf("no pricing integrations found for service")
	}

	var quotes = []Quote{}
	var selectedRateName quote.SelectedRateName

	// NOTE: Handle stop processing for rate prediction.
	// Legacy format only supports exactly 2 stops (pickup and dropoff).
	// while new format supports multiple stops (pickup, intermediate stops, and dropoff)
	if len(body.NewStops) > 2 {
		// New format: Convert internal Stop model array that supports multiple stops
		// This handles cases with pickup[0], intermediate stops[1..n-1], and dropoff[n]
		var err error
		ratePredictionStops, err = convertToRatePredictionStops(ctx, body.NewStops)
		if err != nil {
			return nil, fmt.Errorf("failed to process multi-stop format: %w", err)
		}

	} else {
		// Legacy format: Process original two-stop format (pickup and dropoff only).
		// This maintains backward compatibility with existing logic.
		for i, item := range body.Stops {
			stop := models.Stop{
				Order: i,
				Address: models.Address{
					Country: string(item.Country),
					State:   item.State,
					City:    item.City,
					Zip:     item.Zip,
				},
			}

			// We need all stops to have a city, state, and zip for metrics to work with higher confidence.
			if item.City == "" || item.State == "" {

				// Using AWS Locations for Canada postal codes, since they're not supported by USPS API
				if item.Country == QuoteCountryCanada {
					city, province, err := getCanadaLocationByPostalCode(ctx, &item.Zip)
					if err != nil {
						return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
					}

					stop.Address.City = city
					stop.Address.State = province
					stops = append(stops, stop)
					continue
				}

				zipResp, err := util.LookupCityStateByZipcode(ctx, item.Zip, env.Vars.USPSUserID)
				if err != nil {
					return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
				}

				stop.Address.State = zipResp.ZipCode.State
				stop.Address.City = zipResp.ZipCode.City
			}

			if item.Zip == "" {

				zipResp, err := util.LookupZipCodeByCityState(ctx, item.City, item.State)
				if err != nil {
					return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
				}

				if len(zipResp) != 0 {
					stop.Address.Zip = zipResp
				}
			}

			stops = append(stops, stop)
		}
	}

	// before fetching external quotes, ensure a parent quote request exists
	if body.QuoteRequestID == 0 {
		pickupLocation := models.Address{
			City:    stops[0].Address.City,
			State:   stops[0].Address.State,
			Zip:     stops[0].Address.Zip,
			Country: stops[0].Address.Country,
		}

		dropoffLocation := models.Address{
			City:    stops[1].Address.City,
			State:   stops[1].Address.State,
			Zip:     stops[1].Address.Zip,
			Country: stops[1].Address.Country,
		}

		appliedFields := models.QuoteLoadInfo{
			CustomerID:       customer.ID,
			TransportType:    body.TransportType,
			PickupLocation:   pickupLocation,
			PickupDate:       models.NullTime{Time: body.PickupDate, Valid: true},
			DeliveryLocation: dropoffLocation,
			DeliveryDate:     models.NullTime{Time: body.DeliveryDate, Valid: true},
			Stops:            stops,
		}

		parentQuoteRequest := models.QuoteRequest{
			UserID:   user.ID,
			EmailID:  body.EmailID,  // may be 0 if user is using a portal
			ThreadID: body.ThreadID, // may be "" if user is using a portal
			// TODO: Add source to payload to capture portal QQs w/o suggestions
			ServiceID:            service.ID,
			AppliedRequest:       appliedFields,
			Status:               models.Pending, // we update to inFlight once this function succeeds
			SelectedQuickQuoteID: body.SelectedQuickQuoteID,
		}

		err = quoteRequestDB.CreateQuoteRequest(ctx, &parentQuoteRequest)
		if err != nil {
			return nil, fmt.Errorf("error creating parent quote request: %w", err)
		}

		body.QuoteRequestID = parentQuoteRequest.ID

	}

	for _, integration := range pricingIntegrations {
		switch integration.Name {
		case models.Greenscreens:
			// Call GreenScreens API with stops array.
			// This supports multiple stops (>2) as ratePredictionStops can contain pickup[0], intermediate
			// stops[1..n-1], and dropoff[n].
			// Works with both legacy format (2 stops) and new multi-stop format.
			laneRatePrediction, networkLaneRatePrediction, _, err := quote.
				FetchGreenscreensInfo(
					ctx,
					service,
					integration,
					stops,
					email,
					models.TransportType(transportTypeInput),
					body.PickupDate,
				)
			if err != nil {
				// This is a backwards-compatible approach for handling low confidence rates
				// when OmitUnderLowThreshold is enabled.
				//
				// Previously, we would halt all logic and only return an ErrNoConfidentQuote if
				// if Greenscreens returned a low confidence rate. Now, since we can have valid
				// results from other sources, we append a GS Quote object with the respective
				// error under the Metadata attributes.
				if errors.Is(err, quote.ErrNoConfidentQuote) {
					quotes = append(quotes, Quote{
						Source: models.GreenscreensSource,
						Metadata: map[string]any{
							"error": quote.ErrNoConfidentQuote.Error(),
						},
					})
				}

				log.Error(ctx, "failed to get Greenscreens quote", zap.Error(err))
				continue
			}

			// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
			err = quote.SetRedisMileDistanceBetweenLocations(
				ctx,
				stops[0].Address.City,
				stops[0].Address.State,
				stops[1].Address.City,
				stops[1].Address.State,
				laneRatePrediction.Distance,
			)
			if err != nil {
				log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
			}

			createdGSBuyPower := createGSQuoteRecord(
				ctx,
				service,
				user,
				body,
				stops,
				laneRatePrediction,
				models.GSBuyPowerType,
			)
			quotes = append(quotes, Quote{
				ID:     createdGSBuyPower.ID,
				Source: models.GreenscreensSource,
				Type:   models.GSBuyPowerType,
				Rates: RateValues{
					TargetPerMile: laneRatePrediction.TargetBuyRate,
					LowPerMile:    laneRatePrediction.LowBuyRate,
					HighPerMile:   laneRatePrediction.HighBuyRate,
				},
				Distance: laneRatePrediction.Distance,
				Metadata: map[string]any{
					"confidenceLevel": laneRatePrediction.ConfidenceLevel,
				},
			})

			createdGSNetwork := createGSQuoteRecord(
				ctx,
				service,
				user,
				body,
				stops,
				networkLaneRatePrediction,
				models.GSNetworkType,
			)
			quotes = append(quotes, Quote{
				ID:     createdGSNetwork.ID,
				Source: models.GreenscreensSource,
				Type:   models.GSNetworkType,
				Rates: RateValues{
					TargetPerMile: networkLaneRatePrediction.TargetBuyRate,
					LowPerMile:    networkLaneRatePrediction.LowBuyRate,
					HighPerMile:   networkLaneRatePrediction.HighBuyRate,
				},
				Distance: networkLaneRatePrediction.Distance,
				Metadata: map[string]any{
					"confidenceLevel": networkLaneRatePrediction.ConfidenceLevel,
				},
			})

		case models.DAT:
			if !user.HasGrantedDATPermissions {
				log.Warn(ctx, "user fetched quote but has not granted DAT permissions")
				continue
			}

			reqBody := []dat.RateRequest{
				{
					Origin: dat.InputLocation{
						PostalCode:      stops[0].Address.Zip,
						City:            stops[0].Address.City,
						StateOrProvince: stops[0].Address.State,
					},
					Destination: dat.InputLocation{
						PostalCode:      stops[1].Address.Zip,
						City:            stops[1].Address.City,
						StateOrProvince: stops[1].Address.State,
					},
					Equipment: models.TransportType(transportTypeInput),
					RateType:  dat.BrokerToCarrierSpotRateType,
				},
			}

			resp, err := quote.GetDATLaneRate(ctx, integration, user.DATEmailAddress, &reqBody)
			if err != nil {
				log.Error(ctx, "failed to get DAT lane rate", zap.Error(err))
				continue
			}

			// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
			err = quote.SetRedisMileDistanceBetweenLocations(
				ctx,
				stops[0].Address.City,
				stops[0].Address.State,
				stops[1].Address.City,
				stops[1].Address.State,
				resp.RateResponses[0].Response.Rate.Mileage,
			)
			if err != nil {
				log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
			}

			selectedRateName = quote.DATRateView

			rateData := resp.RateResponses[0].Response
			createdQuote := createDATQuoteRecord(ctx, service, user, body, stops, rateData)

			var avgFuelPerMile float64
			var avgRatePerMile float64
			var lowRatePerMile float64
			var highRatePerMile float64

			// DAT per-mile values flaky and randomly unavailable for some lanes, so we calculate them ourselves.
			avgRatePerMile = rateData.Rate.PerTrip.RateUSD / rateData.Rate.Mileage
			lowRatePerMile = rateData.Rate.PerTrip.LowUSD / rateData.Rate.Mileage
			highRatePerMile = rateData.Rate.PerTrip.HighUSD / rateData.Rate.Mileage

			avgFuelPerTrip := rateData.Rate.AverageFuelSurchargePerTripUsd
			avgFuelPerMile = math.Round((avgFuelPerTrip/rateData.Rate.Mileage)*100) / 100

			quotes = append(quotes, Quote{
				ID:     createdQuote.ID,
				Source: models.DATSource,
				Type:   models.DATSpotType,
				Rates: RateValues{
					Target:        rateData.Rate.PerTrip.RateUSD + avgFuelPerTrip,
					Low:           rateData.Rate.PerTrip.LowUSD + avgFuelPerTrip,
					High:          rateData.Rate.PerTrip.HighUSD + avgFuelPerTrip,
					TargetPerMile: avgRatePerMile + avgFuelPerMile,
					LowPerMile:    lowRatePerMile + avgFuelPerMile,
					HighPerMile:   highRatePerMile + avgFuelPerMile,
				},
				Metadata: map[string]any{
					"reports":              rateData.Rate.Reports,
					"companies":            rateData.Rate.Companies,
					"timeframe":            rateData.Escalation.Timeframe,
					"originName":           rateData.Escalation.Origin.Name,
					"originType":           rateData.Escalation.Origin.Type,
					"destinationName":      rateData.Escalation.Destination.Name,
					"destinationType":      rateData.Escalation.Destination.Type,
					"fuelSurchargePerMile": avgFuelPerMile,
				},
				Distance: rateData.Rate.Mileage,
			})

		case models.Truckstop:
			if stops[0].Address.Country == string(QuoteCountryCanada) ||
				stops[1].Address.Country == string(QuoteCountryCanada) {

				log.WarnNoSentry(ctx, "TruckStop quote not supported for Canada")
				continue
			}

			// TruckStop only supports pickup and delivery locations (2 stops).
			// We need to extract just the first and last stops.
			var truckstopStops []greenscreens.RatePredictionStop

			if len(body.NewStops) >= 2 {
				// For the new format, only extract pickup[0] and dropoff[n-1] stops.
				firstLast := []models.Stop{
					body.NewStops[0],                    // pickup
					body.NewStops[len(body.NewStops)-1], // delivery
				}

				var err error
				truckstopStops, err = convertToRatePredictionStops(ctx, firstLast)
				if err != nil {
					return nil, fmt.Errorf("failed to process stops for TruckStop: %w", err)
				}
			} else {
				// Legacy format already has exactly 2 stops
				truckstopStops = ratePredictionStops
			}

			for i, item := range truckstopStops {
				if item.City != "" && item.State != "" && item.Zip == "" {
					zipResp, err := util.LookupZipCodeByCityState(ctx, item.City, item.State)
					if err != nil {
						log.Error(ctx, "failed to lookup zipcode", zap.Error(err))
						continue
					}

					truckstopStops[i].Zip = zipResp
				}
			}

			bookedEstimate, postedEstimateData, err := quote.FetchTruckStopQuotes(
				ctx,
				service,
				integration,
				email,
				body.LoadID,
				truckstop.LocationDetails{
					StateCode: truckstopStops[0].State,
					City:      truckstopStops[0].City,
					ZipCode:   truckstopStops[0].Zip,
				},
				truckstop.LocationDetails{
					StateCode: truckstopStops[1].State,
					City:      truckstopStops[1].City,
					ZipCode:   truckstopStops[1].Zip,
				},
				models.TransportType(transportTypeInput),
				body.PickupDate,
				body.DeliveryDate,
			)
			if err != nil {
				log.Error(ctx, "failed to get truckstop quote", zap.Error(err))
				continue
			}

			// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
			estimatedMileDistance := bookedEstimate.PredictedRate / bookedEstimate.RatePerMile
			err = quote.SetRedisMileDistanceBetweenLocations(
				ctx,
				stops[0].Address.City,
				stops[0].Address.State,
				stops[1].Address.City,
				stops[1].Address.State,
				estimatedMileDistance,
			)
			if err != nil {
				log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
			}

			createdBookRate, createdPostedRate := createTruckstopQuoteRecord(
				ctx,
				service,
				user,
				body,
				stops,
				bookedEstimate,
				postedEstimateData,
			)

			quotes = append(quotes,
				Quote{
					ID:     createdBookRate.ID,
					Source: models.TruckStopSource,
					Type:   models.TruckStopBookedType,
					Rates: RateValues{
						Target:        bookedEstimate.Average,
						Low:           bookedEstimate.LowerRate,
						High:          bookedEstimate.UpperRate,
						TargetPerMile: bookedEstimate.RatePerMile,
					},
					Metadata: map[string]any{
						"confidenceLevel": bookedEstimate.Average,
					},
				},
				Quote{
					ID:     createdPostedRate.ID,
					Source: models.TruckStopSource,
					Type:   models.TruckStopPostedType,
					Rates: RateValues{
						Target:        postedEstimateData.Average,
						Low:           postedEstimateData.LowerRate,
						High:          postedEstimateData.UpperRate,
						TargetPerMile: postedEstimateData.RatePerMile,
					},
					Metadata: map[string]any{
						"confidenceLevel": postedEstimateData.Average,
					},
				},
			)
		}
	}

	var config Configuration
	if service.QuickQuoteConfig != nil {
		config = Configuration{
			LowConfidenceThreshold:    service.QuickQuoteConfig.LowConfidenceThreshold,
			MediumConfidenceThreshold: service.QuickQuoteConfig.MediumConfidenceThreshold,
			DefaultPercentMargin:      service.QuickQuoteConfig.DefaultPercentMargin,
			DefaultFlatMargin:         service.QuickQuoteConfig.DefaultFlatMargin,
		}
	}

	var respStops []Stop
	for _, stop := range stops {
		stopCountry := QuoteCountryName(stop.Address.Country)
		respStops = append(respStops, Stop{
			Order:   stop.Order,
			Country: stopCountry,
			State:   stop.Address.State,
			City:    stop.Address.City,
			Zip:     stop.Address.Zip,
		})
	}

	return &QuickQuotePrivateResponse{
		Stops:                  respStops,
		SelectedRateName:       selectedRateName,
		InputtedTransportType:  body.TransportType,
		SubmittedTransportType: models.TransportType(transportTypeInput),
		Quotes:                 quotes,
		Configuration:          config,
	}, err
}

func createGSQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	ratePrediction *greenscreens.RatePredictionDetail,
	typeInSource models.QuoteTypeInSource,
) *models.QuickQuote {
	rateTargetCost := ratePrediction.TargetBuyRate * ratePrediction.Distance
	rateData := quote.RateData{
		ExternalID:      ratePrediction.ID,
		TargetBuyRate:   ratePrediction.TargetBuyRate,
		LowBuyRate:      ratePrediction.LowBuyRate,
		HighBuyRate:     ratePrediction.HighBuyRate,
		StartBuyRate:    ratePrediction.StartBuyRate,
		FuelRate:        ratePrediction.FuelRate,
		Distance:        ratePrediction.Distance,
		ConfidenceLevel: float64(ratePrediction.ConfidenceLevel),
	}

	return quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		rateData,
		rateTargetCost,
		1.07*rateTargetCost,
		1.10*rateTargetCost,
		models.GreenscreensSource,
		typeInSource,
		nil,
	)
}

func createDATQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	ratePrediction dat.RateResponse,
) *models.QuickQuote {
	avgFuelPerTrip := ratePrediction.Rate.AverageFuelSurchargePerTripUsd
	rateData := quote.RateData{
		TargetBuyRate: ratePrediction.Rate.PerTrip.RateUSD + avgFuelPerTrip,
		LowBuyRate:    ratePrediction.Rate.PerTrip.LowUSD + avgFuelPerTrip,
		HighBuyRate:   ratePrediction.Rate.PerTrip.HighUSD + avgFuelPerTrip,
		Distance:      ratePrediction.Rate.Mileage,
	}

	return quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		rateData,
		0,
		0,
		0,
		models.DATSource,
		models.DATSpotType,
		nil,
	)
}

func createTruckstopQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	bookedEstimate *truckstop.BookedRateEstimateResp,
	postedEstimate *truckstop.PostedRateResp,
) (*models.QuickQuote, *models.QuickQuote) {
	bookedRateData := quote.RateData{
		TargetBuyRate: bookedEstimate.Average,
		LowBuyRate:    bookedEstimate.LowerRate,
		HighBuyRate:   bookedEstimate.UpperRate,
	}

	createdBookRate := quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		bookedRateData,
		0,
		0,
		0,
		models.TruckStopSource,
		models.TruckStopBookedType,
		nil,
	)

	postedEstimateData := quote.RateData{
		TargetBuyRate: postedEstimate.Average,
		LowBuyRate:    postedEstimate.LowerRate,
		HighBuyRate:   postedEstimate.UpperRate,
	}

	createdPostedRate := quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		postedEstimateData,
		0,
		0,
		0,
		models.TruckStopSource,
		models.TruckStopPostedType,
		nil,
	)

	return createdBookRate, createdPostedRate
}

func showLowConfidenceWarning(lowConfidenceThreshold int, quotes []Quote) bool {

	if lowConfidenceThreshold != 0 {
		gsQuoteCount := 0
		gsLowThresholdCount := 0
		for _, quote := range quotes {
			if quote.Source == models.GreenscreensSource &&
				(quote.Type == models.GSBuyPowerType || quote.Type == models.GSNetworkType) {
				gsQuoteCount++

				if conf, ok := quote.Metadata["confidenceLevel"].(int); ok && conf < lowConfidenceThreshold {
					gsLowThresholdCount++
				}
			}
		}

		if gsQuoteCount > 0 && gsLowThresholdCount == gsQuoteCount {
			return true
		}
	}

	return false
}

func convertToRatePredictionStops(ctx context.Context, stops []models.Stop) ([]greenscreens.RatePredictionStop, error) {
	predictionStops := make([]greenscreens.RatePredictionStop, len(stops))

	// We need all stops to have a city, state, and zip for metrics to work with higher confidence.
	for i, stop := range stops {
		predictionStops[i] = greenscreens.RatePredictionStop{
			Order:   stop.StopNumber,
			Country: "US", // Default to US
			State:   stop.Address.State,
			City:    stop.Address.City,
			Zip:     stop.Address.Zip,
		}

		if (stop.Address.City == "" || stop.Address.State == "") && stop.Address.Zip != "" {
			// Missing city or state but have ZIP - lookup by ZIP
			zipResp, err := util.LookupCityStateByZipcode(ctx, stop.Address.Zip, env.Vars.USPSUserID)
			if err != nil {
				return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
			}
			predictionStops[i].State = zipResp.ZipCode.State
			predictionStops[i].City = zipResp.ZipCode.City

		} else if stop.Address.Zip == "" {
			// Missing ZIP - lookup by city and state
			zipResp, err := util.LookupZipCodeByCityState(ctx, stop.Address.City, stop.Address.State)
			if err != nil {
				return nil, fmt.Errorf("error looking up stop %d's zipcode: %w", i, err)
			}
			if len(zipResp) != 0 {
				predictionStops[i].Zip = zipResp
			}
		}
	}

	return predictionStops, nil
}

func getOriginDestinationStrFromStops(stops []Stop) (originStr string, destStr string) {
	if len(stops) == 0 {
		return "", ""
	}

	originStr = stops[0].Zip
	destStr = stops[len(stops)-1].Zip

	if originStr == "" {
		originStr = fmt.Sprintf("%s, %s", stops[0].City, stops[0].State)
	}

	if destStr == "" {
		destStr = fmt.Sprintf("%s, %s", stops[len(stops)-1].City, stops[len(stops)-1].State)
	}

	return originStr, destStr
}

func getQuoteReplyTemplate(ctx context.Context, user models.User) (models.EmailTemplate, error) {
	emailTemplate, err := emailTemplateDB.GetEmailTemplateByTypeAndIDs(ctx,
		user.ID,
		user.ServiceID,
		models.QuickQuoteReply,
	)
	if err == nil && emailTemplate != nil {
		return *emailTemplate, nil
	}

	return models.GenericEmailTemplates[models.QuickQuoteReply], err
}
