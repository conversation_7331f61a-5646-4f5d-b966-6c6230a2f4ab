package quoteprivate

import (
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	PostSubmitQuoteViaURLParams struct {
		EmailID uint `json:"emailID" validate:"required"`
	}

	PostSubmitQuoteViaURLBody struct {
		QuoteAmount    int       `json:"quoteAmount" validate:"required"`
		QuoteNumber    string    `json:"quoteNumber" validate:"required"`
		ExpirationDate time.Time `json:"expirationDate"`
		Eta            time.Time `json:"eta"`
	}
)

func PostSubmitQuoteViaURL(c *fiber.Ctx) error {
	var params PostSubmitQuoteViaURLParams
	var body PostSubmitQuoteViaURLBody
	var err error

	if err := api.Parse(c, &params, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()

	email := middleware.ClaimsFromContext(c).Email
	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "could not get user from email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// get the quote's OID from the submission URL
	quoteURLs, err := quoteRequestDB.GetThirdPartyURLsByEmailID(ctx, params.EmailID)
	if err != nil {
		log.Error(ctx, "could not get submission URL", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var quoteOID string
	re := regexp.MustCompile(`-3Foid-3D([0-9]+)`)
	matches := re.FindStringSubmatch(quoteURLs.FormURL)
	if len(matches) > 1 {
		quoteOID = matches[1]
	} else {
		log.Error(ctx, "could not find quote OID in the submission URL", zap.String("quoteFormURL", quoteURLs.FormURL))
		return c.SendStatus(http.StatusInternalServerError)
	}

	expDate, expHours, expMinutes, expAmpm := formatTimeForQueryParams(body.ExpirationDate)

	etaDate, etaHours, etaMinutes, etaAmpm := formatTimeForQueryParams(body.Eta)

	parsedSubmissionURL, err := url.Parse(quoteURLs.SubmissionURL)
	if err != nil {
		log.Error(ctx, "could not parse the base URL", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// define query parameters
	queryParamsForSubmissionURL := url.Values{}
	queryParamsForSubmissionURL.Add("oid", quoteOID)
	queryParamsForSubmissionURL.Add("Submit", "true")
	queryParamsForSubmissionURL.Add("fromCarrierQuotePortlet", "")
	queryParamsForSubmissionURL.Add("quoteCurrency", "USD")
	queryParamsForSubmissionURL.Add("Amount", strconv.Itoa(body.QuoteAmount))
	queryParamsForSubmissionURL.Add("sCurrencyCode", "USD")
	queryParamsForSubmissionURL.Add("quoteNumber", body.QuoteNumber)
	queryParamsForSubmissionURL.Add("dateQuoteExpirationDate", expDate)
	queryParamsForSubmissionURL.Add("hoursQuoteExpirationDate", expHours)
	queryParamsForSubmissionURL.Add("minutesQuoteExpirationDate", expMinutes)
	queryParamsForSubmissionURL.Add("ampmQuoteExpirationDate", expAmpm)
	queryParamsForSubmissionURL.Add("dateQuoteETA", etaDate)
	queryParamsForSubmissionURL.Add("hoursQuoteETA", etaHours)
	queryParamsForSubmissionURL.Add("minutesQuoteETA", etaMinutes)
	queryParamsForSubmissionURL.Add("ampmQuoteETA", etaAmpm)
	queryParamsForSubmissionURL.Add("quoteComment", "")
	queryParamsForSubmissionURL.Add("quoteContact", user.Name)
	queryParamsForSubmissionURL.Add("quoteEmail", user.EmailAddress)
	queryParamsForSubmissionURL.Add("quotePhone", "")
	queryParamsForSubmissionURL.Add("quoteFax", "")
	queryParamsForSubmissionURL.Add("bookItNow", "false")
	queryParamsForSubmissionURL.Add("bookItNowAmount", "")

	parsedSubmissionURL.RawQuery = queryParamsForSubmissionURL.Encode()

	log.Info(ctx, "built url for submitting quotes via url", zap.String("url", parsedSubmissionURL.String()))

	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, parsedSubmissionURL.String(), nil)
	if err != nil {
		log.Error(ctx, "could not build request", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	res, err := client.Do(req)
	if err != nil {
		log.Error(ctx, "could not get response", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}
	defer res.Body.Close()

	// response is html so we can't rely fully on the status code
	var bodyBytes []byte
	if res.StatusCode == http.StatusOK {
		bodyBytes, err = io.ReadAll(res.Body)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to read response body", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		if strings.Contains(strings.ToLower(string(bodyBytes)), "processing failed") {
			log.WarnNoSentry(
				ctx,
				"submitting quote via url returned error",
				zap.String("responseBody", string(bodyBytes)),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

		log.Info(ctx, "successful response", zap.String("responseBody", string(bodyBytes)))
		return c.SendStatus(http.StatusOK)
	}

	log.Error(ctx,
		"submitting quote via URL failed",
		zap.Int("statusCode", res.StatusCode),
		zap.String("responseBody", string(bodyBytes)),
	)

	return c.SendStatus(http.StatusInternalServerError)
}

func formatTimeForQueryParams(t time.Time) (string, string, string, string) {
	date := t.Format("01/02/2006") // Get date as MM/DD/YYYY
	hours := t.Format("3")         // Get hours in 12-hour clock
	minutes := t.Format("04")      // Get minutes
	ampm := t.Format("PM")         // Get AM/PM

	return date, hours, minutes, ampm
}
