package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

type (
	SearchCustomersQuery = tmsCustomerDB.SearchCustomersQuery

	SearchCustomersResponse = GetCustomersResponse
)

// Searches for customers in the DB by name OR by addressLine1 (note: not AND)
// Note that unlike GetCustomers, this function does not support force refresh.
func SearchCustomers(c *fiber.Ctx) error {
	var query SearchCustomersQuery

	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	var err error
	var customerList []models.TMSCustomer
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "integration not configured properly",
				zap.Error(err), zap.Uint("service_id", userServiceID))
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integration from DB",
			zap.Error(err), zap.Uint("service_id", userServiceID))
		return err
	}
	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(ctx, "user serviceID %d does not match TMS service ID %d", userServiceID, tmsIntegration.ServiceID)
		return c.SendStatus(http.StatusUnauthorized)
	}
	query.TMSID = tmsIntegration.ID

	if query.Name != "" {
		customerList, err = tmsCustomerDB.FuzzySearchByName(ctx, query)
	} else {
		customerList, err = tmsCustomerDB.FuzzySearchByStreetAddress(ctx, query)
	}
	if err != nil {
		log.Error(ctx, "error searching TMS customers in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(
		GetCustomersResponse{
			CustomerList: customerList,
			TMSTenant:    tmsIntegration.Tenant,
		},
	)
}
