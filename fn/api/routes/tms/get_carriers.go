package tmsroutes

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCarrierDB "github.com/drumkitai/drumkit/common/rds/tms_carrier"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/util"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetCarriersQuery = rds.GenericGetQuery

	GetCarriersResponse struct {
		CarrierList []models.TMSCarrier `json:"carrierList"`
		TMSTenant   string              `json:"tmsTenant"`
		Message     string              `json:"message"`
	}
)

func GetCarriers(c *fiber.Ctx) error {
	var query GetCarriersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	if query.Limit <= 0 {
		query.Limit = 100
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	var err error
	var carrierList []models.TMSCarrier
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch customers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(ctx, "user serviceID %d does not match TMS service ID %d", userServiceID, tmsIntegration.ServiceID)
		return c.SendStatus(http.StatusUnauthorized)
	}

	// If we're not forcing a refresh, then get from DB.
	// If async request, get query.Limit records from DB to return to FE as a placeholder
	// while we asynchronously add the rest to DB
	if !query.ForceRefresh || query.Async {
		carrierList, err = tmsCarrierDB.GetTMSCarriersByTMSID(ctx, query)

		switch {
		case len(carrierList) == 0:
			log.Info(ctx, "no carriers found in DB, falling back to TMS")

		case err != nil:
			// Fail-open and try TMS; Gorm will send to Sentry
			log.WarnNoSentry(ctx, "error searching TMS carriers in DB, falling back to TMS", zap.Error(err))

		case len(carrierList) > 0 && !query.Async:
			return c.Status(http.StatusOK).JSON(
				GetCarriersResponse{
					CarrierList: carrierList,
					TMSTenant:   tmsIntegration.Tenant,
				},
			)
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if query.Async {
		go sentry.WithHub(ctx, func(ctx context.Context) {
			asyncCtx := log.InheritContext(ctx, context.Background())
			asyncCtx, cancel := context.WithTimeout(asyncCtx, 5*time.Minute)
			defer cancel()

			carrierList, err = client.GetCarriers(asyncCtx)
			if err != nil {
				log.Error(asyncCtx, "async getCarriers from TMS failed", zap.Error(err))
				return
			}
			log.Info(asyncCtx, "successfully got carriers from TMS", zap.Int("count", len(carrierList)))

			if err = tmsCarrierDB.RefreshTMSCarriers(asyncCtx, tmsIntegration.ServiceID, carrierList); err != nil {
				log.WarnNoSentry(asyncCtx, "error refreshing TMS carriers in db", zap.Error(err))
			}

		})

		// If async request, return query.Limit records from DB to return to FE as a placeholder
		// while we asynchronously add the rest to DB
		endIndex := util.Min(len(carrierList), query.Limit)

		return c.Status(http.StatusAccepted).JSON(
			GetCarriersResponse{
				CarrierList: carrierList[0:endIndex],
				TMSTenant:   tmsIntegration.Tenant,
				Message:     "Asynchronously fetching more customers from TMS",
			},
		)
	}

	// Otherwise, fetch synchronously from TMS
	carrierList, err = client.GetCarriers(ctx)
	if err != nil {
		log.Error(ctx, "getCarriers from TMS failed, falling back to DB", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	if err = tmsCarrierDB.RefreshTMSCarriers(ctx, tmsIntegration.ServiceID, carrierList); err != nil {
		log.WarnNoSentry(ctx, "error refreshing TMS carriers in db", zap.Error(err))
	}

	endIndex := util.Min(len(carrierList), query.Limit)

	return c.Status(http.StatusOK).JSON(
		GetCarriersResponse{
			CarrierList: carrierList[0:endIndex],
			TMSTenant:   tmsIntegration.Tenant,
		},
	)

}
