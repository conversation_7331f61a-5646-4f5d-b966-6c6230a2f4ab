package tmsroutes

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/util"
)

type (
	SearchLocationsQuery = tmsLocationDB.SearchLocationsQuery

	SearchLocationsResponse struct {
		LocationList []models.LocationWithDistance `json:"locationList"`
		TMSTenant    string                        `json:"tmsTenant"`
		Message      string                        `json:"message"`
		// initial city, state, zip info because we search by zip OR city+state and need to fetch
		// complimetary location details from AWS
		Location struct {
			City  string `json:"city"`
			State string `json:"state"`
			Zip   string `json:"zip"`
		} `json:"location"`
	}

	// for location batch operations
	locationKey struct {
		externalTMSID    string
		tmsIntegrationID uint
	}
)

// SearchLocations searches for locations in the DB by name OR by addressLine1 (note: not AND)
// or by radius if MileRadius is provided along with city/state or zip
func SearchLocations(c *fiber.Ctx) error {
	var query SearchLocationsQuery

	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "integration not configured properly",
				zap.Error(err), zap.Uint("service_id", userServiceID))
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integration from DB",
			zap.Error(err), zap.Uint("service_id", userServiceID))
		return err
	}
	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(ctx, "user serviceID %d does not match TMS service ID %d", userServiceID, tmsIntegration.ServiceID)
		return c.SendStatus(http.StatusUnauthorized)
	}
	query.TMSID = tmsIntegration.ID

	// Create TMS client for fetching notes
	client, err := tms.New(ctx, tmsIntegration, models.WithChangeHostName(true))
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var locationList []models.LocationWithDistance

	if query.MileRadius > 0 {
		response, err := handleRadiusSearch(ctx, query, tmsIntegration, client)
		if err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
		if response.Message != "" {
			return c.Status(http.StatusBadRequest).JSON(response)
		}
		return c.Status(http.StatusOK).JSON(response)
	}

	switch {
	case query.Name != "":
		locationList, err = handleNameSearch(ctx, query)
	case query.NameAddress != "":
		locationList, err = handleNameAddressSearch(ctx, query)
	default:
		locationList, err = handleAddressSearch(ctx, query)
	}

	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	if tmsIntegration.Name == models.Turvo {
		locationList = updateLocationNotes(ctx, locationList, client)
	}

	city, state, err := lookupLocationFromZip(ctx, query)
	if err != nil {
		log.WarnNoSentry(ctx, "error looking up location from zip", zap.Error(err))
	}

	return c.Status(http.StatusOK).JSON(
		SearchLocationsResponse{
			LocationList: locationList,
			TMSTenant:    tmsIntegration.Tenant,
			Location: struct {
				City  string `json:"city"`
				State string `json:"state"`
				Zip   string `json:"zip"`
			}{
				City:  city,
				State: state,
				Zip:   query.Zip,
			},
		},
	)
}

// Helper function to handle radius-based location search
func handleRadiusSearch(
	ctx context.Context,
	query SearchLocationsQuery,
	tmsIntegration models.Integration,
	client tms.Interface,
) (*SearchLocationsResponse, error) {

	const geocodeErr = "Could not lookup location, please try again or try another location"

	if (util.IsBlank(query.City) || util.IsBlank(query.State)) && util.IsBlank(query.Zip) {
		return &SearchLocationsResponse{Message: "Please provide a valid zipcode or city/state"}, nil
	}

	pickupLocation, err := util.AwsLocationLookup(ctx, query.City, query.State, query.Zip)
	if err != nil {
		log.Error(ctx, "error geocoding location", zap.Error(err))
		return &SearchLocationsResponse{Message: geocodeErr}, nil
	}

	if pickupLocation.Results[0].Place == nil || pickupLocation.Results[0].Place.Geometry == nil {
		log.Error(ctx, "missing geometry data for pickup", zap.Any("result", pickupLocation.Results[0]))
		return &SearchLocationsResponse{Message: geocodeErr}, nil
	}

	pickupPlace := *pickupLocation.Results[0].Place
	longitude := pickupPlace.Geometry.Point[0]
	latitude := pickupPlace.Geometry.Point[1]

	// Get location details from AWS response
	city := ""
	if pickupPlace.Municipality != nil {
		city = *pickupPlace.Municipality
	}
	state := ""
	if pickupPlace.Region != nil {
		state = util.GetStateAbbreviation(ctx, *pickupPlace.Region)
	}
	zip := ""
	if pickupPlace.PostalCode != nil {
		zip = *pickupPlace.PostalCode
	}

	locationList, err := tmsLocationDB.SearchLocationsByRadius(ctx, query, latitude, longitude)
	if err != nil {
		log.Error(ctx, "error searching locations by radius", zap.Error(err))
		return nil, err
	}

	// Log the number of locations found and how many have ExternalTMSID
	var withExternalID int
	for _, loc := range locationList {
		if loc.ExternalTMSID != "" {
			withExternalID++
		}
	}
	log.Info(
		ctx,
		"found locations in radius search",
		zap.Int("total", len(locationList)),
		zap.Int("with_external_id", withExternalID),
	)

	locationList = updateLocationNotes(ctx, locationList, client)

	return &SearchLocationsResponse{
		LocationList: util.Ternary(len(locationList) == 0, []models.LocationWithDistance{}, locationList),
		TMSTenant:    tmsIntegration.Tenant,
		Location: struct {
			City  string `json:"city"`
			State string `json:"state"`
			Zip   string `json:"zip"`
		}{
			City:  city,
			State: state,
			Zip:   zip,
		},
	}, nil
}

// Helper function to handle name-based location search
func handleNameSearch(ctx context.Context, query SearchLocationsQuery) ([]models.LocationWithDistance, error) {
	locations, err := tmsLocationDB.FuzzySearchByName(ctx, query)
	if err != nil {
		return nil, err
	}
	locationList := make([]models.LocationWithDistance, len(locations))
	for i, loc := range locations {
		locationList[i] = models.LocationWithDistance{TMSLocation: loc}
	}
	return locationList, nil
}

// Helper function to handle name/address-based location search
func handleNameAddressSearch(ctx context.Context, query SearchLocationsQuery) ([]models.LocationWithDistance, error) {
	locations, err := tmsLocationDB.FuzzySearchByNameAddress(ctx, query)
	if err != nil {
		return nil, err
	}
	locationList := make([]models.LocationWithDistance, len(locations))
	for i, loc := range locations {
		locationList[i] = models.LocationWithDistance{TMSLocation: loc}
	}
	return locationList, nil
}

// Helper function to handle address-based location search
func handleAddressSearch(ctx context.Context, query SearchLocationsQuery) ([]models.LocationWithDistance, error) {
	locations, err := tmsLocationDB.FuzzySearchByStreetAddress(ctx, query)
	if err != nil {
		return nil, err
	}
	locationList := make([]models.LocationWithDistance, len(locations))
	for i, loc := range locations {
		locationList[i] = models.LocationWithDistance{TMSLocation: loc}
	}
	return locationList, nil
}

// Helper function to fetch notes for a location from Turvo API
func fetchLocationNotes(ctx context.Context, tmsClient tms.Interface, locationID string) (string, error) {
	if locationID == "" {
		return "", fmt.Errorf("empty location ID")
	}

	id, err := strconv.Atoi(locationID)
	if err != nil {
		return "", fmt.Errorf("invalid location ID: %w", err)
	}

	turvoClient, ok := tmsClient.(*turvo.Turvo)
	if !ok {
		return "", fmt.Errorf("client is not a Turvo client")
	}

	location, err := turvoClient.GetLocationByID(ctx, id, models.WithChangeHostName(true))
	if err != nil {
		return "", fmt.Errorf("failed to get location details: %w", err)
	}

	// Return the combined notes field that includes both special instructions and notes
	return location.Notes, nil
}

// Helper function to update location notes in batch
func updateLocationNotes(
	ctx context.Context,
	locationList []models.LocationWithDistance,
	client tms.Interface,
) []models.LocationWithDistance {

	locationKeys := make([]locationKey, 0, len(locationList))
	locationIndices := make(map[locationKey]int)

	for i := range locationList {
		if locationList[i].Notes == "" && locationList[i].ExternalTMSID != "" {
			key := locationKey{
				externalTMSID:    locationList[i].ExternalTMSID,
				tmsIntegrationID: locationList[i].TMSIntegrationID,
			}
			locationKeys = append(locationKeys, key)
			locationIndices[key] = i
		}
	}

	if len(locationKeys) == 0 {
		return locationList
	}

	locationKeyValues := make([][]any, 0, len(locationKeys))
	for _, key := range locationKeys {
		locationKeyValues = append(locationKeyValues, []any{key.externalTMSID, key.tmsIntegrationID})
	}

	var currentLocations []models.TMSLocation
	err := rds.WithContextReader(ctx).
		Where("(external_tms_id, tms_integration_id) IN ?", locationKeyValues).
		Find(&currentLocations).Error
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching current location data in batch", zap.Error(err))
		return locationList
	}

	locationMap := make(map[locationKey]models.TMSLocation)
	for _, loc := range currentLocations {
		key := locationKey{
			externalTMSID:    loc.ExternalTMSID,
			tmsIntegrationID: loc.TMSIntegrationID,
		}
		locationMap[key] = loc
	}

	locationsToUpdate := make([]models.TMSLocation, 0, len(locationKeys))

	for _, key := range locationKeys {
		notes, err := fetchLocationNotes(ctx, client, key.externalTMSID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error fetching notes for location",
				zap.Error(err),
				zap.String("locationID", key.externalTMSID),
			)
			continue
		}

		currentLocation, exists := locationMap[key]
		if !exists || notes == "" {
			continue
		}

		currentLocation.Notes = notes
		currentLocation.Point = models.Point{
			Latitude:  float32(currentLocation.Latitude),
			Longitude: float32(currentLocation.Longitude),
		}

		locationsToUpdate = append(locationsToUpdate, currentLocation)

		if idx, ok := locationIndices[key]; ok {
			locationList[idx].Notes = notes
		}
	}

	if len(locationsToUpdate) > 0 {
		if err := tmsLocationDB.RefreshTMSLocations(ctx, &locationsToUpdate); err != nil {
			log.WarnNoSentry(
				ctx,
				"error batch updating location notes in database",
				zap.Error(err),
				zap.Int("location_count", len(locationsToUpdate)),
			)
		}
	}

	return locationList
}

// Helper function to lookup location details from zip code
func lookupLocationFromZip(ctx context.Context, query SearchLocationsQuery) (string, string, error) {
	if query.Zip == "" || (query.City != "" && query.State != "") {
		return query.City, query.State, nil
	}

	pickupLocation, err := util.AwsLocationLookup(ctx, query.City, query.State, query.Zip)
	if err != nil || len(pickupLocation.Results) == 0 || pickupLocation.Results[0].Place == nil {
		return query.City, query.State, err
	}

	place := pickupLocation.Results[0].Place
	city := ""
	if place.Municipality != nil {
		city = *place.Municipality
	}
	state := ""
	if place.Region != nil {
		state = util.GetStateAbbreviation(ctx, *place.Region)
	}
	return city, state, nil
}
