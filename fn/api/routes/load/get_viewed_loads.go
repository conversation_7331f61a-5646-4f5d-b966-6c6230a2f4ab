package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetViewedLoadsResponse struct {
		ViewedLoadsHistory []string `json:"viewedLoadsHistory"`
	}
)

func GetViewedLoads(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())

	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var viewedLoads []string
	if len(user.ViewedLoads) > 5 {
		viewedLoads = user.ViewedLoads[len(user.ViewedLoads)-5:]
	} else {
		viewedLoads = user.ViewedLoads
	}

	return c.Status(http.StatusOK).JSON(GetViewedLoadsResponse{
		ViewedLoadsHistory: viewedLoads,
	})
}
