package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	checkCallsDB "github.com/drumkitai/drumkit/common/rds/checkcalls"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/perms"
)

type (
	GetCheckCallPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	GetCheckCallHistory = []models.CheckCall
)

func GetCheckCallsHistory(c *fiber.Ctx) error {
	var path GetCheckCallPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID))
		}
		log.Error(ctx, "get checkcalls loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.With(ctx, zap.String("tmsName", string(load.TMS.Name)))

	if !perms.HasLoadReadPermissions(c, load) {
		err := fmt.Errorf("not allowed to read load %d", load.ID)
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	client, err := tms.New(ctx, load.TMS)
	if err != nil {
		log.Error(ctx, "error building TMS client", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	history, err := client.GetCheckCallsHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		msg := fmt.Sprintf("error getting %s CheckCallHistory", load.TMS.Name)
		log.Error(ctx, msg, zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error getting check calls from TMS")
	}

	if err = checkCallsDB.BatchUpsertCheckCalls(ctx, history); err != nil {
		// Fail-open
		log.ErrorNoSentry(ctx, "error upserting check calls",
			zap.Error(err),
			zap.String("freight_tracking_id", load.FreightTrackingID))
	}

	return c.Status(http.StatusOK).JSON(history)
}
