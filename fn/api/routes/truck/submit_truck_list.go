package truck

import (
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/customer/redwood"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
)

type (
	PostTruckListQuery struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	PostTruckListBody struct {
		ServiceName string                    `json:"serviceName"`
		Carrier     models.CarrierInformation `json:"carrier"`
		Trucks      []CoreTruckObject         `json:"trucks"`
	}

	PostTruckListResponse struct {
		Error        models.TruckListErrors `json:"errors"`
		ErrorMessage string                 `json:"errorMessage"`
	}
)

func SubmitTruckList(c *fiber.Ctx) error {
	var query PostTruckListQuery
	var body PostTruckListBody

	err := api.Parse(c, nil, &query, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	trucklist, err := truckDB.GetTruckListByEmail(ctx, query.EmailID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching truck list by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if trucklist.ServiceID != service.ID {
		log.Warn(ctx, "unauthorized: truck list from email does not belong to service")
		return c.SendStatus(http.StatusUnauthorized)
	}

	updatedTrucks := make(map[uint]CoreTruckObject)
	for _, updatedTruck := range body.Trucks {
		updatedTrucks[updatedTruck.ID] = updatedTruck
	}

	for i, truck := range trucklist.Trucks {
		if updatedTruck, exists := updatedTrucks[truck.ID]; exists {
			if updatedTruck.PickupLocation.City == "" || updatedTruck.PickupLocation.State == "" {
				log.Info(
					ctx,
					"SubmitTruckList - truck failed parsing",
					zap.Uint("truck ID", truck.ID),
					zap.Any("truck list", trucklist),
				)

				msg := fmt.Sprintf("pickup city and state are required for truck ID %d", truck.ID)
				return c.Status(http.StatusBadRequest).SendString(msg)
			}

			// Map pickup date to start date
			trucklist.Trucks[i].PickupDate.Applied = updatedTruck.PickupDate
			trucklist.Trucks[i].PickupLocation.Applied = models.Address{
				City:  updatedTruck.PickupLocation.City,
				State: updatedTruck.PickupLocation.State,
			}

			// Set default dropoff date to one week later if not provided
			if updatedTruck.DropoffDate.Time.IsZero() {
				defaultDropoffDate := updatedTruck.PickupDate.Time.AddDate(0, 0, 7)
				trucklist.Trucks[i].DropoffDate.Applied = models.NullTime{
					Time:  defaultDropoffDate,
					Valid: true,
				}
			} else {
				trucklist.Trucks[i].DropoffDate.Applied = updatedTruck.DropoffDate
			}

			trucklist.Trucks[i].DropoffLocation.Applied = models.Address{
				City:  updatedTruck.DropoffLocation.City,
				State: updatedTruck.DropoffLocation.State,
			}
			trucklist.Trucks[i].Type.Applied = updatedTruck.Type
			trucklist.Trucks[i].Length.Applied = updatedTruck.Length
		}
	}

	// Update carrier information
	trucklist.Carrier = body.Carrier
	if body.Carrier.ExternalTMSID != "" {
		trucklist.Carrier.ExternalTMSID = body.Carrier.ExternalTMSID
	}

	log.Info(ctx, "SubmitTruckList - processing truck list", zap.Any("truck list", trucklist))

	var isDraft bool
	// Truck lists from Redwood should always be submitted as non-drafts,
	// but internal accounts should instead only submit drafts.
	if service.Name == "Redwood" {
		isDraft = false
		integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
		if err != nil {
			log.Error(ctx, "integrationDB.GetByServiceIDAndType error", zap.Error(err))
			return err
		}

		client, err := redwood.New(ctx, integration)
		if err != nil {
			log.Error(ctx, "redwood.New error", zap.Error(err))
			return err
		}

		trucklistErrors, err := client.SubmitTruckList(ctx, isDraft, *trucklist)
		if err != nil {
			log.Error(ctx, "client.SubmitTruckList error", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON("Failed to submit truck list")
		}

		if trucklistErrors != nil {
			log.Info(ctx, "Truck list submitted with errors", zap.Any("errors", trucklistErrors))

			errorMsg := redwood.BuildTruckListErrorMessage(*trucklistErrors)

			response := PostTruckListResponse{
				Error:        *trucklistErrors,
				ErrorMessage: errorMsg,
			}

			return c.Status(http.StatusBadRequest).JSON(response)
		}
		return c.SendStatus(http.StatusAccepted)
	}

	integrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "integrationDB.GetByServiceIDAndType error", zap.Error(err))
		return err
	}

	for _, integration := range integrations {
		if integration.Name == models.Turvo {
			turvoClient := turvo.New(ctx, integration)
			_, err = turvoClient.CreateRoutingGuide(ctx, trucklist)
			if err != nil {
				log.Error(ctx, "turvoClient.CreateRoutingGuide error", zap.Error(err))
				return c.Status(http.StatusInternalServerError).JSON("Failed to submit truck list")
			}
			return c.SendStatus(http.StatusAccepted)
		}
	}

	// If we get here, we didn't find a Turvo or Redwood integration,
	// so we need to return a bad request
	return c.Status(http.StatusBadRequest).SendString("No TMS integration found")
}
