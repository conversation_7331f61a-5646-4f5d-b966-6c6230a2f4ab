package truck

import (
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/customer/redwood"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	ValidateCarrierQuery struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	ValidateCarrierBody struct {
		Carrier models.CarrierInformation `json:"carrier"`
	}

	ValidateCarrierResponse struct {
		Error         models.TruckListErrors `json:"errors"`
		ErrorMessage  string                 `json:"errorMessage"`
		ExternalTMSID string                 `json:"externalTMSID,omitempty"`
	}
)

func ValidateCarrier(c *fiber.Ctx) error {
	var query ValidateCarrierQuery
	var body ValidateCarrierBody

	err := api.Parse(c, nil, &query, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "GetUserByEmail failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if service.Name == "Redwood" {
		integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
		if err != nil {
			log.Error(ctx, "integrationDB.GetByServiceIDAndType error", zap.Error(err))
			return err
		}

		client, err := redwood.New(ctx, integration)
		if err != nil {
			log.Error(ctx, "redwood.New error", zap.Error(err))
			return err
		}

		carrierValidationErrors, err := client.ValidateCarrier(ctx, user, body.Carrier)
		if err != nil {
			log.Error(ctx, "client.ValidateCarrier error", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON("Failed to submit truck list")
		}

		if carrierValidationErrors != nil {
			log.Info(ctx, "Carrier validation with errors", zap.Any("errors", carrierValidationErrors))

			errorMsg := redwood.BuildTruckListErrorMessage(*carrierValidationErrors)

			response := ValidateCarrierResponse{
				Error:        *carrierValidationErrors,
				ErrorMessage: errorMsg,
			}

			return c.Status(http.StatusBadRequest).JSON(response)
		}

		return c.SendStatus(http.StatusAccepted)
	}

	integrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "integrationDB.GetByServiceIDAndType error", zap.Error(err))
		return err
	}

	for _, integration := range integrations {
		if integration.Name == models.Turvo {
			turvoClient := turvo.New(ctx, integration)
			resp, err := turvoClient.SearchCarriers(ctx, body.Carrier.Name, body.Carrier.MC, body.Carrier.DOT)
			if err != nil {
				log.Error(ctx, "turvo.SearchCarriers error", zap.Error(err))
				return c.Status(http.StatusInternalServerError).JSON("Failed to validate carrier with Turvo")
			}
			// Build error response if no carriers found or multiple found
			var errors models.TruckListErrors
			if resp == nil || len(resp.Details.Carriers) == 0 {
				errors.Carrier = map[string][]models.CarrierInformation{"NoCarriersFound": {body.Carrier}}
			} else if len(resp.Details.Carriers) > 1 {
				var carriers []models.CarrierInformation
				for _, c := range resp.Details.Carriers {
					carriers = append(carriers, models.CarrierInformation{
						Name: c.Name,
						MC:   c.McNumber,
						DOT:  fmt.Sprint(c.DotNumber),
					})
				}
				errors.Carrier = map[string][]models.CarrierInformation{"MultipleCarriersFound": carriers}
			}
			if errors.HasErrors() {
				response := ValidateCarrierResponse{
					Error:        errors,
					ErrorMessage: "Carrier validation failed in Turvo.",
				}
				return c.Status(http.StatusBadRequest).JSON(response)
			}
			// If exactly one carrier found, return its ID
			carrierID := ""
			if len(resp.Details.Carriers) == 1 {
				carrierID = fmt.Sprint(resp.Details.Carriers[0].ID)
			}
			return c.Status(http.StatusAccepted).JSON(ValidateCarrierResponse{
				ExternalTMSID: carrierID,
			})
		}
	}

	return c.Status(http.StatusBadRequest).SendString("No TMS integration found")
}
