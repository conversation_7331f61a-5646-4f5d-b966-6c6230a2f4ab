package truck

import (
	"net/http"
	"slices"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
)

type (
	DeleteTruckFromTruckListQuery struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	DeleteTruckFromTruckListPath struct {
		TruckID uint `json:"truckID"`
	}
)

func DeleteTruckFromTruckList(c *fiber.Ctx) error {
	var query DeleteTruckFromTruckListQuery
	var path DeleteTruckFromTruckListPath

	err := api.Parse(c, &path, &query, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	if path.TruckID == 0 {
		log.Warn(ctx, "truck with invalid id cannot be deleted")
		return c.SendStatus(http.StatusBadRequest)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	trucklist, err := truckDB.GetTruckListByEmail(ctx, query.EmailID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching truck list by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if trucklist.ServiceID != service.ID {
		log.Warn(ctx, "unauthorized: truck list from email does not belong to service")
		return c.SendStatus(http.StatusUnauthorized)
	}

	truckToBeDeleted, err := truckDB.GetTruckByID(ctx, path.TruckID)
	if err != nil {
		log.Error(ctx, "error fetching truck by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	err = truckDB.DeleteTruck(ctx, truckToBeDeleted)
	if err != nil {
		log.Error(ctx, "error deleting truck", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Filter out deleted truck from Truck List
	trucklist.Trucks = slices.DeleteFunc(trucklist.Trucks, func(truck models.Truck) bool {
		return truck.ID == path.TruckID
	})

	err = truckDB.UpdateTruckList(ctx, trucklist)
	if err != nil {
		log.Warn(ctx, "failed to update truck list while removing deleted truck association", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusAccepted)
}
