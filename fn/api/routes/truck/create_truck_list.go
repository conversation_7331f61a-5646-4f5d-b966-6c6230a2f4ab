package truck

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
)

type (
	CreateTruckListQuery struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	CreateTruckListBody struct {
		PickupDate string `json:"pickupDate"`
	}

	CreateTruckListResponse struct {
		ServiceName string                    `json:"serviceName"`
		Carrier     models.CarrierInformation `json:"carrier"`
		Trucks      []CoreTruckObject         `json:"trucks"`
		Errors      models.TruckListErrors    `json:"errors"`
	}
)

func CreateTruckList(c *fiber.Ctx) error {
	var query PostTruckListQuery
	var body CreateTruckListBody

	err := api.Parse(c, nil, &query, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	email := middleware.ClaimsFromContext(c).Email
	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "GetUserByEmail failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	parsedEmailID, err := strconv.ParseUint(query.EmailID, 10, 32)
	if err != nil {
		log.Error(ctx, "error converting emailID into uint", zap.Error(err), zap.String("emailID", query.EmailID))
		return c.SendStatus(http.StatusInternalServerError)
	}

	newTruckList := models.TruckList{
		UserID:    user.ID,
		ServiceID: userServiceID,
		EmailID:   uint(parsedEmailID),
		ThreadID:  query.ThreadID,
		Carrier:   models.CarrierInformation{},
		Trucks:    []models.Truck{},
		IsDraft:   false,
	}

	err = truckDB.CreateTruckList(ctx, &newTruckList)
	if err != nil {
		log.Error(ctx, "error creating truck list", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	newEmptyTruck := getEmptyTruckForTruckList(newTruckList, body.PickupDate)

	err = truckDB.CreateTruck(ctx, &newEmptyTruck)
	if err != nil {
		log.Error(ctx, "error creating truck", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	newTruckList.Trucks = []models.Truck{newEmptyTruck}

	err = truckDB.UpdateTruckList(ctx, &newTruckList)
	if err != nil {
		log.Warn(ctx, "failed to update created truck list with empty default truck", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(CreateTruckListResponse{
		ServiceName: service.Name,
		Carrier:     newTruckList.Carrier,
		Trucks:      getTruckCoreListFromTruckList(newTruckList),
		Errors:      newTruckList.Errors,
	})
}
