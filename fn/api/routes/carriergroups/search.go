package carriergroups

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	carrierGroupDB "github.com/drumkitai/drumkit/common/rds/carrier_group"
)

type SearchCarrierGroupsQuery struct {
	Name  string `query:"name"`
	Limit int    `query:"limit"`
}

type CarrierGroupResponse struct {
	ID       uint                 `json:"id"`
	Name     string               `json:"name"`
	Email    string               `json:"email"`
	Carriers []*models.TMSCarrier `json:"carriers,omitempty"`
}

type SearchCarrierGroupsResponse struct {
	CarrierGroups []CarrierGroupResponse `json:"carrierGroups"`
}

func SearchCarrierGroups(c *fiber.Ctx) error {
	var query SearchCarrierGroupsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 100
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	log.Info(ctx, "searching for carrier groups")
	userServiceID := middleware.ServiceIDFromContext(c)

	var fetchedCarrierGroups []models.CarrierGroup
	var err error

	// If name is empty, return all carrier groups for the service
	if query.Name == "" {
		fetchedCarrierGroups, err = carrierGroupDB.GetByServiceID(ctx, userServiceID, query.Limit)
	} else {
		searchQuery := carrierGroupDB.SearchCarrierGroupQuery{
			ServiceID: userServiceID,
			Name:      query.Name,
		}
		fetchedCarrierGroups, err = carrierGroupDB.FuzzySearchByName(ctx, searchQuery, query.Limit)
	}

	if err != nil {
		log.Error(ctx, "error fetching carrier groups from DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	mappedCarrierGroups := make([]CarrierGroupResponse, len(fetchedCarrierGroups))
	for i, cg := range fetchedCarrierGroups {
		mappedCarrierGroups[i] = CarrierGroupResponse{
			ID:       cg.ID,
			Name:     cg.Name,
			Email:    cg.Email,
			Carriers: cg.TMSCarriers,
		}
	}

	return c.Status(http.StatusOK).JSON(SearchCarrierGroupsResponse{
		CarrierGroups: mappedCarrierGroups,
	})
}
