package email

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/services"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/api/util"
)

type (
	IngestOnPremEmailPath struct {
		ThreadID string `params:"id" validate:"required"`
	}

	// Incoming request body from API
	IngestOnPremEmailRequestBody struct {
		EmailAddress string `json:"emailAddress" validate:"required"`
	}

	// Outgoing request body to OnPrem
	IngestOnPremEmailForwardBody struct {
		ThreadID      string               `json:"threadId"`
		EmailProvider models.EmailProvider `json:"emailProvider"`
		UserID        uint                 `json:"userId"`
	}

	IngestOnPremEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

// IngestOnPremEmail manually ingests an email where the user comes from a customer who self-hosts Drumkit.
func IngestOnPremEmail(c *fiber.Ctx) error {
	var err error
	var path IngestOnPremEmailPath
	var body IngestOnPremEmailRequestBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	path.ThreadID, err = util.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user, err := userDB.GetByEmail(ctx, body.EmailAddress)
	if err != nil {
		return util.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	ctx = log.With(
		ctx,
		zap.Uint("userId", user.ID),
		zap.String("emailAddress", user.EmailAddress),
		zap.Uint("serviceId", user.ServiceID),
		zap.String("serviceName", user.Service.Name),
	)

	log.Info(ctx, "received manual email ingestion request from customer that self-hosts drumkit")

	ingestURL, err := services.GetOnPremIngestEmailURL(user.Service.Name)
	if err != nil {
		log.Error(ctx, "failed to get customer ingestion URL", zap.Error(err))

		return c.Status(http.StatusBadRequest).JSON(IngestOnPremEmailResponse{
			Message: "failed to get customer ingestion URL",
		})
	}

	fullURL := ingestURL

	if strings.ToLower(user.Service.Name) == "redwood" {
		queryParams := url.Values{}
		queryParams.Set("code", user.Service.OnPremAuthToken)

		fullURL = ingestURL + "?" + queryParams.Encode()
	}

	ingestBody := IngestOnPremEmailForwardBody{
		ThreadID:      path.ThreadID,
		EmailProvider: user.EmailProvider,
		UserID:        user.OnPremID,
	}

	payload, err := json.Marshal(ingestBody)
	if err != nil {
		log.Error(ctx, "failed to marshal payload for email ingestion request", zap.Error(err))

		return c.Status(http.StatusBadRequest).JSON(IngestOnPremEmailResponse{
			Message: "failed to marshal payload for email ingestion request",
		})
	}

	log.Info(ctx, "Sending manual email ingestion request", zap.String("payload", string(payload)))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL, bytes.NewBuffer(payload))
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send email ingestion request",
			zap.String("customer", user.Service.Name),
			zap.Error(err),
		)

		return c.Status(http.StatusBadRequest).JSON(IngestOnPremEmailResponse{
			Message: "failed to construct http request to send email ingestion request",
		})
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(
			ctx,
			"failed to send http request for email ingestion request",
			zap.String("customer", user.Service.Name),
			zap.Error(err),
		)

		return err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error(ctx, "failed to read response body", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(IngestOnPremEmailResponse{
			Message: "failed to read response from ingestion service",
		})
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		log.Error(
			ctx,
			"unexpected response from ingestion service",
			zap.Int("statusCode", resp.StatusCode),
			zap.String("response", string(respBody)),
		)

		return c.Status(resp.StatusCode).JSON(IngestOnPremEmailResponse{
			Message: fmt.Sprintf("ingestion service returned error: %s", string(respBody)),
		})
	}

	log.Info(ctx, "successfully ingested onprem email")

	return c.Status(http.StatusOK).JSON(IngestOnPremEmailResponse{
		Message: "email ingested successfully",
	})
}
