package appt

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehousesDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetWarehousesRecentResponse struct {
		Warehouses []models.WarehouseCore `json:"warehouses"`
	}
)

func GetWarehousesRecent(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())

	userServiceID := middleware.ServiceIDFromContext(c)

	resp, err := getWarehousesRecent(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "getWarehousesRecent failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getWarehousesRecent(ctx context.Context, serviceID uint) (*GetWarehousesBySourceResponse, error) {
	warehouses, err := warehousesDB.GetRecentWarehousesByService(ctx, serviceID)
	if err != nil {
		log.Error(ctx, "error fetching warehouse list by service loads", zap.Error(err))
		return nil, fmt.Errorf("error getting warehouses for service loads: %w", err)
	}

	var warehousesResult []models.WarehouseCore
	for _, wh := range warehouses {
		warehousesResult = append(warehousesResult, util.GetWarehouseCoreFromWarehouse(wh))
	}

	return &GetWarehousesBySourceResponse{
		Warehouses: warehousesResult,
	}, nil
}
