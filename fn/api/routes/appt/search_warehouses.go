package appt

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehousesDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetWarehousesBySearchQuery struct {
		Search string `json:"search" validate:"required"`
	}

	GetWarehousesBySearchResponse struct {
		Warehouses []models.WarehouseCore `json:"warehouses"`
	}
)

func GetWarehousesBySearch(c *fiber.Ctx) error {
	var query GetWarehousesBySearchQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)

	resp, err := getWarehousesBySearch(ctx, userServiceID, query.Search)
	if err != nil {
		log.Error(ctx, "getWarehousesBySearch failed", zap.Error(err), zap.String("search", query.Search))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getWarehousesBySearch(ctx context.Context, serviceID uint, search string) (*GetWarehousesBySourceResponse, error) {
	warehouses, err := warehousesDB.GetWarehousesBySearch(ctx, serviceID, search)
	if err != nil {
		log.Error(ctx, "error fetching getWarehouses", zap.Error(err))
		return nil, fmt.Errorf("error getting warehouses for integration: %w", err)
	}

	var warehousesResult []models.WarehouseCore
	for _, wh := range warehouses {
		warehousesResult = append(warehousesResult, util.GetWarehouseCoreFromWarehouse(wh))
	}

	return &GetWarehousesBySourceResponse{
		Warehouses: warehousesResult,
	}, nil
}
