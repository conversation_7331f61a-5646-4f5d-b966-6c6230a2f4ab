package appt

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehousesDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetWarehousesBySourceQuery struct {
		Source models.WarehouseSource `json:"source"`
	}

	GetWarehousesBySourceResponse struct {
		Warehouses []models.WarehouseCore `json:"warehouses"`
	}
)

// Deprecated; FE now only uses /warehouses/recent and /warehouses/search
func GetWarehousesBySource(c *fiber.Ctx) error {
	var query GetWarehousesBySourceQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	log.Info(ctx, "fetching warehouses")

	resp, err := getWarehousesBySource(ctx, userServiceID, query.Source)
	if err != nil {
		log.Error(ctx, "getWarehouse failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Debug(ctx, "warehouses", zap.Any("warehouses", resp))

	return c.Status(http.StatusOK).JSON(resp)
}

func getWarehousesBySource(
	ctx context.Context,
	serviceID uint,
	source models.WarehouseSource,
) (*GetWarehousesBySourceResponse, error) {

	warehouses, err := warehousesDB.GetWarehousesBySource(ctx, serviceID, source)
	if err != nil {
		log.Error(ctx, "error fetching getWarehouses", zap.Error(err))
		return nil, fmt.Errorf("error getting warehouses for integration: %w", err)
	}

	var warehousesResult []models.WarehouseCore
	for _, wh := range warehouses {
		warehousesResult = append(warehousesResult, util.GetWarehouseCoreFromWarehouse(wh))
	}

	return &GetWarehousesBySourceResponse{
		Warehouses: warehousesResult,
	}, nil
}
