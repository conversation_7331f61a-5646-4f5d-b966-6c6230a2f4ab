package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/perms"
)

type (
	MakeAppointmentBody struct {
		FreightTrackingID string `json:"freightTrackingID"` // Required for appts scheduled through Load view

		// If DryRun=true, the appt is created in the DB but not submitted to Scheduler or TMS
		// Useful for testing appointment creation without spamming Scheduler with appointment reservations
		DryRun bool `json:"dryRun"`

		IsTMSLoad       bool      `json:"isTMSLoad"` // Indicates if a appt is associated with a load in TMS
		StartTime       time.Time `json:"start" validate:"required"`
		WarehouseID     string    `json:"warehouseID"`
		DockID          string    `json:"dockId" validate:"required"`
		LoadTypeID      string    `json:"loadTypeId" validate:"required"`
		SubscribedEmail string    `json:"subscribedEmail"`
		CcEmails        []string  `json:"ccEmails"`
		//nolint:lll
		TrailerType string `json:"trailerType" validate:"omitempty,oneof='Cargo Van' 'LTL/FTL Trailer' 'Passenger Vehicle' 'Straight Truck'"`
		Notes       string `json:"notes"`
		PONums      string `json:"poNums"`
		RefNumber   string `json:"refNumber"` // Opendock-specific reference number, required by some warehouses
		models.CustomApptFieldsTemplate
	}

	MakeAppointmentResponse = models.Appointment
)

var newYorkLoc *time.Location

// NOTE: Only supports NFI + Aljex + Opendock for now
func MakeV1(c *fiber.Ctx) error {
	var body MakeAppointmentBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))

	// TODO: authorize the claim for different customers
	// For now, we just verify that the email in the claim is either axle or NFI
	email := middleware.ClaimsFromContext(c).Email
	if !strings.HasSuffix(email, "@nfiindustries.com") && !perms.IsInternalEmail(ctx, email) {
		log.WarnNoSentry(ctx, "unauthorized: email is not from NFI nor Axle")
		return c.SendStatus(http.StatusUnauthorized)
	}

	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "GetUserByEmail failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	body.CcEmails = append(body.CcEmails, body.SubscribedEmail)

	var backwardsCompatibilityCheck = body.WarehouseID == ""
	var tmsClient tms.Interface
	var load models.Load

	loadFromDB, err := loadDB.GetLoadByFreightIDAndService(ctx, user.ServiceID, body.FreightTrackingID)
	if err != nil {
		// fallback: if load is not on DB, match TMS by FreightID and look it up on TMS
		tmsIntegration, err := integrationDB.MatchTMSByServiceAndFreightID(
			ctx,
			user.ServiceID,
			body.FreightTrackingID,
		)
		if err != nil {
			return fmt.Errorf("could not get tms integration: %w", err)
		}

		load, err = getLoadByID(ctx, *tmsIntegration, body.FreightTrackingID)
		if err != nil {
			return err
		}
	} else {
		load = loadFromDB
	}

	if body.IsTMSLoad || backwardsCompatibilityCheck {
		tmsClient, err = tms.New(ctx, load.TMS)
		if err != nil {
			log.Error(ctx, "error creating TMS client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		if !perms.HasLoadReadWritePermissions(c, load, tmsClient) {
			const msg = "not allowed to schedule production load"

			log.ErrorNoSentry(ctx, msg)

			return c.Status(http.StatusForbidden).SendString(msg)
		}
	}

	resp, err := makeAppt(
		ctx,
		user.ID,
		user.ServiceID,
		email,
		&body,
		load.TMS,
		tmsClient,
		backwardsCompatibilityCheck,
	)
	if err != nil {
		log.Error(ctx, "makeAppt failed", zap.Error(err))

		// TODO: Check for existing appt preemptively
		// We ran into this error while demoing for NFI, and got the feedback that it likely happened because
		// Opendock already had an appointment scheduled for that ref number.
		// We treat this error code differently now to warn the frontend and show a descriptive error message on
		// a toast, asking the user to check for existing appointments.

		var httpErr errtypes.HTTPResponseError
		if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusConflict {
			return c.SendStatus(http.StatusConflict)
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func makeAppt(
	ctx context.Context,
	userID,
	userServiceID uint,
	email string,
	body *MakeAppointmentBody,
	tmsIntegration models.Integration,
	tmsClient tms.Interface,
	backwardsCompatibilityCheck bool,
) (*MakeAppointmentResponse, error) {

	log.Info(ctx, "processing makeAppt")

	var apptRecord models.Appointment
	var curLoad models.Load
	var err error

	if body.IsTMSLoad || backwardsCompatibilityCheck {
		curLoad, _, err = tmsClient.GetLoad(ctx, body.FreightTrackingID)
		if err != nil {
			return nil, err
		}
	}

	if body.DryRun {
		now := time.Now()
		apptRecord = models.Appointment{
			Account:           email,
			ConfirmationNo:    strconv.Itoa(-int(now.Unix())),
			Date:              body.StartTime.Format(time.DateOnly),
			DockID:            body.DockID,
			ExternalID:        strconv.Itoa(-int(now.Unix())),
			FreightTrackingID: body.FreightTrackingID,
			PONums:            body.PONums,
			RefNumber:         body.RefNumber,
			LoadTypeID:        body.LoadTypeID,
			StartTime:         body.StartTime,
			Status:            "{axleDryRun: true}",
			WarehouseID:       body.WarehouseID,
		}
		log.Info(ctx, "DryRun: skipping appointment creation")
	} else {
		integration, err := integrationDB.GetSchedulerByServiceAndUserID(ctx, userID, userServiceID)
		if err != nil {
			log.Error(ctx, "error fetching integration for warehouse on get_slots", zap.Error(err))
			return nil, fmt.Errorf("unable to fetch to integration: %w", err)
		}

		client, err := scheduling.GetCachedClient(ctx, integration)
		if err != nil {
			return nil, fmt.Errorf("unable to connect: %w", err)
		}

		apptReq := models.MakeAppointmentRequest{
			CcEmails:   body.CcEmails,
			DockID:     body.DockID,
			LoadTypeID: body.LoadTypeID,
			Notes:      body.Notes,
			RefNumber:  body.RefNumber,
			StartTime:  body.StartTime,
		}

		if body.IsTMSLoad {
			apptRecord, err = client.MakeAppointmentWithLoad(
				ctx,
				apptReq,
				curLoad,
			)
		} else {
			apptRecord, err = client.MakeAppointment(
				ctx,
				apptReq,
			)
		}
		if err != nil {
			return nil, err
		}

		ctx = log.With(
			ctx,
			zap.String("ApptId", strconv.FormatUint(uint64(apptRecord.ID), 10)),
			zap.String("ConfNo", apptRecord.ConfirmationNo),
		)

		log.Info(ctx, " apptRecord created successfully")
	}

	apptRecord.Account = email
	apptRecord.FreightTrackingID = body.FreightTrackingID
	apptRecord.PONums = body.PONums
	apptRecord.ServiceID = userServiceID

	// TODO: These fields (DockID, WarehouseID, Date, StartTime) are currently being set both here and in the
	// integration implementation (e.g., Opendock.MakeAppointment).  Once we verify the integration is properly
	// setting and persisting these fields, we can remove this duplicate assignment.
	apptRecord.DockID = body.DockID
	apptRecord.WarehouseID = body.WarehouseID
	apptRecord.Date = body.StartTime.Format(time.DateOnly)
	apptRecord.StartTime = body.StartTime

	if err := apptDB.Create(ctx, &apptRecord); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Any("record", &apptRecord), zap.Error(err))
	}

	ctx = log.With(ctx, zap.Uint("apptRecordId", apptRecord.ID))
	log.Info(ctx, "appt DB record added")

	// If a 7/11 curLoad is associated with this appointment, update TMS
	reqLoad := curLoad
	if body.IsTMSLoad && curLoad.FreightTrackingID != "" {
		// Update Aljex with appointment details
		// TODO: Handle the fact that O&M appt notes are formatted {BOL/CustomerRef} - Opendock Conf No
		reqLoad.Consignee.ApptNote = "Opendock " + apptRecord.ConfirmationNo
		reqLoad.Consignee.ApptStartTime = models.NullTime{Time: body.StartTime, Valid: true}

		// HACK: 7/11's warehouse is in Eastern TZ (see Opendock.GetWarehouse), which is what Opendock displays
		// all their appt timestamps in, regardless of the user's locale. Normalize timestamp so Aljex time
		// matches Opendock.
		if tmsIntegration.Name == models.Aljex {
			if newYorkLoc == nil {
				newYorkLoc, err = time.LoadLocation("America/New_York")
				if err != nil {
					log.Error(ctx, "error loading location", zap.Error(err))

					// Fail-open
					return &apptRecord, nil
				}

			}

			nyTime := body.StartTime.In(newYorkLoc) // Use .In() in case JSON timestamp had an offset
			tempNormalizedTime := time.Date(nyTime.Year(), nyTime.Month(), nyTime.Day(),
				nyTime.Hour(), nyTime.Minute(), nyTime.Second(), nyTime.Nanosecond(), time.UTC)

			reqLoad.Consignee.ApptStartTime = models.NullTime{Time: tempNormalizedTime, Valid: true}
		}

		if body.DryRun {
			log.Info(ctx, "DryRun: skipping Aljex update")
		} else {
			updatedLoad, _, err := tmsClient.UpdateLoad(ctx, &curLoad, &reqLoad)
			if err != nil {
				// fail-open
				log.Error(ctx, "aljex update load with appt details failed", zap.Error(err))
			} else {
				log.Info(ctx, "aljex updated with appt details")

				if err = loadDB.UpsertLoad(ctx, &updatedLoad, &tmsIntegration); err != nil {
					log.Error(ctx, "error updating load DB with appt details", zap.Error(err))
				}
			}
		}
	}

	return &apptRecord, nil
}
