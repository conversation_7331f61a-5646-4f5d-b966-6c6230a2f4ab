package appt

import (
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestGetWarehousesBySource(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetWarehousesBySourceQuery{
			Source: "opendock",
		}

		if err := validator.TestQuery(query); err != nil {
			t.<PERSON>("Expected no error, got: %v", err)
		}
	})
}
