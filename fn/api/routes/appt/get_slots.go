package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	GetOpenSlotsQuery struct {
		FreightTrackingID string `json:"freightTrackingID"`
		LoadTypeID        string `json:"loadTypeID" validate:"required"`
		TrailerType       string `json:"trailerType"`
		DockID            string `json:"dockID"`
		models.GetOpenSlotsRequest
		Source string `json:"source"` // TODO: limit type to scheduling integrations
	}

	GetOpenSlotsResponse struct {
		GetOpenSlotsQuery
		Warehouse models.Warehouse `json:"warehouse"`
		LoadType  models.LoadType  `json:"loadType"`
		Slots     []models.Slot    `json:"slots"`
	}
)

func GetOpenSlots(c *fiber.Ctx) error {
	var query GetOpenSlotsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))

	// TODO: remove this when the frontend passes in a nonempty source
	if query.Source == "" {
		query.Source = string(models.OpendockSource)
	}

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(ctx, userID, userServiceID, query.Source)
	if err != nil {
		log.Error(ctx, "error fetching integration id for warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := getSlots(ctx, &query, query.WarehouseID, integration)
	if err != nil {
		errStr := strings.ToLower(err.Error())

		isNotFound := strings.Contains(errStr, "not found") || strings.Contains(errStr, "404")
		if isNotFound {
			log.WarnNoSentry(ctx, "getSlots failed - warehouse not found", zap.String("err", errStr))
			return c.Status(http.StatusNotFound).SendString(errStr)
		}

		if strings.Contains(errStr, "not supported") {
			log.WarnNoSentry(ctx, "getSlots failed", zap.String("err", errStr))
			return c.Status(http.StatusUnprocessableEntity).SendString(errStr)
		}

		log.Error(ctx, "getSlots failed", zap.Error(err))

		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"message": "Live appointments are not available at this time",
		})
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getSlots(
	ctx context.Context,
	query *GetOpenSlotsQuery,
	warehouseID string,
	integration models.Integration,
) (*GetOpenSlotsResponse, error) {

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to %s: %w", query.Source, err)
	}

	var warehouse models.Warehouse
	var loadType models.LoadType
	var slots []models.Slot

	switch integration.Name {
	case models.E2open:
		if query.FreightTrackingID == "" {
			return nil, fmt.Errorf("PRO ID is required for E2open")
		}

		slots, err = client.GetOpenSlots(ctx, query.FreightTrackingID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		key := fmt.Sprintf("%s-%s-%s", models.E2openSource, query.FreightTrackingID, query.RequestType)

		whName, err := redis.RDB.Get(ctx, key).Result()
		if err == nil && whName != "" {
			warehouse = models.Warehouse{
				WarehouseID:   whName,
				WarehouseName: whName,
				Source:        models.E2openSource,
			}
		} else {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis - falling back to db",
				zap.String("source", string(models.E2openSource)),
				zap.String("freightTrackingID", query.FreightTrackingID),
				zap.Error(err),
			)

			// NOTE: E2open warehouses have no IDs so we use freightTrackingID for storing and lookups
			wh, err := warehouseDB.GetWarehouseByIDAndSource(
				ctx,
				integration.ServiceID,
				models.E2openSource,
				query.FreightTrackingID,
			)
			if err != nil {
				return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
			}

			if err := redis.RDB.Set(ctx, key, wh.WarehouseName, 24*time.Hour).Err(); err != nil {
				log.WarnNoSentry(
					ctx,
					"error setting warehouse in redis",
					zap.String("source", string(models.E2openSource)),
					zap.String("freightTrackingID", query.FreightTrackingID),
					zap.String("name", wh.WarehouseName),
					zap.Error(err),
				)
			}
		}

	case models.Retalix:
		// Validate Retalix-specific requirements
		if len(query.PONumbers) == 0 {
			return nil, fmt.Errorf("PO numbers are required for Retalix")
		}

		if query.Warehouse.WarehouseID == "" {
			return nil, fmt.Errorf("warehouse ID is required for Retalix")
		}

		if query.Warehouse.WarehouseName == "" {
			return nil, fmt.Errorf("warehouse name is required for Retalix")
		}

		// Retalix warehouses are available in the database after onboarding flow
		wh, err := warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			integration.ServiceID,
			models.RetalixSource,
			query.Warehouse.WarehouseID,
		)
		if err != nil {
			return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
		}

		warehouse = *wh

		// Retalix doesn't need warehouse/loadtype calls
		slots, err = client.GetOpenSlots(ctx, "", query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.Opendock:
		warehouse, err = client.GetWarehouse(ctx, warehouseID)
		if err != nil {
			return nil, fmt.Errorf("%s.GetWarehouse failed: %w", query.Source, err)
		}

		loadTypes, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{
			WarehouseID: warehouseID,
			Search:      `{"allowCarrierScheduling":true}`,
		})
		if err != nil {
			return nil, fmt.Errorf("%s.GetLoadTypes failed: %w", query.Source, err)
		}

		// Find the loadType with the matching name
		for _, lt := range loadTypes {
			if strings.EqualFold(lt.ID, query.LoadTypeID) {
				loadType = lt
				break
			}
		}

		if loadType.ID == "" {
			return nil, fmt.Errorf("loadType '%s' not found for warehouse %s", query.LoadTypeID, warehouseID)
		}

		query.IncludeStartTimes = true
		log.Info(ctx, "get open slots request object", zap.Any("query", query.GetOpenSlotsRequest))

		slots, err = client.GetOpenSlots(ctx, loadType.ID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.YardView:
		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	default:
		return nil, fmt.Errorf("unsupported integration: %s", integration.Name)
	}

	// Filter slots by dock if specified
	slotsResponse := make([]models.Slot, 0)
	if query.DockID != "" && integration.Name != models.E2open && integration.Name != models.YardView {
		for _, s := range slots {
			if s.Dock.ID == query.DockID {
				slotsResponse = append(slotsResponse, s)
			}
		}
	} else {
		slotsResponse = slots
	}

	// Ensure warehouse info is set on all slots
	for i := range slotsResponse {
		slotsResponse[i].Warehouse = warehouse
	}

	return &GetOpenSlotsResponse{
		GetOpenSlotsQuery: *query,
		Warehouse:         warehouse,
		LoadType:          loadType,
		Slots:             slotsResponse,
	}, nil
}

func getLoadByID(ctx context.Context, integration models.Integration, freightID string) (models.Load, error) {
	client, err := tms.New(ctx, integration)
	if err != nil {
		return models.Load{}, fmt.Errorf("error building TMS client: %w", err)
	}

	load, _, err := client.GetLoad(ctx, freightID)
	return load, err
}
