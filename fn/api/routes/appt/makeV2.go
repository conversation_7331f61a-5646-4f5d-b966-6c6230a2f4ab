package appt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/util/perms"
)

type (
	BodyV2 struct {
		LoadID            uint   `json:"loadID" validate:"required"`
		FreightTrackingID string `json:"freightTrackingID" validate:"required"`
		Source            string `json:"source"` // TODO: limit type to scheduling integrations
		// If DryRun=true, the appt is created in the DB but not submitted to Scheduler or TMS
		// Useful for testing appointment creation without spamming Scheduler with appointment reservations
		DryRun bool `json:"dryRun"`

		StopType          string    `json:"stopType" validate:"required,oneof=pickup dropoff"`
		StartTime         time.Time `json:"start" validate:"required"`
		WarehouseID       string    `json:"warehouseID" validate:"required"`
		WarehouseTimezone string    `json:"warehouseTimezone" validate:"required"` // IANA, e.g. America/New_York
		DockID            string    `json:"dockId" validate:"required"`
		LoadTypeID        string    `json:"loadTypeId" validate:"required"`
		SubscribedEmail   string    `json:"subscribedEmail"`
		CcEmails          []string  `json:"ccEmails"`
		//nolint:lll
		TrailerType string `json:"trailerType" validate:"omitempty,oneof='Cargo Van' 'LTL/FTL Trailer' 'Passenger Vehicle' 'Straight Truck'"`
		Notes       string `json:"notes"`
		PONums      string `json:"poNums"`
		// Opendock-specific reference number, required by some warehouses
		RefNumber string `json:"refNumber"`
		models.CustomApptFieldsTemplate
		// E2open-specific request type
		RequestType models.RequestType `json:"requestType"`
	}

	ResponseV2 struct {
		models.Appointment
		IsAppointmentTMSUpdateEnabled bool `json:"isAppointmentTMSUpdateEnabled"`
		TMSUpdateSucceeded            bool `json:"tmsUpdateSucceeded"`
		// User-facing message, typically used if creating the Scheduler appt succeeded but not the TMS update
		Message string `json:"message"`
	}
)

// MakeV2 handles appointment creation for both Opendock and Retalix scheduling integrations.
// For Opendock appointments, it also updates the TMS with the appointment details.
// For Retalix appointments, it only creates the appointment without TMS integration *for now*.
func MakeV2(c *fiber.Ctx) error {
	var body BodyV2
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Set default source to Opendock if empty
	if body.Source == "" {
		body.Source = string(models.Opendock)
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}
	body.CcEmails = append(body.CcEmails, body.SubscribedEmail)

	switch body.Source {
	case string(models.E2open):
		return handleE2openAppointment(c, user, &body)

	case string(models.Opendock):
		// Handle Opendock + TMS flow
		tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, body.FreightTrackingID)
		if err != nil {
			return err
		}

		tmsClient, err := tms.New(ctx, *tmsIntegration)
		if err != nil {
			log.Error(ctx, "error creating TMS client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		load := models.Load{FreightTrackingID: body.FreightTrackingID, ServiceID: user.ServiceID}

		// drumkit.ai is not allowed to schedule real PROs
		if !body.DryRun &&
			!perms.HasLoadReadWritePermissions(c, load, tmsClient) {
			const msg = "not allowed to schedule production load"

			log.ErrorNoSentry(ctx, msg)
			return c.Status(http.StatusForbidden).SendString(msg)
		}

		return handleOpendockAppointment(c, user, &body, *tmsIntegration, tmsClient)

	case string(models.Retalix):
		// Handle Retalix-only flow
		// TODO: Update Relay once we've confirmed with NFI on how they want us to update it

		// TODO: uncomment code after testing
		// drumkit.ai is not allowed to schedule real PROs
		// if perms.IsInternalEmail(ctx, email) {
		// 	const msg = "not allowed to schedule production PRO"

		// 	log.ErrorNoSentry(ctx, msg)
		// 	return c.Status(http.StatusForbidden).SendString(msg)
		// }

		return handleRetalixAppointment(c, user, &body)

	case string(models.YardView):
		return handleYardViewAppointment(c, user, &body)

	default:
		return c.Status(http.StatusBadRequest).SendString("unsupported scheduling integration")
	}
}

func handleOpendockAppointment(
	c *fiber.Ctx,
	user models.User,
	body *BodyV2,
	tmsIntegration models.Integration,
	tmsClient tms.Interface,
) error {

	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Time("apptTime", body.StartTime),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", body.Source),
	)

	apptRecord, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to create appointment", zap.Error(err))

		// Check for conflict specifically
		var httpErr errtypes.HTTPResponseError
		if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusConflict {
			return c.Status(http.StatusConflict).JSON(&ResponseV2{
				Message: "An appointment already exists for this reference number. Please check existing appointments.",
			})
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &apptRecord); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil || !service.IsAppointmentTMSUpdateEnabled {
		if err != nil {
			log.Warn(ctx, "failed to get feature flag after appointment creation", zap.Error(err))
		}

		// If there's a transient error getting the service feature flag,
		// err on the side of caution and let the user update the TMS manually
		return c.Status(http.StatusOK).JSON(&ResponseV2{
			Appointment:                   apptRecord,
			IsAppointmentTMSUpdateEnabled: service.IsAppointmentTMSUpdateEnabled,
			TMSUpdateSucceeded:            false,
		})
	}

	if body.DryRun {
		return c.Status(http.StatusOK).JSON(&ResponseV2{
			Appointment:                   apptRecord,
			IsAppointmentTMSUpdateEnabled: service.IsAppointmentTMSUpdateEnabled,
			TMSUpdateSucceeded:            service.IsAppointmentTMSUpdateEnabled,
		})
	}

	resp := &ResponseV2{
		Appointment:                   apptRecord,
		IsAppointmentTMSUpdateEnabled: service.IsAppointmentTMSUpdateEnabled,
	}

	curLoad, _, err := tmsClient.GetLoad(ctx, body.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "failed to get load from TMS for appt update", zap.Error(err))

		return c.Status(http.StatusOK).JSON(resp)
	}

	err = updateTMSWithAppointment(ctx, body, curLoad, apptRecord, tmsIntegration, body.Source, tmsClient)
	if err != nil {
		return c.Status(http.StatusOK).JSON(resp)
	}

	resp.TMSUpdateSucceeded = true
	return c.Status(http.StatusOK).JSON(resp)
}

func handleRetalixAppointment(c *fiber.Ctx, user models.User, body *BodyV2) error {
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Time("apptTime", body.StartTime),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", body.Source),
	)

	// Create appointment record without TMS update for now
	apptRecord, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to create appointment", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &apptRecord); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(&ResponseV2{
		Appointment:                   apptRecord,
		Message:                       "Appointment created successfully. Be sure to update your TMS.",
		IsAppointmentTMSUpdateEnabled: false, // False for Retalix rn regardless of service feature flag
		TMSUpdateSucceeded:            false,
	})
}

func handleE2openAppointment(c *fiber.Ctx, user models.User, body *BodyV2) error {
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Time("apptTime", body.StartTime),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", body.Source),
	)

	apptRecord, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID)
	if err != nil {
		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		log.Error(ctx, "failed to create appointment", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &apptRecord); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(&ResponseV2{
		Appointment:                   apptRecord,
		Message:                       "Appointment created successfully",
		IsAppointmentTMSUpdateEnabled: false,
		TMSUpdateSucceeded:            false,
	})
}

func handleYardViewAppointment(c *fiber.Ctx, user models.User, body *BodyV2) error {
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Time("apptTime", body.StartTime),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", body.Source),
	)

	apptRecord, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID)
	if err != nil {
		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		log.Error(ctx, "failed to create appointment", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &apptRecord); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(&ResponseV2{
		Appointment:                   apptRecord,
		Message:                       "Appointment created successfully",
		IsAppointmentTMSUpdateEnabled: false,
		TMSUpdateSucceeded:            false,
	})
}

func getTMSIntegration(ctx context.Context, serviceID uint, freightID string) (*models.Integration, error) {
	loadFromDB, err := loadDB.GetLoadByFreightIDAndService(ctx, serviceID, freightID)
	if err == nil {
		return &loadFromDB.TMS, nil
	}

	// Fallback to direct TMS lookup if load not in DB
	tmsIntegration, err := integrationDB.MatchTMSByServiceAndFreightID(ctx, serviceID, freightID)
	if err != nil {
		return nil, fmt.Errorf("could not get tms integration: %w", err)
	}

	return tmsIntegration, nil
}

func createAppointment(
	ctx context.Context,
	body *BodyV2,
	email string,
	userID,
	userServiceID uint,
) (models.Appointment, error) {

	if body.DryRun {
		return createDryRunAppointment(body, email), nil
	}

	return createRealAppointment(ctx, body, email, userID, userServiceID)
}

func createDryRunAppointment(body *BodyV2, email string) models.Appointment {
	now := time.Now()
	return models.Appointment{
		Account:           email,
		FreightTrackingID: body.FreightTrackingID,
		ExternalID:        strconv.Itoa(-int(now.Unix())),
		ConfirmationNo:    strconv.Itoa(-int(now.Unix())),
		WarehouseID:       body.WarehouseID,
		LoadTypeID:        body.LoadTypeID,
		DockID:            body.DockID,
		PONums:            body.PONums,
		RefNumber:         body.RefNumber,
		Date:              body.StartTime.Format(time.DateOnly),
		StartTime:         body.StartTime,
		Status:            "{drumkitDryRun: true}",
	}
}

// createRealAppointment handles the actual appointment creation through the scheduling integration.
// For Retalix, it requires additional warehouse information to be passed as a scheduling option.
func createRealAppointment(
	ctx context.Context,
	body *BodyV2,
	email string,
	userID,
	userServiceID uint,
) (models.Appointment, error) {

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(ctx, userID, userServiceID, body.Source)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("fetch integration: %w", err)
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("create scheduler client: %w", err)
	}

	apptReq := models.MakeAppointmentRequest{
		CcEmails:     body.CcEmails,
		CustomFields: body.CustomApptFieldsTemplate,
		DockID:       body.DockID,
		LoadTypeID:   body.LoadTypeID,
		Notes:        body.Notes,
		RefNumber:    body.RefNumber,
		StartTime:    body.StartTime,
	}

	var warehouse *models.Warehouse
	var opts []models.SchedulingOption

	switch body.Source {
	case string(models.E2open):
		apptReq.RequestType = string(body.RequestType)
		apptReq.LoadTypeID = body.FreightTrackingID

	case string(models.Retalix):
		apptReq.PONums = body.PONums

		// Get warehouse details for Retalix
		warehouse, err := warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			models.RetalixSource,
			body.WarehouseID,
		)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("fetch warehouse: %w", err)
		}

		opts = append(opts, models.WithWarehouse(
			models.Warehouse{
				WarehouseID:   warehouse.WarehouseID,
				WarehouseName: warehouse.WarehouseName,
			},
		))

	case string(models.YardView):
		apptReq.RequestType = string(body.RequestType)
		apptReq.WarehouseID = body.WarehouseID
	}

	appt, err := client.MakeAppointment(ctx, apptReq, opts...)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("create appointment: %w", err)
	}

	if len(appt.Status) > 0 {
		if statusBytes, err := json.Marshal(appt.Status); err == nil {
			appt.Status = string(statusBytes)
		}
	}

	// Set common fields
	appt.Account = email
	appt.FreightTrackingID = body.FreightTrackingID
	appt.PONums = body.PONums
	appt.ServiceID = userServiceID

	// TODO: These fields (DockID, WarehouseID, Date, StartTime) are currently being set both here and in the
	// integration implementation (e.g., Opendock.MakeAppointment).  Once we verify the integration is properly
	// setting and persisting these fields, we can remove this duplicate assignment.
	appt.DockID = body.DockID
	appt.WarehouseID = body.WarehouseID
	appt.Date = body.StartTime.Format(time.DateOnly)
	appt.StartTime = body.StartTime

	ctx = log.With(
		ctx,
		zap.String("apptID", strconv.FormatUint(uint64(appt.ID), 10)),
		zap.String("confirmationNumber", appt.ConfirmationNo),
	)

	// TODO: do this directly in the body instead
	// Validate and set source only if it's a valid scheduling integration
	switch models.IntegrationName(body.Source) {
	case models.DataDocks,
		models.DaySmart,
		models.E2open,
		models.Opendock,
		models.Retalix,
		models.Turvo,
		models.Velostics,
		models.YardView:

		appt.Source = models.IntegrationName(body.Source)

	default:
		log.Warn(
			ctx,
			"unsupported scheduling integration source for appt",
			zap.String("scheduler", body.Source),
		)
	}

	ctx = log.With(ctx, zap.String("scheduler", string(appt.Source)))
	log.Info(ctx, "appointment created successfully")

	// Try to create warehouse-address association if we have a warehouse
	go func() {
		err := tryCreateWarehouseAddress(
			context.WithoutCancel(ctx),
			warehouse,
			userServiceID,
			body.Source,
			body.RequestType,
		)
		if err != nil {
			log.Error(ctx, "failed to create warehouse-address association", zap.Error(err))
		}

	}()

	return appt, nil
}

// tryCreateWarehouseAddress attempts to create a warehouse-address association.
func tryCreateWarehouseAddress(
	ctx context.Context,
	warehouse *models.Warehouse,
	serviceID uint,
	integrationName string,
	requestType models.RequestType,
) error {

	if string(requestType) == "" {
		return fmt.Errorf("empty request type for %s integration", integrationName)
	}

	if warehouse == nil {
		return fmt.Errorf("empty warehouse for %s integration", integrationName)
	}

	load, err := loadDB.GetLoadByWarehouseID(
		ctx,
		serviceID,
		warehouse.ID,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to get load: %w", err)
	}

	err = warehouseDB.CreateWarehouseAddress(
		ctx,
		*warehouse,
		load,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to create warehouse-address association: %w", err)
	}

	log.Info(ctx, "successfully created warehouse-address association")
	return nil
}

// updateTMSWithAppointment updates the TMS load with appointment details.
// It handles different time zones and formats the appointment reference appropriately.
func updateTMSWithAppointment(
	ctx context.Context,
	body *BodyV2,
	curLoad models.Load,
	appt models.Appointment,
	tmsIntegration models.Integration,
	schedulingIntegration string,
	tmsClient tms.Interface,
) error {

	reqLoad := curLoad
	normalizedTime, err := getNormalizedTime(body, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error loading location", zap.String("timezone", body.WarehouseTimezone), zap.Error(err))

		return err
	}

	updateLoadDetails(&curLoad, &reqLoad, body.StopType, appt.ConfirmationNo, schedulingIntegration, normalizedTime)

	updatedLoad, _, err := tmsClient.UpdateLoad(ctx, &curLoad, &reqLoad)
	if err != nil {
		log.Error(ctx, "failed to update TMS load with appt details", zap.Error(err))

		return fmt.Errorf("update TMS load: %w", err)
	}

	if err := loadDB.UpsertLoad(ctx, &updatedLoad, &tmsIntegration); err != nil {
		log.Error(ctx, "error updating load DB with appt details", zap.Error(err))
	}

	return nil
}

func getNormalizedTime(body *BodyV2, tmsIntegration models.Integration) (time.Time, error) {
	if tmsIntegration.Name != models.Aljex {
		return body.StartTime, nil
	}

	loc, err := time.LoadLocation(body.WarehouseTimezone)
	if err != nil {
		return time.Time{}, fmt.Errorf("load timezone %s: %w", body.WarehouseTimezone, err)
	}

	// Use .In() in case JSON timestamp had an offset
	nyTime := body.StartTime.In(loc)
	return time.Date(
		nyTime.Year(), nyTime.Month(), nyTime.Day(),
		nyTime.Hour(), nyTime.Minute(), nyTime.Second(),
		nyTime.Nanosecond(), time.UTC,
	), nil
}

func updateLoadDetails(
	curLoad,
	reqLoad *models.Load,
	stopType,
	confirmationNo,
	schedulingIntegration string,
	normalizedTime time.Time,
) {

	nullTime := models.NullTime{Time: normalizedTime, Valid: true}
	integrationRef := schedulingIntegration + " " + confirmationNo

	if stopType == "pickup" {
		reqLoad.Pickup.RefNumber = appendWithSeparator(curLoad.Pickup.RefNumber, confirmationNo)
		reqLoad.Pickup.ApptNote = appendWithSeparator(
			curLoad.Pickup.ApptNote,
			integrationRef,
		)
		reqLoad.Pickup.ApptStartTime = nullTime

		return
	}

	reqLoad.Consignee.RefNumber = appendWithSeparator(curLoad.Consignee.RefNumber, confirmationNo)
	reqLoad.Consignee.ApptNote = appendWithSeparator(
		curLoad.Consignee.ApptNote,
		integrationRef,
	)
	reqLoad.Consignee.ApptStartTime = nullTime
}

func appendWithSeparator(base, addition string) string {
	if base == "" {
		return addition
	}

	return base + " - " + addition
}
