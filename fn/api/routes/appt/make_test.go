package appt

import (
	"errors"
	"testing"
	"time"

	"github.com/drumkitai/drumkit/common/models"
	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestMakeV1(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := MakeAppointmentBody{
			FreightTrackingID: "12345",
			DryRun:            false,
			IsTMSLoad:         false,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		if err := validator.TestBody(body); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required StartTime", func(t *testing.T) {
		body := MakeAppointmentBody{
			FreightTrackingID: "12345",
			DryRun:            false,
			IsTMSLoad:         false,
			WarehouseID:       "warehouse-1",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing StartTime, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("start is a required field") {
			t.Errorf("Expected body error about StartTime field, got: %v", validationErr)
		}
	})

	t.Run("missing required DockID", func(t *testing.T) {
		body := MakeAppointmentBody{
			FreightTrackingID: "12345",
			DryRun:            false,
			IsTMSLoad:         false,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing DockID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("dockId is a required field") {
			t.Errorf("Expected body error about DockID field, got: %v", validationErr)
		}
	})

	t.Run("missing required LoadTypeID", func(t *testing.T) {
		body := MakeAppointmentBody{
			FreightTrackingID: "12345",
			DryRun:            false,
			IsTMSLoad:         false,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			DockID:            "dock-1",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing LoadTypeID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("loadTypeId is a required field") {
			t.Errorf("Expected body error about LoadTypeID field, got: %v", validationErr)
		}
	})
}
