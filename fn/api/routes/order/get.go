package order

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// GetOrder retrieves an order by ID
func GetOrder(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c<PERSON><PERSON>("id"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid order ID format")
	}

	orderObj, err := orderdb.GetOrderFromDB(c.UserContext(), uint(id))
	if err != nil {
		return c.Status(http.StatusNotFound).SendString("Order not found")
	}

	return c.JSON(orderObj)
}
