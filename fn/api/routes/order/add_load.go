package order

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// AddLoadToOrder adds a load to an order
func AddLoadToOrder(c *fiber.Ctx) error {
	orderID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid order ID format")
	}

	var req struct {
		LoadID uint `json:"loadID"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid request body")
	}

	// Get the order from the database
	orderObj, err := orderdb.GetOrderFromDB(c.UserContext(), uint(orderID))
	if err != nil {
		return c.Status(http.StatusNotFound).SendString("Order not found")
	}

	// Set the LoadID to associate the order with the load
	orderObj.LoadID = req.LoadID

	// Save the updated order
	if err := orderdb.UpdateOrderInDB(c.UserContext(), orderObj); err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to update order")
	}

	return c.SendStatus(http.StatusCreated)
}
