package order

import (
	"net/http"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/models"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// GetOrderByReference retrieves an order by reference number
func GetOrderByReference(c *fiber.Ctx) error {
	system := c.Query("system")
	value := c.Query("value")
	isPrimary := c.QueryBool("isPrimary", false)

	if system == "" || value == "" {
		return c.Status(http.StatusBadRequest).SendString("system and value parameters are required")
	}

	var orderObj *models.Order
	var err error

	if isPrimary {
		orderObj, err = orderdb.GetOrderByPrimaryReference(c.UserContext(), system, value)
	} else {
		orderObj, err = orderdb.GetOrderByReference(c.UserContext(), system, value)
	}

	if err != nil {
		return c.Status(http.StatusNotFound).SendString("Order not found")
	}

	return c.<PERSON><PERSON>(orderObj)
}
