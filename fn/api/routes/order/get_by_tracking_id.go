package order

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// GetOrderByTrackingID retrieves an order by tracking ID using path parameters
func GetOrderByTrackingID(c *fiber.Ctx) error {
	trackingID := c.Params("trackingId")
	if trackingID == "" {
		return c.Status(http.StatusBadRequest).SendString("Tracking ID is required")
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	log.Info(ctx, "Searching for order by tracking ID", zap.String("trackingId", trackingID))

	// First try to get the order from the database
	orderObj, dbErr := orderdb.GetOrderByTrackingID(ctx, trackingID)
	if dbErr != nil {
		log.Info(ctx, "Order not found in database, trying TMS", zap.Error(dbErr))

		// If not found in DB, try to get from TMS
		orderObj, dbErr = getOrderFromTMS(ctx, userServiceID, trackingID)
		if dbErr != nil {
			log.Error(ctx, "Order not found in TMS either", zap.Error(dbErr))
			return c.Status(http.StatusNotFound).SendString("Order not found")
		}
	}

	return c.JSON(orderObj)
}

// getOrderFromTMS attempts to fetch an order from the TMS and save it to the database
func getOrderFromTMS(ctx context.Context, userServiceID uint, trackingID string) (*models.Order, error) {
	// Get the service to find TMS integrations
	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		return nil, err
	}

	// Get TMS integrations for this service
	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
	if err != nil {
		return nil, err
	}

	if len(tmsIntegrations) == 0 {
		return nil, errors.New("no TMS integrations found for service")
	}

	// Try each TMS integration until we find the order
	for _, tmsIntegration := range tmsIntegrations {
		log.Info(ctx, "Trying TMS integration",
			zap.String("tmsName", string(tmsIntegration.Name)),
			zap.Uint("tmsID", tmsIntegration.ID))

		// Create TMS client
		client, err := tms.New(ctx, tmsIntegration)
		if err != nil {
			log.Warn(ctx, "Failed to create TMS client",
				zap.String("tmsName", string(tmsIntegration.Name)),
				zap.Error(err))
			continue
		}

		// Try to get order from this TMS
		order, _, err := client.GetOrder(ctx, trackingID)
		if err != nil {
			log.Info(ctx, "Order not found in TMS",
				zap.String("tmsName", string(tmsIntegration.Name)),
				zap.Error(err))
			continue
		}

		// Found the order! Set the service and TMS info
		order.ServiceID = userServiceID
		order.Service = service

		// Save to database
		if err := orderdb.CreateOrderInDB(ctx, order); err != nil {
			log.Warn(ctx, "Failed to save order to database", zap.Error(err))
			// Continue anyway, we still return the order
		}

		log.Info(ctx, "Successfully retrieved order from TMS",
			zap.String("tmsName", string(tmsIntegration.Name)),
			zap.String("trackingID", trackingID))

		return order, nil
	}

	return nil, errors.New("order not found in any TMS integration")
}
