package usergroups

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type (
	GetUserGroupsPath struct {
		ServiceID uint `path:"serviceID" validate:"required"`
	}

	GetUserGroupsResponse struct {
		UserGroups         []CoreUserGroup `json:"userGroups"`
		CurrentUserGroupID uint            `json:"currentUserGroupId,omitempty"`
	}

	CoreUserGroup struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}
)

func GetUserGroups(c *fiber.Ctx) error {
	var path GetUserGroupsPath

	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	if middleware.ServiceIDFromContext(c) != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	userGroups, err := userGroupsDB.GetByServiceID(ctx, path.ServiceID)
	if err != nil {
		log.Error(ctx, "error while getting user groups", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	currentUserGroup, err := userGroupsDB.GetByUserID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error while getting current user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var userGroupsResponse []CoreUserGroup
	for _, userGroup := range userGroups {
		userGroupsResponse = append(userGroupsResponse, CoreUserGroup{
			ID:   userGroup.ID,
			Name: userGroup.Name,
		})
	}

	return c.Status(http.StatusOK).JSON(GetUserGroupsResponse{
		UserGroups:         userGroupsResponse,
		CurrentUserGroupID: currentUserGroup.ID,
	})
}
