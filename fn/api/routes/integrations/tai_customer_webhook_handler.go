package integrations

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	customerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

// Tai TMS does not support pulling customers from the TMS, so we need to use the webhook to create customers.
// We have a special persistent webhook token (defined in webhook_token.go)
// that is used to authenticate the webhook.
func TaiCustomerWebhookHandler(c *fiber.Ctx) error {
	var body tai.CustomerResp
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := log.With(c.UserContext())

	integration, err := integrationDB.GetByName(ctx, userServiceID, models.Tai)
	if err != nil {
		log.Error(
			ctx,
			"integration not configured properly",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	client := tai.New(ctx, integration)

	// Handle ShipmentUpdate and ShipmentCreate webhooks with the same model
	customer := client.TaiCustomerToCustomer(body)
	customers := []models.TMSCustomer{customer}
	err = customerDB.RefreshTMSCustomers(ctx, &customers)
	if err != nil {
		log.Error(
			ctx, "error refreshing tms customers",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	// Load and save customer addresses
	customerID, err := strconv.Atoi(customer.ExternalID)
	if err != nil {
		log.Error(
			ctx, "error converting customer ID to int",
			zap.Error(err), zap.String("customer_id", customer.ExternalID),
		)
		return c.SendStatus(http.StatusInternalServerError)
	}

	locations, err := client.LoadCustomerAddresses(ctx, customerID)
	if err != nil {
		log.Error(
			ctx, "error loading customer addresses",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	err = tmsLocationDB.RefreshTMSLocations(ctx, &locations)
	if err != nil {
		log.Error(
			ctx, "error refreshing tms locations",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
