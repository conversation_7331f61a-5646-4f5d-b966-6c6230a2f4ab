package integrations

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func OnboardCarrierVerification(c *fiber.Ctx) error {
	var body models.OnboardCarrierVerificationRequest
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := log.With(c.UserContext())

	newCarrierVerification := models.Integration{
		Name: models.IntegrationName(body.Name),
		Type: models.CarrierVerification,
	}

	client, err := carrierverification.New(ctx, newCarrierVerification)
	if err != nil {
		log.Error(ctx, "Creating Carrier Verification client failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "Creating Carrier Verification client failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	initialOnboardResponse, err := client.InitialOnboard(ctx, service, body)
	if err != nil {
		log.Error(ctx, "OnboardCarrierVerification failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newCarrierVerification.Username = initialOnboardResponse.Username
	newCarrierVerification.EncryptedPassword = []byte(initialOnboardResponse.EncryptedPassword)
	newCarrierVerification.AccessToken = initialOnboardResponse.AccessToken
	newCarrierVerification.RefreshToken = initialOnboardResponse.RefreshToken
	newCarrierVerification.AccessTokenExpirationDate = models.NullTime{
		Time:  initialOnboardResponse.AccessTokenExpirationDate,
		Valid: true,
	}
	newCarrierVerification.ServiceID = userServiceID

	if body.UserGroups == "all" || body.UserGroups == "" {
		newCarrierVerification.IsServiceWide = true
	} else {
		newCarrierVerification.IsServiceWide = false
		newCarrierVerification.UserGroups = make([]models.UserGroup, len(body.UserGroups))
		for i, groupID := range strings.Split(body.UserGroups, ",") {
			id, err := strconv.ParseUint(groupID, 10, 32)
			if err != nil {
				log.Error(ctx, "Invalid user group ID", zap.String("groupID", groupID))
				return c.SendStatus(http.StatusBadRequest)
			}
			newCarrierVerification.UserGroups[i] = models.UserGroup{Model: gorm.Model{ID: uint(id)}}
		}
	}

	if err = integrationDB.Create(ctx, &newCarrierVerification); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusCreated)
}
