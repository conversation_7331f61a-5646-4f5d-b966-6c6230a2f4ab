package carrierverification

import (
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type InviteCarrierBody struct {
	Email     string `json:"email"`
	DOTNumber string `json:"dot_number"`
}

func InviteCarrierVerification(c *fiber.Ctx) error {
	var body InviteCarrierBody
	var err error

	if err = api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	integration, err := integrationDB.GetCarrierVerificationByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "Get carrier failed", zap.Error(
			fmt.Errorf("error fetching carrier verification integration: %w", err)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client := carrierverification.MustNew(ctx, integration)

	err = client.InviteCarrier(ctx, body.DOTNumber, body.Email)
	if err != nil {
		log.Error(ctx, "Carrier invitation failed", zap.Error(err))
		return c.SendStatus(http.StatusBadRequest)
	}

	return c.SendStatus(http.StatusOK)
}
