package drumkit

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/api/env"
)

const devCyclopsURL = "http://localhost:8000"

// NOTE: Unprotected endpoint.
func PublicGetCyclopsHealth(c *fiber.Ctx) error {
	cyclopsURL := devCyclopsURL

	if env.Vars.AppEnv != "dev" {
		cyclopsURL = env.Vars.CyclopsURL
	}

	cyclopsHealthURL := cyclopsURL + "/health"
	ctx := log.With(c.UserContext(), zap.String("cyclops_url", cyclopsHealthURL))

	log.Info(ctx, "attempting to check cyclops health")

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, cyclopsHealthURL, nil)
	if err != nil {
		log.Error(ctx, "failed to create health request to cyclops", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	res, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(ctx, "failed to execute health request to cyclops", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}
	defer res.Body.Close()

	log.Info(ctx, "cyclops health check completed", zap.Int("status_code", res.StatusCode))

	return c.SendStatus(res.StatusCode)
}
