package metrics

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	metricsHelpers "github.com/drumkitai/drumkit/common/metrics"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
)

type (
	DateRange struct {
		From time.Time `json:"from"`
		To   time.Time `json:"to"`
	}

	PostQuoteDashboardPath struct {
		ServiceID uint `path:"serviceID" validate:"required"`
	}

	PostQuoteDashboardBody struct {
		DateRange  DateRange `json:"dateRange"`
		UserID     uint      `json:"userId"`
		CustomerID uint      `json:"customerId"`
	}

	QuoteDashboardResponse struct {
		Hero              metricsHelpers.QuoteSummaryData `json:"hero"`
		TopCustomers      []metricsHelpers.MetricData     `json:"topCustomers"`
		QuoteServiceUsage []metricsHelpers.MetricData     `json:"quoteServiceUsage"`
		TransportTypes    []metricsHelpers.MetricData     `json:"transportTypes"`
		AverageMetrics    []metricsHelpers.MetricData     `json:"averageMetrics"`
	}
)

func PostQuoteDashboard(c *fiber.Ctx) error {
	var path PostQuoteDashboardPath
	var body PostQuoteDashboardBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("serviceID", path.ServiceID))
	userServiceID := middleware.ServiceIDFromContext(c)

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	// User is in the middle of selecting a date range, so we shouldn't error out here.
	if body.DateRange.From.IsZero() || body.DateRange.To.IsZero() {
		return c.Status(http.StatusOK).JSON(QuoteDashboardResponse{})
	}

	// get all quote requests that fit the filters
	var opts []quoteRequestDB.Option
	if body.UserID != 0 {
		opts = append(opts, quoteRequestDB.WithUserID(body.UserID))
	}
	if body.CustomerID != 0 {
		opts = append(opts, quoteRequestDB.WithCustomerID(body.CustomerID))
	}
	opts = append(opts, quoteRequestDB.WithDateRange(body.DateRange.From, body.DateRange.To))

	quoteRequests, err := quoteRequestDB.GetQuoteRequests(ctx, path.ServiceID, opts...)
	if err != nil {
		log.Error(ctx, "failed to get quote requests", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(QuoteDashboardResponse{})
	}

	summaryData := metricsHelpers.CalculateQuoteSummaryMetrics(ctx, quoteRequests)

	topFiveCustomers := metricsHelpers.CalculateTopFiveCustomersByRequestCount(ctx, quoteRequests)

	quotePlatformUsage := metricsHelpers.CalculateQuotePlatformUsage(ctx, quoteRequests)

	transportTypes := metricsHelpers.CalculateTransportTypeDistribution(quoteRequests)

	averageMetrics := metricsHelpers.CalculateAverageMetrics(quoteRequests)

	return c.Status(http.StatusOK).JSON(QuoteDashboardResponse{
		Hero:              summaryData,
		TopCustomers:      topFiveCustomers,
		QuoteServiceUsage: quotePlatformUsage,
		TransportTypes:    transportTypes,
		AverageMetrics:    averageMetrics,
	})
}
