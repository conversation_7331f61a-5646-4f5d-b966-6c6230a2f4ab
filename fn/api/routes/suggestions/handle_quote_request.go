package suggestions

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteDB "github.com/drumkitai/drumkit/common/rds/quick_quote"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	braintrustutil "github.com/drumkitai/drumkit/common/util/braintrust"
)

type (
	HandleQuoteRequestPath struct {
		ID uint
	}

	FinalQuoteData struct {
		// If CarrierCostType is Amount, this is FinalCarrierCost + Margin
		// If CarrierCostType is PerMile, this is (FinalCarrierCost + Margin) * Distance
		FinalQuotePrice       int    `json:"finalQuotePrice"`
		FinalMargin           int    `json:"finalMargin"`
		MarginType            string `json:"marginType"`      // Amount or Percentage
		CarrierCostType       string `json:"carrierCostType"` // Flat, PerMile or Linehaul
		FinalCarrierCost      int    `json:"finalCarrierCost"`
		CustomerExternalTMSID string `json:"customerExternalTMSId"`
	}

	HandleQuoteRequestBody struct {

		// V1
		QuoteID uint             `json:"quoteId"`
		Changes SuggestedRequest `json:"changes"`

		// V2
		FinalQuoteData FinalQuoteData `json:"finalQuoteData"`

		// Both
		Status models.SuggestionStatus `json:"status" validate:"required,oneof=inFlight accepted rejected"`

		SelectedQuickQuoteID *uint `json:"selectedQuickQuoteId,omitempty"`
	}

	HandleQuoteRequestResponse struct {
		Message string `json:"message"`
	}
)

// This endpoint is only called at the end of the quick quote flow.
func UpdateQuoteRequestSuggestion(c *fiber.Ctx) error {
	var path HandleQuoteRequestPath
	var body HandleQuoteRequestBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)
	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	user, err := userDB.GetByID(ctx, userID)
	if err != nil {
		// Fail-open; non-critical lookup
		log.WarnNoSentry(ctx, "error fetching user", zap.Error(err))
		user.ID = userID
	}

	switch {
	// V1
	case body.QuoteID != 0:
		log.Info(ctx, "updating quote request suggestion via V1", zap.Any("body", body))
		var qr models.QuoteRequest
		err := rds.GetByID(ctx, path.ID, &qr)
		if err != nil {
			log.ErrorNoSentry(ctx, "error fetching quote request", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		qr.Status = body.Status
		qr.SelectedQuickQuoteID = body.SelectedQuickQuoteID

		if body.Status == models.Accepted {
			Changes := models.QuoteLoadInfo{
				TransportType: body.Changes.TransportType,
				PickupLocation: models.Address{
					City:  body.Changes.PickupCity,
					State: body.Changes.PickupState,
					Zip:   body.Changes.PickupZip,
				},
				DeliveryLocation: models.Address{
					City:  body.Changes.DeliveryCity,
					State: body.Changes.DeliveryState,
					Zip:   body.Changes.DeliveryZip,
				},
			}
			qr.AppliedRequest = Changes

			if body.Changes.PickupDate != nil {
				qr.AppliedRequest.PickupDate = models.NullTime{
					Time:  *body.Changes.PickupDate,
					Valid: true,
				}
			}

			if body.Changes.DeliveryDate != nil {
				qr.AppliedRequest.DeliveryDate = models.NullTime{
					Time:  *body.Changes.DeliveryDate,
					Valid: true,
				}
			}

			quote, err := quoteDB.GetByID(ctx, body.QuoteID)
			if err != nil {
				log.ErrorNoSentry(
					ctx,
					"error creating quote and quote request association",
					zap.Error(err),
				)

				return c.SendStatus(http.StatusInternalServerError)
			}

			qr.QuickQuotes = append(qr.QuickQuotes, quote)
		}

		// Here we also send Braintrust the expected values for the quote request suggestion
		err = braintrustutil.SubmitBraintrustQuoteRequestFeedback(ctx, qr)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}

		if err = quoteRequestDB.UpsertQuoteRequestAndAssociations(ctx, &qr); err != nil {
			log.ErrorNoSentry(ctx, "error updating quote request", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		return c.Status(http.StatusOK).JSON(HandleQuoteRequestResponse{
			Message: "Quote request updated successfully",
		})

	// V2
	default:
		log.Info(ctx, "updating quote request suggestion via V2", zap.Any("body", body))

		if path.ID == 0 {
			return c.Status(http.StatusBadRequest).SendString("quote request ID is required")
		}

		quoteRequest, err := quoteRequestDB.GetRequestByID(ctx, path.ID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(http.StatusNotFound).SendString(
					fmt.Sprintf("quote request ID %d not found", path.ID))
			}

			log.WarnNoSentry(ctx, "failed to get quote request for update", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		// If the customer external TMS ID is provided, we must associate it with the quote request.
		var customer models.TMSCustomer
		if body.FinalQuoteData.CustomerExternalTMSID != "" {
			tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
			if err != nil {
				log.Error(ctx, "error fetching TMS integrations", zap.Error(err))
				return c.SendStatus(http.StatusInternalServerError)
			}

			customer, err = tmsCustomerDB.GetByExternalTMSID(
				ctx,
				tmsIntegrations[0].ID,
				body.FinalQuoteData.CustomerExternalTMSID,
			)
			if err != nil {
				log.Error(ctx, "error fetching customer", zap.Error(err))
				return c.SendStatus(http.StatusInternalServerError)
			}
		}

		// Here we also send Braintrust the expected values for the quote request suggestion
		err = braintrustutil.SubmitBraintrustQuoteRequestFeedback(ctx, quoteRequest)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}

		// We pass nil for the appliedFields because we don't need to update them
		// since they are added to the quote request when the user fetches external quotes.
		err = quoteRequestDB.UpdateQuoteRequestStatus(
			ctx,
			path.ID,
			nil,
			&user,
			body.Status,
			&models.FinalQuoteData{
				FinalQuotePrice:  body.FinalQuoteData.FinalQuotePrice,
				FinalMargin:      body.FinalQuoteData.FinalMargin,
				MarginType:       body.FinalQuoteData.MarginType,
				CarrierCostType:  body.FinalQuoteData.CarrierCostType,
				FinalCarrierCost: body.FinalQuoteData.FinalCarrierCost,
				CustomerID:       customer.ID,
			},
			body.SelectedQuickQuoteID,
		)
		if err != nil {
			log.ErrorNoSentry(ctx, "error updating quote request", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		return c.Status(http.StatusOK).JSON(HandleQuoteRequestResponse{
			Message: "Quote request updated successfully",
		})
	}
}
