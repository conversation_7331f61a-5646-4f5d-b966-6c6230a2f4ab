package suggestions

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
)

type (
	ApplyLoadInfoPath struct {
		ID uint
	}

	ApplyLoadInfoBody struct {
		models.SuggestedChanges
		Status models.SuggestionStatus `json:"status" validate:"required,oneof=accepted rejected"`
	}

	ApplyLoadInfoResponse struct {
		Message string `json:"message,omitempty"`
	}
)

// This endpoint is only used for updates to loads made by the Load Information tab on the FE.
func ApplyLoadInfo(c *fiber.Ctx) error {
	var path ApplyLoadInfoPath
	var body ApplyLoadInfoBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	suggestion, err := suggestionsDB.GetSuggestionByID(ctx, path.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("suggestion ID %d not found", path.ID))
		}

		log.WarnNoSentry(ctx, "get suggestions endpoint - suggestion query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	claims := middleware.ClaimsFromContext(c)
	if claims.Email != suggestion.Account {
		log.ErrorNoSentry(ctx, "unauthorized: email from token does not match DB",
			zap.String("dbSuggestionAccount", suggestion.Account), zap.String("claimsEmail", claims.Email))

		return c.SendStatus(http.StatusUnauthorized)
	}

	suggestion.Applied = body.SuggestedChanges
	suggestion.Status = body.Status
	if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
		log.Error(ctx, "suggestion update error", zap.Error(err))

		response := ApplyLoadInfoResponse{
			Message: "Updated suggestion, but Drumkit failed to log this action.",
		}

		return c.Status(http.StatusUnprocessableEntity).JSON(response)
	}

	response := ApplyLoadInfoResponse{
		Message: "Suggestion updated successfully",
	}

	return c.Status(http.StatusOK).JSON(response)
}
