package user

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	SignupGoogleBody struct {
		api.GoogleAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}

	SignupGoogleResponse struct {
		AccessToken     string      `json:"access_token"`
		TokenExpiration int64       `json:"token_expiration"` // In Unix time
		Email           string      `json:"email"`
		Role            models.Role `json:"role"`
		GroupID         uint        `json:"group_id"`
		ServiceID       uint        `json:"service_id"`
		TokenType       string      `json:"token_type"`
	}
)

func SignupGoogle(c *fiber.Ctx) error {
	var body SignupGoogleBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("hostedDomain", body.HostedDomain))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		ctx = log.With(ctx, zap.String("userEmail", body.DevEmail))

		err := userDB.Create(ctx, &models.User{
			EmailAddress:       body.DevEmail,
			GmailLastHistoryID: 1,
			Service:            models.Service{},
		})
		if err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusCreated).SendString("user created successfully")
	}

	log.Info(ctx, "received google signup request")

	// make Google or Microsoft call here
	resp, err := api.CallBackFromGoogle(ctx, body.GoogleAuthCodeRequest)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx, zap.String("userEmail", resp.UserInfo.Email))

	email := resp.UserInfo.Email

	// Block signup requests from *@gmail.com, except Google's official oauth test account
	if strings.HasSuffix(email, "@gmail.com") && email != "<EMAIL>" {
		// Request is not from a GSuite account (block gmail.com signups)
		log.Error(ctx, "rejecting signup request from gmail account", zap.String("email", email))

		return c.Status(http.StatusUnauthorized).SendString("gsuite domain required")
	}

	user := models.User{
		EmailAddress:  resp.UserInfo.Email,
		EmailProvider: models.GmailEmailProvider,
		Name:          resp.UserInfo.Name,
	}

	userFound, err := userDB.GetByEmail(ctx, resp.UserInfo.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = userFound.ID
	user.Role = userFound.Role

	if err := assignOrCreateService(ctx, &user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
		ctx,
		resp.AccessToken,
		resp.RefreshToken,
		nil,
	)
	if err != nil {
		log.Error(ctx, "gmail token encryption failed", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	user.TokenExpiry = resp.ExpTime

	if err := watchGmailInbox(ctx, &user); err != nil {
		// Fail-open. If initial watch fails, manually trigger Lambda for the user
		log.Error(ctx, "watch gmail inbox failed", zap.Error(err))
	}

	if userFound.ID == 0 {
		err = userDB.Create(ctx, &user)
	} else {
		err = userDB.Update(ctx, user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, email, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(SignupGoogleResponse{
		AccessToken:     *accessToken,
		Email:           email,
		Role:            user.Role,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}

func watchGmailInbox(ctx context.Context, user *models.User) error {
	client, err := gmailclient.New(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, user)
	if err != nil {
		return fmt.Errorf("error creating Gmail service for %s: %w", user.EmailAddress, err)
	}

	req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}
	watchResp, err := client.WatchInbox(ctx, req)
	if err != nil {
		return fmt.Errorf("error watching %s inbox after onboarding: %w", user.EmailAddress, err)
	}

	user.WebhookExpiration = time.UnixMilli(watchResp.Expiration)
	log.Info(ctx, "watch inbox successful", zap.Time("expiration", user.WebhookExpiration))

	// Ignore current history ID returned in watchResp because:
	// 1) For new users, we backfill the last 14 days (see ingestion/main.go)
	// 2) For existing users, keep historical ID so we can process old emails

	return nil
}
