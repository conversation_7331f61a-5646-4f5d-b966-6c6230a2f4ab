package user

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	UpdateOnPremUserBody struct {
		ID                uint      `json:"id"`
		EmailAddress      string    `json:"emailAddress"`
		WebhookExpiration time.Time `json:"webhookExpiration"`
	}
)

func UpdateOnPremUser(c *fiber.Ctx) error {
	var body UpdateOnPremUserBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),
		zap.Uint("id", body.ID),
		zap.String("account", body.EmailAddress),
	)

	log.Info(ctx, "received onprem user update request")

	user, err := userDB.GetByOnPremID(ctx, body.ID, body.EmailAddress)
	if err != nil {
		log.Error(ctx, "error getting onprem user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.WebhookExpiration = body.WebhookExpiration

	err = userDB.Update(ctx, user)
	if err != nil {
		log.Error(ctx, "error updating onprem user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
