package user

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type GetUserDATInfoResponse struct {
	HasGrantedDATPermissions bool   `json:"hasGrantedDATPermissions"`
	DATEmailAddress          string `json:"datEmailAddress"`
}

func GetUserDATInfo(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())

	email := middleware.ClaimsFromContext(c).Email
	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetUserDATInfoResponse{
		HasGrantedDATPermissions: user.HasGrantedDATPermissions,
		DATEmailAddress:          user.DATEmailAddress,
	})
}
