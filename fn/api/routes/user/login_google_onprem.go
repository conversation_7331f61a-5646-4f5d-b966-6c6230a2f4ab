package user

import (
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	LoginGoogleFromOnPremBody struct {
		OnPremUser      models.OnPremUser `json:"user"`
		AccessToken     string            `json:"access_token"`
		TokenExpiration int64             `json:"token_expiration"` // In Unix time
		Email           string            `json:"email"`
		TokenType       string            `json:"token_type"`

		// For local dev only: the email can be specified directly
		DevEmail string `json:"dev_email"`
	}

	LoginGoogleFromOnPremResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

// LoginGoogleFromOnPrem manages the login process for users originating from a customer that self-hosts Drumkit. This
// function bypasses typical OAuth token handling, as these tokens are managed by the customer. Instead, it focuses on
// verifying the user's existence in the database using their email address. In development environments, a direct email
// can be used for login. The function then updates the token expiration date as provided by the on-premise customer and
// generates a Drumkit access token for further authenticated API interactions. This token is returned along with user
// and service details in the response.
func LoginGoogleFromOnPrem(c *fiber.Ctx) error {
	var body LoginGoogleFromOnPremBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	var email string
	var err error

	// If manual POST request and not from drumkit-portal, require only dev email
	if env.Vars.AppEnv == "dev" {
		if email = body.DevEmail; email == "" {
			return c.Status(http.StatusBadRequest).SendString("email is required")
		}
	}

	email = body.OnPremUser.EmailAddress

	ctx = log.With(ctx, zap.String("userEmail", email))

	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "login failed: user does not exist", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		// Some other DB error
		return c.SendStatus(http.StatusInternalServerError)
	}

	if env.Vars.AppEnv == "dev" || body.DevEmail != "" {
		user.TokenExpiry = body.OnPremUser.TokenExpiry

		if err := userDB.Update(ctx, user); err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, email, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userGroup, err := userGroupsDB.GetByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error while getting user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginGoogleFromOnPremResponse{
		AccessToken:     *accessToken,
		Email:           email,
		GroupID:         userGroup.ID,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
