package user

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

type GetTMSInfoResponse struct {
	RevenueCode   string `json:"revenueCode"`
	ExternalTMSID string `json:"externalTMSId"`
}

func GetTMSInfo(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	userID := middleware.UserIDFromContext(c)

	info, err := tmsUserDB.GetTMSInfo(ctx, userID)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if info == nil {
		return c.SendStatus(http.StatusNotFound)
	}

	return c.Status(http.StatusOK).JSON(GetTMSInfoResponse{
		RevenueCode:   info.RevenueCode,
		ExternalTMSID: info.ExternalTMSID,
	})
}
