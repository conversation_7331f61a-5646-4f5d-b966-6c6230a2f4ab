package user

import (
	"errors"
	"fmt"
	"strings"

	"github.com/golang-jwt/jwt/v4"
)

// MicrosoftClaims satisfies jwt.Claims interface.
type MicrosoftClaims struct {
	jwt.RegisteredClaims
	Name              string  `json:"name"`
	Oid               string  `json:"oid"` // User's unique ID
	PreferredUsername string  `json:"preferred_username"`
	TenantID          string  `json:"tid"`
	Rh                string  `json:"rh"`
	Scp               string  `json:"scp"`
	Sub               string  `json:"sub"`
	Uti               string  `json:"uti"`
	Acct              float64 `json:"acct"`
	Aio               string  `json:"aio"`
	Azp               string  `json:"azp"`
	Azpacr            string  `json:"azpacr"`
	Ver               string  `json:"ver"` // Token version
}

type (
	JWKS struct {
		Keys []JSONWebKeys `json:"keys"`
	}

	JSONWebKeys struct {
		Kid    string   `json:"kid"`
		Kty    string   `json:"kty"`
		Alg    string   `json:"alg"`
		Use    string   `json:"use"`
		N      string   `json:"n"`
		E      string   `json:"e"`
		X5c    []string `json:"x5c,omitempty"`
		Issuer string   `json:"issuer"`
	}
)

func (c MicrosoftClaims) IsValid() error {
	if err := c.Valid(); err != nil {
		return err
	}

	expectedIss := strings.ReplaceAll("https://login.microsoftonline.com/{tenantID}/v2.0", "{tenantID}", c.TenantID)
	if !c.VerifyIssuer(expectedIss, true) {
		return errors.New("invalid issuer: " + c.Issuer)
	}

	// App ID in Vesta's manifest
	if !c.VerifyAudience(getAddInAppID(), true) {
		return fmt.Errorf("invalid audience: %v", c.Audience)
	}

	return nil
}

// The app ID associated with the add-in (separate from Drumkit Portal and Drumkit Backend apps).
//
// Tokens are only valid for the environment for which they are generated.
func getAddInAppID() string {
	// From Vesta(/dist/)manifest.xml
	return "dfe5d4cc-66cd-4299-afa7-ed8ab8fb9fb8"

}
