package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRemoveHashedToken(t *testing.T) {
	// test that token actually gets removed from slice as expected
	tokenList1 := []string{"token1", "token2", "token3"}
	result1 := removeHashedToken("token2", tokenList1)
	expected1 := []string{"token1", "token3"}
	assert.Equal(t, result1, expected1)

	// test that when given a token value not in slice, the original slice is returned
	tokenList2 := []string{"token1", "token2", "token3"}
	result2 := removeHashedToken("token4", tokenList2)
	expected2 := []string{"token1", "token2", "token3"}
	assert.Equal(t, result2, expected2)
}
