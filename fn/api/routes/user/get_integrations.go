package user

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	GetUserIntegrationsQuery struct {
		EmailAddress string `query:"email" validate:"required"`
	}

	GetUserIntegrationsResponse struct {
		UserIntegrations []CoreIntegration `json:"userIntegrations"`
	}

	CoreIntegration struct {
		Name     models.IntegrationName `json:"name"`
		Type     models.IntegrationType `json:"type"`
		Username string                 `json:"username"`
		Note     string                 `json:"note"`
		Disabled bool                   `json:"disabled"`
	}
)

// Used by Drumkit Portal to get user integrations
func GetUserIntegrations(c *fiber.Ctx) error {
	var query GetUserIntegrationsQuery

	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("queryParams", query))

	if claims := middleware.ClaimsFromContext(c); claims.Email != query.EmailAddress {
		log.WarnNoSentry(ctx, "unauthorized: email from token does not match query")
		return c.SendStatus(http.StatusUnauthorized)
	}

	user, err := rds.GetUserByEmail(ctx, query.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", query.EmailAddress))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userIntegrations, err := integrationDB.GetGroupIntegrationsByUser(ctx, user)
	if err != nil {
		log.Error(ctx, "error getting user integrations", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var userIntegrationsResponse []CoreIntegration
	for _, integration := range userIntegrations {
		userIntegrationsResponse = append(userIntegrationsResponse, CoreIntegration{
			Name:     integration.Name,
			Type:     integration.Type,
			Username: integration.Username,
			Note:     integration.Note,
			Disabled: integration.Disabled,
		})
	}

	return c.Status(http.StatusOK).JSON(GetUserIntegrationsResponse{
		UserIntegrations: userIntegrationsResponse,
	})
}
