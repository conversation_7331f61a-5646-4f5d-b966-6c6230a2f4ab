package user

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	EnableDATIndividualAccessBody struct {
		DATEmailAddress string `json:"datEmailAddress" validate:"required"`
	}
)

func EnableDATIndividualAccess(c *fiber.Ctx) error {
	var body EnableDATIndividualAccessBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),
		zap.String("DAT email address", body.DATEmailAddress),
	)

	email := middleware.ClaimsFromContext(c).Email
	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := validateDATEmail(ctx, user.ServiceID, body.DATEmailAddress); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	user.HasGrantedDATPermissions = true
	user.DATEmailAddress = body.DATEmailAddress

	if err := userDB.Update(ctx, user); err != nil {
		log.Error(ctx, "user update error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}

func validateDATEmail(ctx context.Context, userServiceID uint, datEmailAddress string) error {
	integration, err := integrationDB.GetByName(ctx, userServiceID, models.DAT)
	if err != nil {
		log.Error(
			ctx,
			"failed to get DAT integration for service",
			zap.Error(err),
			zap.Uint("serviceID", userServiceID),
		)
		return fmt.Errorf("service doesn't contain enabled DAT Integration")
	}

	_, err = dat.New(ctx, integration, datEmailAddress)
	if err != nil {
		log.Error(ctx, "Failed to initialize DAT client with given email", zap.Error(err))
		return fmt.Errorf("failed to initialize DAT client with email")
	}

	return nil
}
