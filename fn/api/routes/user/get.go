package user

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetUserQuery struct {
		EmailAddress string `query:"email" validate:"required"`
	}

	GetUserResponse struct {
		ID                            uint                 `json:"id"`
		Name                          string               `json:"name"`
		EmailProvider                 models.EmailProvider `json:"emailProvider"`
		Email                         string               `json:"email"`
		EmailSignatureHTML            string               `json:"emailSignatureHTML"`
		EmailSignatureQuillDelta      json.RawMessage      `json:"emailSignatureQuillDelta"`
		UseSignatureOnNewEmails       bool                 `json:"useSignatureOnNewEmails"`
		UseSignatureOnRepliesForwards bool                 `json:"useSignatureOnRepliesForwards"`
	}
)

func GetUser(c *fiber.Ctx) error {
	var query GetUserQuery

	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("queryParams", query))

	if claims := middleware.ClaimsFromContext(c); claims.Email != query.EmailAddress {
		log.WarnNoSentry(ctx, "unauthorized: email from token does not match query")
		return c.SendStatus(http.StatusUnauthorized)
	}

	user, err := userDB.GetByEmail(ctx, query.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", query.EmailAddress))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetUserResponse{
		ID:                            user.ID,
		Name:                          user.Name,
		Email:                         user.EmailAddress,
		EmailProvider:                 user.EmailProvider,
		EmailSignatureHTML:            user.EmailSignature,
		EmailSignatureQuillDelta:      user.EmailSignatureQuillDelta,
		UseSignatureOnNewEmails:       user.UseSignatureOnNewEmails,
		UseSignatureOnRepliesForwards: user.UseSignatureOnRepliesForwards,
	})
}

type GetUsersByServiceIDPath struct {
	ServiceID uint `path:"serviceID" validate:"required"`
}

func GetUsersByServiceID(c *fiber.Ctx) error {
	var path GetUsersByServiceIDPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := log.With(c.UserContext(), zap.Uint("serviceID", userServiceID))

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	users, err := userDB.GetAllByServiceID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userResponse := make([]GetUserResponse, len(users))
	for i, user := range users {
		userResponse[i] = GetUserResponse{
			ID:    user.ID,
			Email: user.EmailAddress,
		}
	}

	return c.Status(http.StatusOK).JSON(userResponse)
}
