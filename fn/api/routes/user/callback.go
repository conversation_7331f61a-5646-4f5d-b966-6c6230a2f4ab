package user

import (
	"context"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/fn/api/env"
)

func postInternalOnboardingCallback(
	ctx context.Context,
	user *models.User,
	service *models.Service,
	isNewService bool,
) error {

	// Optional in dev & staging
	callbackURI := env.Vars.OnboardingPipedreamURL
	if callbackURI == "" {
		if env.Vars.AppEnv == "prod" {
			log.Warn(ctx, "no onboarding callback configured")
		} else {
			log.WarnNoSentry(ctx, "no onboarding callback configured")
		}

		return nil
	}

	body := map[string]any{
		"environment":         env.Vars.AppEnv,
		"id":                  user.ID,
		"email":               user.EmailAddress,
		"name":                user.Name,
		"role":                user.Role,
		"emailProvider":       user.EmailProvider,
		"serviceName":         service.Name,
		"serviceEmailDomains": service.EmailDomains,
		"isNewService":        isNewService,
		"isOnPrem":            user.IsOnPrem,
	}

	itg := models.Integration{Name: "pipedream"}
	addr, err := url.Parse(env.Vars.OnboardingPipedreamURL)
	if err != nil {
		return fmt.Errorf("error parsing callback url: %w", err)
	}

	log.Info(ctx, "sending onboarding callback to Pipedream", zap.Any("body", body))
	//nolint:bodyclose // Already closed in function
	_, _, err = httputil.PostBytesWithToken(ctx, itg, *addr, body, nil, nil, "")

	return err
}
