# API Validation Testing

## Why This Exists

### The Problem

When working with nested and embedded structs in our API routes, it's easy to introduce validation bugs when:

1. Changing field types (especially pointer vs non-pointer)
2. Modifying validation rules
3. Embedding structs within other structs

For example, this seemingly innocent change broke our API, which broke appointment scheduling in the Drumkit sidebar for NFI users:

```go
// fn/api/routes/appt/get_slots.go
type (
	GetOpenSlotsQuery struct {
		FreightTrackingID string `json:"freightTrackingID"`
		LoadTypeID        string `json:"loadTypeID" validate:"required"`
		TrailerType       string `json:"trailerType"`
		DockID            string `json:"dockID"`
		models.GetOpenSlotsRequest                                       // <--- the embedded struct
		Source string `json:"source"`
	}

	GetOpenSlotsResponse struct {
		GetOpenSlotsQuery
		Warehouse models.Warehouse `json:"warehouse"`
		LoadType  models.LoadType  `json:"loadType"`
		Slots     []models.Slot    `json:"slots"`
	}
)
```

```go
// common/models/appt.go
// Before: Required warehouse (some fields are required)
GetOpenSlotsRequest struct {
	Start             time.Time  `json:"start" validate:"required"` // RFC3339
	End               time.Time  `json:"end" validate:"required"`   // RFC3339
	IncludeStartTimes bool       `json:"includeStartTimes"`
	WarehouseID       string     `json:"warehouseId"`
	Warehouse         Warehouse `json:"warehouse"`                  // <--- We added this field but some fields within Warehouse are required
	PONumbers         []string   `json:"poNums"`
}
```

We added the Warehouse field, which we were testing in the integrations package, so we didn't test other instances where `GetOpenSlotsRequest` was called like in some specific route.

```go
// common/models/warehouse.go
Warehouse struct {
	gorm.Model
	//nolint:lll
	WarehouseID              string `gorm:"index:idx_wh_id_source,unique" json:"warehouseID" validate:"required"` // <--- nested required field
	WarehouseName            string `json:"warehouseName" validate:"required"`                                    // <--- nested required field
	...
}
```

```go
// common/models/appt.go
// After: Optional warehouse with omitempty (fixed)
GetOpenSlotsRequest struct {
	Start             time.Time  `json:"start" validate:"required"` // RFC3339
	End               time.Time  `json:"end" validate:"required"`   // RFC3339
	IncludeStartTimes bool       `json:"includeStartTimes"`
	WarehouseID       string     `json:"warehouseId"`
	Warehouse         *Warehouse `json:"warehouse,omitempty"`       // <-- fixed the issue
	PONumbers         []string   `json:"poNums"`
}
```

### Real-World Impact

Without proper validation testing:
- 🐛 Bugs are discovered in production
- 🔍 Issues are harder to track down
- 🧩 The Drumkit sidebar breaks unexpectedly
- 🕒 Development time is wasted on preventable issues

## How To Use

### 1. Create Validation Tests

Create a test file next to your route handler:

```go
// fn/api/routes/appt/get_slots_test.go
func TestGetOpenSlotsValidation(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			LoadTypeID: "test-load",
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				Start:       time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				End:         time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				WarehouseID: "warehouse-1",
			},
		}

		if err := validator.TestQuery(query); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required LoadTypeID", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				Start:       time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				End:         time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				WarehouseID: "warehouse-1",
			},
		}

		err := validator.TestQuery(query)
		if err == nil {
			t.Error("Expected error for missing LoadTypeID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one query error
		if validationErr.NumQueryErrors() != 1 {
			t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
				validationErr.NumQueryErrors(), validationErr)
			return
		}

		// Verify it's the specific query error we expect
		if !validationErr.Contains("loadTypeID is a required field") {
			t.Errorf("Expected query error about loadTypeID field, got: %v", validationErr)
		}
	})
}
```

If we had this test in place when adding the `Warehouse` field to `GetOpenSlotsRequest`, the test would have immediately failed because:

1. Adding a non-pointer `Warehouse` struct:

```go
// common/models/appt.go
type GetOpenSlotsRequest struct {
    Start       time.Time `json:"start" validate:"required"`
    End         time.Time `json:"end" validate:"required"`
    Warehouse   Warehouse `json:"warehouse"`                 // <--- Now we need to validate (nested) fields within Warehouse that are marked as required
}
```

This will cause these test failures:

```bash
$ go test
--- FAIL: TestGetOpenSlotsValidation (0.00s)
    --- FAIL: TestGetOpenSlotsValidation/valid_query_params (0.00s)
        get_slots_test.go:25: Expected no error, got: query: warehouseID is a required field; warehouseName is a required field
    --- FAIL: TestGetOpenSlotsValidation/missing_required_LoadTypeID (0.00s)
        get_slots_test.go:52: Expected exactly 1 query validation error, got 3 query errors: query: loadTypeID is a required field; warehouseID is a required field; warehouseName is a required field
    --- FAIL: TestGetOpenSlotsValidation/missing_required_Start_time (0.00s)
        get_slots_test.go:87: Expected exactly 1 query validation error, got 3 query errors: query: start is a required field; warehouseID is a required field; warehouseName is a required field
    --- FAIL: TestGetOpenSlotsValidation/missing_required_End_time (0.00s)
        get_slots_test.go:121: Expected exactly 1 query validation error, got 3 query errors: query: end is a required field; warehouseID is a required field; warehouseName is a required field
FAIL
exit status 1
FAIL    github.com/drumkitai/drumkit/fn/api/routes/appt 0.014s
```

Looking at the test failures:
1. We expected to see just one validation error in each test
2. Instead, we got three errors every time
3. Two unexpected errors (`warehouseID` and `warehouseName`) appeared in all tests
4. This happened because the `Warehouse` struct's required fields were inherited

This early detection helps us:
1. Discover unintended validation changes
2. Prevent breaking changes from reaching production
3. Make conscious decisions about struct embedding

The validation tests catch these issues before they affect our customers.

### 2. Test Different Scenarios

Test according to what your route accepts. Look at your route handler to determine what to test:

```go
// Query parameters only (GET routes typically)
func GetOpenSlots(c *fiber.Ctx) error {
    var query GetOpenSlotsQuery
    if err := api.Parse(c, nil, &query, nil); err != nil {
        return err
    }
    // ...
}

// Request body only (POST routes typically)
func SubmitAppointment(c *fiber.Ctx) error {
    var body SubmitApptBody
    if err := api.Parse(c, nil, nil, &body); err != nil {
        return err
    }
    // ...
}

// Both query and body (PATCH/PUT routes sometimes)
func ValidateCarrier(c *fiber.Ctx) error {
    var query ValidateCarrierQuery
    var body ValidateCarrierBody
    if err := api.Parse(c, nil, &query, &body); err != nil {
        return err
    }
    // ...
}
```

Choose the corresponding test method:
```go
// For query-only routes
apitesting.TestQuery(query)

// For body-only routes
apitesting.TestBody(body)

// For routes with both
apitesting.TestBodyAndQuery(query, body)
```

Always match your tests to what the route actually uses to avoid testing unnecessary scenarios.

### 3. Common Test Cases

Always test:
- ✅ Valid data (all required fields)
- ❌ Missing required fields
- 🔄 Embedded struct validation
- 📝 Field type changes
- 🕒 Time field formatting
- ⭐ Optional vs required fields
