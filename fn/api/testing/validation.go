// Package testing provides utilities for testing API request validation.
// It supports testing query parameters, request body, or both.
// The validation errors are structured to separate query and body validation failures,
// making it clear which part of the request failed validation.
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/valyala/fasthttp"

	"github.com/drumkitai/drumkit/common/api"
)

// ValidationTest provides methods for testing request validation.
// It creates an isolated fiber.App instance for each test to ensure clean validation environments.
type ValidationTest struct {
	T   *testing.T
	app *fiber.App
}

// NewValidationTest returns a new ValidationTest instance configured for the given testing.T instance.
// Each ValidationTest gets its own fiber App to prevent cross-test contamination.
func NewValidationTest(t *testing.T) *ValidationTest {
	return &ValidationTest{
		T:   t,
		app: fiber.New(),
	}
}

// ValidationError represents a structured validation error that separates
// query parameter and request body validation failures.
type ValidationError struct {
	QueryErrors []string // Validation errors from query parameters
	BodyErrors  []string // Validation errors from request body
}

func (v *ValidationError) Error() string {
	var errors []string
	if len(v.QueryErrors) > 0 {
		errors = append(errors, "query: "+strings.Join(v.QueryErrors, "; "))
	}

	if len(v.BodyErrors) > 0 {
		errors = append(errors, "body: "+strings.Join(v.BodyErrors, "; "))
	}

	return strings.Join(errors, " | ")
}

// NumErrors returns the total count of validation errors
func (v *ValidationError) NumErrors() int {
	return len(v.QueryErrors) + len(v.BodyErrors)
}

// Contains checks if a specific validation error is present in either query or body errors
func (v *ValidationError) Contains(err string) bool {
	for _, e := range v.QueryErrors {
		if strings.Contains(e, err) {
			return true
		}
	}

	for _, e := range v.BodyErrors {
		if strings.Contains(e, err) {
			return true
		}
	}

	return false
}

// NumQueryErrors returns the count of query validation errors
func (v *ValidationError) NumQueryErrors() int {
	return len(v.QueryErrors)
}

// NumBodyErrors returns the count of body validation errors
func (v *ValidationError) NumBodyErrors() int {
	return len(v.BodyErrors)
}

// TestQuery validates query parameter parsing and validation for the given data structure.
// It converts the provided struct into URL query parameters and validates them using
// our common/api/validate.go parser. Returns ValidationError with only QueryErrors set.
//
// The data parameter should be a defined struct from the corresponding route that's a query, body, or both.
// For example, fn/api/routes/appt/get_slots.go has the `GetOpenSlotsQuery` query struct.
// This is the struct we should be testing in fn/api/routes/appt/get_slots_test.go
//
// Example usage:
//
//	query := GetOpenSlotsQuery{
//		ID: "123",
//		// LoadTypeID intentionally omitted despite being a required field
//	}
//
//	err := validator.TestQuery(query)
//	if err == nil {
//		t.Error("Expected error for missing LoadTypeID, got none")
//		return
//	}
//
//	var validationErr *apitesting.ValidationError
//	if !errors.As(err, &validationErr) {
//		t.Errorf("Expected ValidationError, got %T: %v", err, err)
//		return
//	}
//
//	// Verify we got exactly one query error
//	if validationErr.NumQueryErrors() != 1 {
//		t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
//			validationErr.NumQueryErrors(), validationErr)
//		return
//	}
//
// // Verify it's the specific query error we expect
//
//	if !validationErr.Contains("loadTypeID is a required field") {
//		t.Errorf("Expected query error about loadTypeID field, got: %v", validationErr)
//	}
func (v *ValidationTest) TestQuery(data any) error {
	ptr := reflect.New(reflect.TypeOf(data)).Interface()
	params := structToQueryParams(data)

	req := httptest.NewRequest(http.MethodGet, "/test?"+params.Encode(), nil)

	fctx := &fasthttp.RequestCtx{}
	fctx.Request.SetRequestURI(req.URL.String())
	fctx.Request.Header.SetMethod(req.Method)
	ctx := v.app.AcquireCtx(fctx)

	err := api.Parse(ctx, nil, ptr, nil)
	if err == nil {
		return nil // Successfully validated, return nil
	}

	// Only try to parse validation errors if we got an error
	errStr := err.Error()
	if !strings.HasPrefix(errStr, "query validation failed: ") {
		return err // Not a validation error, return as-is
	}

	// Parse validation errors into our structure
	errors := strings.Split(strings.TrimPrefix(errStr, "query validation failed: "), "; ")
	return &ValidationError{QueryErrors: errors}
}

// TestBody validates request body parsing and validation for the given data structure.
// It converts the provided struct to JSON and validates them using our common/api/validate.go parser.
// Returns ValidationError with only BodyErrors set.
//
// The data parameter should be a defined struct from the corresponding route that's a query, body, or both.
// For example, fn/api/routes/appt/submit_appt.go has the `SubmitApptBody` body struct.
// This is the struct we should be testing in fn/api/routes/appt/submit_appt_test.go
//
// Example usage:
//
//	body := SubmitApptBody{
//		// PONumbers intentionally omitted despite being required
//		Source:      "opendock",
//		WarehouseID: "warehouse-1",
//		Note:        "test note",
//	}
//
//	err := validator.TestBody(body)
//	if err == nil {
//		t.Error("Expected error for missing PONumbers, got none")
//		return
//	}
//
//	var validationErr *apitesting.ValidationError
//	if !errors.As(err, &validationErr) {
//		t.Errorf("Expected ValidationError, got %T: %v", err, err)
//		return
//	}
//
//	// Verify we got exactly one body error
//	if validationErr.NumBodyErrors() != 1 {
//		t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
//			validationErr.NumBodyErrors(), validationErr)
//		return
//	}
//
//	// Verify it's the specific body error we expect
//	if !validationErr.Contains("poNumbers is a required field") {
//		t.Errorf("Expected body error about poNumbers field, got: %v", validationErr)
//	}
func (v *ValidationTest) TestBody(data any) error {
	ptr := reflect.New(reflect.TypeOf(data)).Interface()

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal body: %w", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/test", bytes.NewReader(jsonData))
	req.Header.Set("Content-Type", "application/json")

	err = v.testRequest(req, nil, ptr)
	if err == nil {
		return nil
	}

	// Parse validation errors
	errStr := err.Error()
	if !strings.HasPrefix(errStr, "body validation failed: ") {
		return err
	}

	errors := strings.Split(strings.TrimPrefix(errStr, "body validation failed: "), "; ")
	return &ValidationError{BodyErrors: errors}
}

// TestBodyAndQuery validates both request body and query parameter parsing for the given data structures.
// It converts the query struct into URL parameters and the body struct to JSON, then validates both using
// our common/api/validate.go parser.
// Returns ValidationError with both QueryErrors and BodyErrors potentially set.
//
// The data parameter should be a defined struct from the corresponding route that's a query, body, or both.
// For example, fn/api/routes/truck/validate_carrier.go has the `ValidateCarrierQuery` query struct and the
// `ValidateCarrierBody` body struct.
// These are the structs we should be testing in fn/api/routes/truck/validate_carrier_test.go
//
// Example usage:
//
//	// Testing missing required query parameter
//	query := ValidateCarrierQuery{
//		// ThreadID intentionally omitted despite being required
//		EmailID: "<EMAIL>",
//	}
//
//	body := ValidateCarrierBody{
//		Carrier: models.CarrierInformation{
//			Name:  "Test Carrier",
//			ContactEmail: "<EMAIL>",
//		},
//	}
//
//	err := validator.TestBodyAndQuery(query, body)
//	if err == nil {
//		t.Error("Expected validation error, got none")
//		return
//	}
//
//	var validationErr *apitesting.ValidationError
//	if !errors.As(err, &validationErr) {
//		t.Errorf("Expected ValidationError, got %T: %v", err, err)
//		return
//	}
//
//	// Verify we got exactly one query error and no body errors
//	if validationErr.NumQueryErrors() != 1 {
//		t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
//			validationErr.NumQueryErrors(), validationErr)
//		return
//	}
//
//	if validationErr.NumBodyErrors() != 0 {
//		t.Errorf("Expected no body validation errors, got %d body errors: %v",
//			validationErr.NumBodyErrors(), validationErr)
//		return
//	}
//
//	// Verify it's the specific query error we expect
//	if !validationErr.Contains("threadID is a required field") {
//		t.Errorf("Expected query error about threadID field, got: %v", validationErr)
//	}
func (v *ValidationTest) TestBodyAndQuery(query, body any) error {
	queryPtr := reflect.New(reflect.TypeOf(query)).Interface()
	bodyPtr := reflect.New(reflect.TypeOf(body)).Interface()

	params := structToQueryParams(query)
	jsonData, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal body: %w", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/test?"+params.Encode(), bytes.NewReader(jsonData))
	req.Header.Set("Content-Type", "application/json")

	err = v.testRequest(req, queryPtr, bodyPtr)
	if err == nil {
		return nil
	}

	// Parse validation errors
	errStr := err.Error()
	var queryErrors, bodyErrors []string

	// Parse both types of errors if present
	if strings.Contains(errStr, "query validation failed: ") {
		parts := strings.Split(errStr, "query validation failed: ")
		if len(parts) > 1 {
			queryErrors = strings.Split(strings.Split(parts[1], ";")[0], "; ")
		}
	}
	if strings.Contains(errStr, "body validation failed: ") {
		parts := strings.Split(errStr, "body validation failed: ")
		if len(parts) > 1 {
			bodyErrors = strings.Split(strings.Split(parts[1], ";")[0], "; ")
		}
	}

	return &ValidationError{
		QueryErrors: queryErrors,
		BodyErrors:  bodyErrors,
	}
}

// structToQueryParams converts a struct to URL query parameters using reflection.
// It handles:
//   - Embedded structs (recursively including their fields)
//   - Required fields (always included, from validate:"required" tag)
//   - Time values (RFC3339 format for non-zero times)
//   - Optional fields (only included when non-zero)
//
// Field processing:
//   - Names come from json tag (e.g., `json:"userName"` becomes "userName=value")
//   - Fields without json tags or with `json:"-"` are skipped
//   - Required fields are always included, even if zero value
//   - Optional fields are only included when non-zero
func structToQueryParams(data any) url.Values {
	values := url.Values{}
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	t := v.Type()

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)

		// Handle embedded structs
		if field.Anonymous {
			embeddedParams := structToQueryParams(value.Interface())
			for k, v := range embeddedParams {
				values[k] = v
			}

			continue
		}

		// Get the json tag
		tag := field.Tag.Get("json")
		if tag == "" || tag == "-" {
			continue
		}
		name := strings.Split(tag, ",")[0]

		// Check if field is required
		isRequired := strings.Contains(field.Tag.Get("validate"), "required")

		// Always include required fields, even if zero
		if isRequired {
			switch v := value.Interface().(type) {
			case time.Time:
				if !v.IsZero() {
					values.Set(name, v.UTC().Format(time.RFC3339))
				}
			default:
				values.Set(name, fmt.Sprint(v))
			}

			continue
		}

		// Handle optional fields - only include if non-zero
		switch v := value.Interface().(type) {
		case time.Time:
			if !v.IsZero() {
				values.Set(name, v.UTC().Format(time.RFC3339))
			}
		default:
			if !value.IsZero() {
				values.Set(name, fmt.Sprint(v))
			}
		}
	}

	return values
}

func (v *ValidationTest) testRequest(req *http.Request, query, body any) error {
	fctx := &fasthttp.RequestCtx{}
	fctx.Request.SetRequestURI(req.URL.String())
	fctx.Request.Header.SetMethod(req.Method)

	for key, values := range req.Header {
		for _, value := range values {
			fctx.Request.Header.Add(key, value)
		}
	}

	if req.Body != nil {
		bodyBytes := new(bytes.Buffer)
		if _, err := bodyBytes.ReadFrom(req.Body); err != nil {
			return fmt.Errorf("failed to read request body: %w", err)
		}
		fctx.Request.SetBody(bodyBytes.Bytes())
	}

	ctx := v.app.AcquireCtx(fctx)
	return api.Parse(ctx, nil, query, body)
}
