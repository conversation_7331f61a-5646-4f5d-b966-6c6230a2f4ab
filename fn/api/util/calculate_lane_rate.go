package util

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

// For testing purposes
var timeNowFunc = time.Now

const carrierCostLowThreshold = 200

type (
	WeekResponseItem struct {
		Week              string  `json:"week"`
		AverageRate       float64 `json:"averageRate,omitempty"`
		LowestRate        float64 `json:"lowestRate,omitempty"`
		MaxRate           float64 `json:"maxRate,omitempty"`
		Quotes            int     `json:"quotes"`
		LowestCarrierName string  `json:"lowestCarrierName"`
		MaxRateCarrier    string  `json:"maxRateCarrierName"`
	}

	// For intermediate calculations
	WeekData struct {
		TotalRate  float64
		MinRate    float64
		MinCarrier string
		MaxRate    float64
		MaxCarrier string
		Count      int
		Rates      []float64 // Added to store all rates for percentile calculation
		Carriers   []string  // Added to track carriers corresponding to rates
	}

	CalculatedQuote struct {
		AvgDistance float64 `json:"avgDistance"`
		CountQuotes int     `json:"countQuotes"`

		AvgCost float64 `json:"avgCost"`
		MinCost float64 `json:"minCost"`
		MaxCost float64 `json:"maxCost"`

		AvgRatePerMile float64 `json:"avgRatePerMile"`
		MinRatePerMile float64 `json:"minRatePerMile"`
		MaxRatePerMile float64 `json:"maxRatePerMile"`
	}
)

func CalculateFourWeekData[T models.LaneHistoryRawDataAccessor](
	ctx context.Context,
	laneHistory []T,
	overrideLatestDate *time.Time,
) map[int]*WeekData {

	if len(laneHistory) == 0 {
		return nil
	}

	latestDate := timeNowFunc().Truncate(24 * time.Hour)
	if overrideLatestDate != nil {
		latestDate = *overrideLatestDate
	}

	weekDataMap := make(map[int]*WeekData)

	getWeekIndex := func(date time.Time) int {
		duration := latestDate.Sub(date)
		days := int(duration.Hours() / 24)
		weekIndex := days / 7
		return weekIndex
	}

	for _, item := range laneHistory {
		date, err := item.GetPickupDate()
		if err != nil {
			log.Warn(ctx, "Failed to parse PickupDate", zap.Error(err))
			continue
		}

		weekIndex := getWeekIndex(date)
		if weekIndex >= 4 {
			continue
		}

		wd, exists := weekDataMap[weekIndex]
		carrierCost := float64(item.GetTotalCarrierCost())
		if carrierCost < carrierCostLowThreshold {
			continue
		}
		if !exists {
			wd = &WeekData{
				MinRate:    carrierCost,
				MinCarrier: item.GetCarrierName(),
				MaxRate:    carrierCost,
				MaxCarrier: item.GetCarrierName(),
				Count:      0,
			}
			weekDataMap[weekIndex] = wd
		}

		wd.TotalRate += carrierCost
		if carrierCost < wd.MinRate {
			wd.MinRate = carrierCost
			wd.MinCarrier = item.GetCarrierName()
		}
		if carrierCost > wd.MaxRate {
			wd.MaxRate = carrierCost
			wd.MaxCarrier = item.GetCarrierName()
		}
		wd.Count++
	}

	return weekDataMap
}

func OrderWeekData(weekDataMap map[int]*WeekData, overrideLatestDate *time.Time) []WeekResponseItem {
	if weekDataMap == nil {
		return nil
	}

	latestDate := timeNowFunc().Truncate(24 * time.Hour)
	if overrideLatestDate != nil {
		latestDate = *overrideLatestDate
	}

	resp := []WeekResponseItem{}

	for weekIndex := 3; weekIndex > -1; weekIndex-- {
		wd, exists := weekDataMap[weekIndex]
		var avgRate float64
		var minRate, maxRate float64
		var minCarrier, maxCarrier string
		var count int

		if exists && wd.Count > 0 {
			avgRate = wd.TotalRate / float64(wd.Count)
			minRate = wd.MinRate
			maxRate = wd.MaxRate
			minCarrier = wd.MinCarrier
			maxCarrier = wd.MaxCarrier
			count = wd.Count
		} else {
			avgRate = 0.0
			minRate = 0.0
			maxRate = 0.0
			minCarrier = ""
			maxCarrier = ""
			count = 0
		}

		weekEnd := latestDate.AddDate(0, 0, -7*weekIndex)
		weekStart := weekEnd.AddDate(0, 0, -6)

		res := WeekResponseItem{
			Week:              weekStart.Format("Jan 2") + " - " + weekEnd.Format("Jan 2"),
			Quotes:            count,
			AverageRate:       avgRate,
			LowestRate:        minRate,
			MaxRate:           maxRate,
			LowestCarrierName: minCarrier,
			MaxRateCarrier:    maxCarrier,
		}
		resp = append(resp, res)
	}

	return resp
}

// Generate avg, min, and max rates for a given set of loads or lane history items
func CalculateQuote[T models.LaneHistoryRawDataAccessor](items []T) *CalculatedQuote {
	if len(items) == 0 {
		return nil
	}

	var avgDistance float64
	var avgCost float64
	var minCost = float64(items[0].GetTotalCarrierCost())
	var maxCost float64

	var countQuotes int

	for _, item := range items {
		cost := item.GetTotalCarrierCost()
		// ignore outliers or cancelled loads
		if cost < 200 {
			continue
		}
		// Distance should be the same for the lane, but in case it's not, we average
		avgDistance += float64(item.GetTotalDistance())
		avgCost += float64(cost)
		minCost = util.Min(minCost, float64(cost))
		maxCost = util.Max(maxCost, float64(cost))

		countQuotes++
	}

	avgDistance /= float64(countQuotes)
	avgCost /= float64(countQuotes)

	var avgRatePerMile = avgCost / avgDistance
	var minRatePerMile = minCost / avgDistance
	var maxRatePerMile = maxCost / avgDistance

	return &CalculatedQuote{
		AvgDistance: avgDistance,
		CountQuotes: countQuotes,

		AvgCost: avgCost,
		MinCost: minCost,
		MaxCost: maxCost,

		AvgRatePerMile: avgRatePerMile,
		MinRatePerMile: minRatePerMile,
		MaxRatePerMile: maxRatePerMile,
	}
}
