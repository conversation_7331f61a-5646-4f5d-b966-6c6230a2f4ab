package main

import (
	"context"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/fn/api/aws"
	"github.com/drumkitai/drumkit/fn/api/env"
)

func main() {
	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}
	log.Debug(ctx, "IN DEBUG MODE")

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.S3BucketName != "" {
		var err error
		if aws.S3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName); err != nil {
			panic(err)
		}
	}

	api.InitializeOAuthGoogle(env.Vars.GoogleClientID, env.Vars.GoogleClientSecret)

	app := buildApp()
	defer func() {
		if err := app.Shutdown(); err != nil {
			log.Error(ctx, "Error shutting down server", zap.Error(err))
		}
		// Flush logs before exit
		log.Flush(ctx)
	}()

	ip, err := httputil.GetIPAddress()
	if err != nil {
		log.Warn(ctx, "error retrieving public IP address", zap.Error(err))
	} else {
		log.Info(ctx, "retrieved public IP address", zap.String("IPAddress", ip))
	}

	handler := func(event events.APIGatewayProxyRequest) (*events.APIGatewayProxyResponse, error) {
		return api.GatewayHandler(ctx, &event)
	}

	if api.IsLambda() {
		go api.RunServer(ctx, app)
		lambda.Start(otellambda.InstrumentHandler(handler))
	} else {
		// During local dev, run AutoMigrate automatically
		if err := rds.AutoMigrate(ctx); err != nil {
			panic(err)
		}

		api.RunServer(ctx, app)
	}
}
