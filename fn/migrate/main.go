package main

import (
	"context"
	"fmt"
	"os"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/sentry"
)

type envVars struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv            string `envconfig:"APP_ENV" required:"true"`
	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
}

const (
	serviceName    = "migrate-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"              // Version of the service.
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	if env := os.Getenv("APP_ENV"); env == "" || env == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file", zap.Error(err))
		}
	}

	var env envVars
	envconfig.MustProcess("", &env)

	ctx = log.NewFromEnv(ctx)

	if err := rds.Open(
		ctx,
		env.EnvConfig,
		rds.WithAppEnv(env.AppEnv),
		rds.WithAWSSecretsManager(env.AppEnv == "staging" || env.AppEnv == "prod"),
	); err != nil {
		panic(err)
	}

	if env.AppEnv == "dev" {
		_ = handlerWithLogging(ctx, nil) //nolint:errcheck // already logged in handler
		return
	}

	exporter, err := otlptracehttp.New(context.Background(),
		otlptracehttp.WithHeaders(map[string]string{
			"Authorization":   fmt.Sprintf("Bearer %s", env.AxiomToken),
			"X-AXIOM-DATASET": env.AxiomTraceDataset,
		}),
	)
	if err != nil {
		log.Error(ctx, "error initializing OTLP exporter", zap.Error(err))
		return
	}

	tp := trace.NewTracerProvider(
		trace.WithBatcher(exporter),
		trace.WithResource(Resource()),
	)

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging,
		otellambda.WithTracerProvider(tp),
		otellambda.WithFlusher(tp)))
}

func handlerWithLogging(ctx context.Context, event any) (err error) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		hub := sentry.GetHubFromContext(ctx)

		// If there is an error, flush it to Sentry before the Lambda invocation is finished.
		defer hub.Flush(sentry.FlushTimeout)

		ctx = log.NewFromEnv(ctx)

		// Log Lambda result and report errors to Sentry
		defer func() {
			if err != nil {
				log.Error(ctx, "returning error", zap.Error(err))
			}
		}()

		log.Info(ctx, "received event", zap.Any("event", event))

		err = rds.AutoMigrate(ctx)
	})

	return
}
