package main

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
)

func runLocalServer() error {
	app := fiber.New()

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/watchinbox", fiberHandler)

	return app.Listen(devPort)
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	log.Info(ctx, "refreshing tokens in drumkit-watch-inbox handler", zap.Any("time", time.Now()))

	result, err := handler(ctx)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(result.StatusCode).SendString(result.Body)
}
