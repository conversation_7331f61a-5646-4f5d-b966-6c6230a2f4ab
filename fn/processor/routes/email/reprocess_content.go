package email

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/log"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
)

type (
	ReprocessContentPath struct {
		EmailID uint `params:"id" validate:"required"`
	}

	ReprocessContentBody struct {
		LabelsToReprocess []emails.EmailLabel `json:"labelsToReprocess"`
	}
)

func ReprocessContent(c *fiber.Ctx) error {
	var path ReprocessContentPath
	var body ReprocessContentBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("emailID", path.EmailID))

	email, err := emailDB.GetByID(ctx, path.EmailID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("email not found")
		}

		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	var labelsToReprocessStr []string
	for _, label := range body.LabelsToReprocess {
		labelsToReprocessStr = append(labelsToReprocessStr, string(label))
	}

	if err := emails.ReprocessContent(ctx, &email, labelsToReprocessStr); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Info(ctx, "Email reprocessed successfully", zap.Uint("emailID", path.EmailID))

	return nil
}
