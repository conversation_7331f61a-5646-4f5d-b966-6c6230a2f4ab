package main

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/processor/routes/email"
)

const (
	devPort                 = ":5005"
	devPathForIngestion     = "/invoke"
	pathForOnPrem           = "/onprem/email"
	pathForReprocessContent = "/reprocess-content/:id"
)

type Request struct {
	Email models.IngestedEmail
}

func runLocalServer() error {

	app := fiber.New()

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	// In prod, this is an actual API Gateway endpoint.
	app.Post(pathForOnPrem, email.ProcessOnPremEmail)
	app.Post(pathForReprocessContent, email.ReprocessContent)

	// In prod, this is triggered by SQS. In dev, behavior is mimicked by the local server.
	app.Post(devPathForIngestion, func(c *fiber.Ctx) error {
		ctx := c.UserContext()
		var payload Request
		if err := json.Unmarshal(c.Body(), &payload.Email); err != nil {
			panic(err)
		}

		if err := handleLocalEvent(ctx, payload.Email); err != nil {
			log.ErrorNoSentry(ctx, "error handling local event", zap.Error(err))
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusOK).SendString("Processed email successfully!")
	})

	return app.Listen(devPort)
}

func handleLocalEvent(ctx context.Context, msg models.IngestedEmail) error {
	log.Info(ctx, "dev: received email", zap.Any("msg", msg))

	return startProcessing(ctx, msg)
}
