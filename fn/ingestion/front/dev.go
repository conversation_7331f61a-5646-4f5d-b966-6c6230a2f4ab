package main

import (
	"encoding/json"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/ingestion"
)

const frontDevPort = ":5007"

func runLocalServer() error {

	var localSQS ingestion.LocalSQSClient
	sqsClient = &localSQS

	app := fiber.New()
	app.Use(middleware.Zap())

	app.Post("/inboxWebhook", fiberHandler)

	return app.Listen(frontDevPort)
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	var event frontclient.FrontEvent
	if err := c.BodyParser(&event); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Convert Fiber POST request into the Front payload expected by the app
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("invalid json body: " + err.Error())
	}

	eventParsed := events.LambdaFunctionURLRequest{
		Body:    string(eventJSON),
		RawPath: c.Path(),
	}

	log.Info(ctx, "sending event to beacon-ingestion-front handler", zap.Any("event", eventParsed))

	result := handler(ctx, eventParsed)

	return c.Status(result.StatusCode).SendString(result.Body)
}
