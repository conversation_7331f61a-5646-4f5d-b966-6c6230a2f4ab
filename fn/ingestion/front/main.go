package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"slices"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/util/jsoncfg"
	"github.com/drumkitai/drumkit/common/util/otel"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/front/env"
)

var sqsClient helpers.SQSAPI

const (
	serviceName    = "ingestion-front-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"                      // Version of the service.
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if !api.IsLambda() {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.WarnNoSentry(ctx, "Error running migrations", zap.Error(err))
		}

		panic(runLocalServer())
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		panic(err)
	}
	sqsClient = sqs.NewFromConfig(cfg)

	lambda.Start(otellambda.InstrumentHandler(handlerWithSentry))
}

func handlerWithSentry(
	ctx context.Context,
	request events.LambdaFunctionURLRequest,
) (result events.LambdaFunctionURLResponse, _ error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		ctx = log.NewFromEnv(ctx, zap.String("path", request.RawPath))
		result = handler(ctx, request)
	})

	return
}

func handler(ctx context.Context, request events.LambdaFunctionURLRequest) events.LambdaFunctionURLResponse {
	// Payloads don't include sensitive tokens or msg contents - safe to log
	log.Info(ctx, "received request", zap.Any("request", request))

	var data frontclient.FrontEvent
	if err := json.Unmarshal([]byte(request.Body), &data); err != nil {
		log.Error(ctx, "request body parsing failed", zap.Error(err))

		return events.LambdaFunctionURLResponse{StatusCode: http.StatusBadRequest}
	}

	log.Info(ctx, "received front webhook", zap.Any("wh", data))

	if data.Type != frontclient.FrontEventTypeInbound {
		log.WarnNoSentry(ctx, "received Front webhook event isn't inbound message", zap.Any("type", data.Type))
		return events.LambdaFunctionURLResponse{StatusCode: http.StatusBadRequest}
	}

	if err := processEvent(ctx, data); err != nil {
		return events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}
	}

	return events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}
}

func extractSubdomainFromEvent(url string) (string, error) {
	// Regular expression to match the subdomain (first part before ".api")
	re := regexp.MustCompile(`https://([a-zA-Z0-9-]+)\.api\.frontapp\.com`)
	matches := re.FindStringSubmatch(url)

	if len(matches) < 2 {
		return "", fmt.Errorf("subdomain not found")
	}

	return matches[1], nil
}

func processEvent(ctx context.Context, e frontclient.FrontEvent) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "processMsgFront", nil)
	defer func() {
		// NOTE: Error logged in startProcessing so it contains zap metadata (ENGB-2300)
		if err != nil {
			log.Error(ctx, "failed to process front webhook", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	frontSubdomain, err := extractSubdomainFromEvent(e.MetaLinks.Self)
	if err != nil {
		return fmt.Errorf("failed to extract subdomain from event: %w", err)
	}

	service, err := rds.GetFrontServiceBySubdomain(ctx, frontSubdomain)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting front service", zap.Error(err))
		return fmt.Errorf("failed to get front service: %w", err)
	}

	front, err := frontclient.New()
	if err != nil {
		log.WarnNoSentry(ctx, "error creating front client", zap.Error(err))
		return fmt.Errorf("failed to create front client: %w", err)
	}

	// We must fetch details about the Front message (email) that the received event is about
	fullMsg, err := front.GetMessageByID(ctx, e.Target.Data.MetaLinks.Self, service)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting Front message", zap.Error(err))
		return fmt.Errorf("failed to get Front message: %w", err)
	}

	log.Info(ctx, "processing messages")

	matchedUsers := matchRecipientsToUsers(ctx, front, service, *fullMsg)
	if err != nil {
		log.WarnNoSentry(ctx, "error matching Front message recipients to Drumkit user", zap.Error(err))
	}

	if len(matchedUsers) == 0 {
		log.WarnNoSentry(ctx, "front message has no valid recipients", zap.Any("msg", fullMsg.Recipients))
		return nil
	}

	frontMsg := emails.FrontMessage{Message: fullMsg}

	// Create a copy of email for each recipient address that either exists in Drumkit or is assigned to a Front channel
	for i := range matchedUsers {
		payload, err := emails.PrepareEmailPayload(
			ctx,
			&frontMsg,
			emails.WithEmailAddress(matchedUsers[i].EmailAddress),
			emails.WithUser(&matchedUsers[i]),
		)
		if err != nil {
			return fmt.Errorf("unable to prepare email payload: %w", err)
		}

		payloadBytes, err := jsoncfg.SpaceEfficientConfig.Marshal(payload)
		if err != nil {
			return fmt.Errorf("json marshal of sqs payload failed: %w", err)
		}

		log.Info(
			ctx,
			"sending SQS payload",
			zap.String("sqsUrl", env.Vars.SQSQueueURL),
			zap.Any("payload", payload.Sanitize()),
		)

		if _, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
			MessageBody: aws.String(string(payloadBytes)),
			QueueUrl:    &env.Vars.SQSQueueURL,
		}); err != nil {
			return fmt.Errorf("sqs send message to %s failed: %w", env.Vars.SQSQueueURL, err)
		}
	}

	return nil
}

func matchRecipientsToUsers(
	ctx context.Context,
	front frontclient.Client,
	service models.Service,
	msg frontclient.Message,
) []models.User {

	matchedUsers := []models.User{}

	channels, err := front.GetChannelList(ctx, service)
	if err != nil {
		log.Warn(ctx, "error getting Front channel list", zap.Error(err))
	}

	serviceEmailAddresses := front.GetServiceEmailAddressesAssignedToChannels(service, channels)

	// TODO: Implement workaround for BCC'd email addresses, as they aren't included in the recipients array.
	for _, recipient := range msg.Recipients {
		if recipient.Role == "to" || recipient.Role == "cc" {
			user, err := userDB.GetByEmail(ctx, recipient.Handle)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					log.WarnNoSentry(ctx, "user not found", zap.String("email", recipient.Handle))

					if slices.Contains(serviceEmailAddresses, recipient.Handle) {
						log.Info(
							ctx,
							"user not found but it's from a valid Front channel, creating user",
							zap.String("email", recipient.Handle),
						)

						user, err := createUserForIngestion(ctx, recipient.Handle, service)
						if err != nil {
							log.Error(ctx, "error creating user", zap.Error(err))
							continue
						}

						matchedUsers = append(matchedUsers, user)
						continue
					}

					log.WarnNoSentry(
						ctx,
						"user not found but it's not assigned to any Front channel",
						zap.String("email", recipient.Handle),
					)
					continue
				}

				log.Error(ctx, "error getting user", zap.Error(err))
				continue
			}

			matchedUsers = append(matchedUsers, user)
		}
	}

	return matchedUsers
}

func createUserForIngestion(ctx context.Context, email string, service models.Service) (models.User, error) {
	user := models.User{
		EmailAddress:  email,
		EmailProvider: models.FrontEmailProvider,
		ServiceID:     service.ID,
	}

	err := userDB.Create(ctx, &user)
	if err != nil {
		log.Error(ctx, "error creating user for ingestion", zap.Error(err))
		return models.User{}, err
	}

	return user, nil
}
