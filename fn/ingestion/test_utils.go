package ingestion

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

// Interface and mock functions for unit testing
type SQSAPI interface {
	SendMessage(context.Context, *sqs.SendMessageInput, ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
}

type MockSQSClient struct {
	SentMessages []string
}

func (c *MockSQSClient) SendMessage(
	_ context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	c.SentMessages = append(c.SentMessages, aws.ToString(input.MessageBody))
	return &sqs.SendMessageOutput{}, nil
}
