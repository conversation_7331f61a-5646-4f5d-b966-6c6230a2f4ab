package main

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/oauth"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/outlook/env"
)

const (
	clientState = "TestClientState"
	testTenant  = "TestTenant"
	externalID  = "OutlookUser1"
)

//nolint:lll
const webhookBody = ` {"value":[{"subscriptionId":"test-subscription","subscriptionExpirationDateTime":"","changeType":"created","resource":"Users/OutlookUser1/Messages/msg2","resourceData":{"@odata.type":"","@odata.id":"","@odata.etag":"","id":"msg2"},"clientState":"TestClientState","tenantId":"TestTenant"}]}`

var (
	defaultMsgs = map[string]msclient.Message{
		"msg1": {
			// Intentionally different from map key to differentiate in tests
			ID: "alpha",
			Body: &msclient.Body{
				ContentType: "text",
				Content:     "This is the first message",
			},
		},
		"msg2": {
			ID: "beta",
			Body: &msclient.Body{
				ContentType: "text",
				Content:     "This is the second message",
			},
		},
	}

	user = models.User{
		Model:                 gorm.Model{ID: 1},
		MailClientID:          externalID,
		EmailAddress:          "<EMAIL>",
		OutlookClientState:    clientState,
		WebhookExpiration:     time.Now().Add(48 * time.Hour),
		TokenExpiry:           time.Now().Add(48 * time.Hour),
		OutlookSubscriptionID: "test-subscription",
	}

	defaultGetUser = func(context.Context, string, string, string) (models.User, error) {
		return user, nil
	}

	defaultUpdateUser = func(context.Context, models.User) error {
		return nil
	}

	defaultGetEmailFunc = func(_ context.Context, _ string) (*models.Email, error) {
		return nil, gorm.ErrRecordNotFound
	}

	defaultGetServiceByID = func(context.Context, uint) (res models.Service, _ error) {
		res.ID = 1
		return res, nil
	}
)

func TestIngestionHandler(t *testing.T) {
	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		// Make UpdatedAt != CreatedAt to force non-backfill logic
		res.UpdatedAt = time.Now()

		return res, nil
	}

	getEmailFunc = defaultGetEmailFunc
	updateUser = defaultUpdateUser
	dbGetServiceFunc = defaultGetServiceByID

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify only 1 message sent to S3/SQS
	assert.Equal(t, map[string]bool{"beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 1)
}

func TestIngestionHandlerNewUserBackfill(t *testing.T) {
	env.Vars.BackfillHours = 14 * 24

	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-2*time.Second), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	getEmailFunc = defaultGetEmailFunc
	dbGetServiceFunc = defaultGetServiceByID

	getUserByOutlookIDs = defaultGetUser
	updateUser = defaultUpdateUser

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
		fmt.Sprintf("ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 3)
	assert.Equal(t, 2, backfillCalls)
}

func TestIngestionHandlerStaleUserBackfill(t *testing.T) {
	env.Vars.BackfillHours = 14 * 24

	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		context.Context,
		string, string,
		models.UserAccessor,
		...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		res.UpdatedAt = time.Now().Add(-80 * time.Hour)

		return res, nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-2*time.Second), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}
	updateUser = defaultUpdateUser
	getEmailFunc = defaultGetEmailFunc

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
		fmt.Sprintf("ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 3)
	assert.Equal(t, 2, backfillCalls)
}

func TestIngestionHandlerConcurrentBackfill(t *testing.T) {
	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		res.BackfillStartTime = models.NullTime{Time: time.Now(), Valid: true}

		return res, nil
	}

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}

	updateBackfillLock = func(_ context.Context, _ *models.User, _ models.NullTime) error {
		return fmt.Errorf("unexpected call to updateBackfillLock")
	}

	updateUser = defaultUpdateUser
	getEmailFunc = defaultGetEmailFunc

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{"GetMessageByID(msg2)"}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 1)
}

func TestIngestionHandlerIncompleteBackfill(t *testing.T) {
	env.Vars.BackfillHours = 14 * 24

	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	// Replace DB funcs
	updateUser = defaultUpdateUser
	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		res.BackfillStartTime = models.NullTime{Time: time.Now().Add(-15 * time.Minute), Valid: true}

		return res, nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-2*time.Second), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	getEmailFunc = defaultGetEmailFunc

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
		fmt.Sprintf("ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 3)
	assert.Equal(t, 2, backfillCalls)
}

func TestIngestionHandlerRewatchInbox(t *testing.T) {
	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	updateUser = defaultUpdateUser

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}

	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		res.UpdatedAt = time.Now()
		res.WebhookExpiration = time.Now()

		return res, nil
	}

	getEmailFunc = defaultGetEmailFunc

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"RewatchInbox(1)",
		"GetMessageByID(msg2)",
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"beta": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 1)
}

func TestIngestionHandlerDuplicateEmails(t *testing.T) {
	env.Vars.BackfillHours = 24 * 3
	env.Vars.SkipDuplicates = true

	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		context.Context,
		string, string,
		models.UserAccessor,
		...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user

		return res, nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-2*time.Second), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	// Only "alpha" email should be processed
	getEmailFunc = func(_ context.Context, externalID string) (*models.Email, error) {
		switch externalID {
		case "alpha":
			return nil, gorm.ErrRecordNotFound
		case "beta":
			return &models.Email{ExternalID: externalID}, nil
		}

		return nil, fmt.Errorf("unexpected ID: %s", externalID)
	}

	updateUser = defaultUpdateUser

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS helpers.MockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
		fmt.Sprintf("ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -3).Format(time.DateOnly)),
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify only 1 message sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.SentMessages, 1)
}

// Test helpers
func TestExtractUserID(t *testing.T) {
	t.Run("OK", func(t *testing.T) {
		//nolint:lll
		res, err := extractUserID("Users/b5346c76-d251-4e32-b72e-b4bd003e9f88/Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA")
		require.NoError(t, err)
		assert.Equal(t, "b5346c76-d251-4e32-b72e-b4bd003e9f88", res)
	})

	t.Run("Error expected", func(t *testing.T) {
		res, err := extractUserID("Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA")
		require.Error(t, err)
		assert.Empty(t, res)
	})

	t.Run("Switched order", func(t *testing.T) {
		//nolint:lll
		res, err := extractUserID("Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA/Users/<USER>")
		require.NoError(t, err)
		assert.Equal(t, "b5346c76-d251-4e32-b72e-b4bd003e9f88", res)
	})
}
