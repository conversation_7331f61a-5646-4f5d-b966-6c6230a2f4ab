package main

type OutlookWebhook struct {
	Value []Value `json:"value"`
}

type Value struct {
	SubscriptionID                 string       `json:"subscriptionId"`
	SubscriptionExpirationDateTime string       `json:"subscriptionExpirationDateTime"`
	ChangeType                     string       `json:"changeType"`
	Resource                       string       `json:"resource"`
	ResourceData                   ResourceData `json:"resourceData"`
	ClientState                    string       `json:"clientState"`
	TenantID                       string       `json:"tenantId"`
}

type ResourceData struct {
	ODataType string `json:"@odata.type"`
	ODataID   string `json:"@odata.id"`
	ODataEtag string `json:"@odata.etag"`
	// Not immutable by default; add IdType="ImmutableId" to request header for immutable IDs.
	// https://learn.microsoft.com/en-us/graph/outlook-immutable-id
	ID string `json:"id"`
}
