package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/util/jsoncfg"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/outlook/env"
)

var (
	// Unit tests can replace these functions
	getUserByOutlookIDs = userDB.GetByOutlookIDs
	updateUser          = userDB.Update
	updateBackfillLock  = userDB.UpdateBackfillLock
	getEmailFunc        = emailDB.GetEmailByExternalID
	outlookConstructor  = msclient.New[models.UserAccessor]
	dbGetServiceFunc    = rds.GetServiceByID

	s3Uploader s3backup.Archiver
	sqsClient  helpers.SQSAPI
)

const (
	serviceName    = "ingestion-outlook-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"                        // Version of the service.
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
	); err != nil {
		panic(err)
	}

	if env.Vars.S3BucketName != "" {
		var err error
		if s3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName); err != nil {
			panic(err)
		}
	}

	if !api.IsLambda() {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.WarnNoSentry(ctx, "Error running migrations", zap.Error(err))
		}

		panic(runLocalServer())
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		panic(err)
	}
	sqsClient = sqs.NewFromConfig(cfg)

	lambda.Start(otellambda.InstrumentHandler(handlerWithSentry))
}

func handlerWithSentry(
	ctx context.Context,
	request events.LambdaFunctionURLRequest,
) (result events.LambdaFunctionURLResponse, _ error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		ctx = log.NewFromEnv(ctx, zap.String("path", request.RawPath))
		result = handler(ctx, request)
	})

	return
}

func handler(ctx context.Context, request events.LambdaFunctionURLRequest) events.LambdaFunctionURLResponse {
	// Payloads don't include sensitive tokens or msg contents - safe to log
	log.Info(ctx, "received request", zap.Any("request", request))

	// Confirm outlook subscription
	if token := request.QueryStringParameters["validationToken"]; token != "" {
		log.Info(ctx, "received validation request - confirming subscription")

		return events.LambdaFunctionURLResponse{
			StatusCode: http.StatusOK,
			Headers:    map[string]string{"Content-Type": "text/plain"},
			Body:       token,
		}
	}

	var data OutlookWebhook
	if err := json.Unmarshal([]byte(request.Body), &data); err != nil {
		log.Error(ctx, "request body parsing failed", zap.Error(err))

		return events.LambdaFunctionURLResponse{StatusCode: http.StatusBadRequest}
	}

	log.Info(ctx, "received outlook webhook", zap.Any("wh", data))
	if count := len(data.Value); count > 1 {
		log.Info(ctx, "webhook contains more than 1 item", zap.Int("count", count))
	}

	for _, v := range data.Value {
		if err := processMsg(ctx, v); err != nil {
			return events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}
		}
	}

	return events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}
}

func processMsg(ctx context.Context, v Value) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "processMsgOutlook", nil)
	defer func() {
		// NOTE: Error logged in startProcessing so it contains zap metadata (ENGB-2300)
		if err != nil {
			// Ignore logging temporary service errors
			if !strings.Contains(err.Error(), "429") &&
				!strings.Contains(err.Error(), "503") &&
				!strings.Contains(err.Error(), "504") {

				log.Error(ctx, "failed to process webhook", zap.Error(err))
			}

		}
		metaSpan.End(err)
	}()

	userExternalID, err := extractUserID(v.Resource)
	if err != nil {
		return err
	}

	user, err := getUserByOutlookIDs(ctx, userExternalID, v.ClientState, v.SubscriptionID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// NotFound can occur if the user exists but not that subscriptionID, meaning there are duplicate subs
			if stopErr := stopWatchingInbox(ctx, v.SubscriptionID, userExternalID); stopErr == nil {
				log.Info(ctx, "deleted unknown subscription", zap.String("subID", v.SubscriptionID))

				return nil
			} else if !errors.Is(stopErr, gorm.ErrRecordNotFound) {
				// NOTE: Subscriptions are short-lived, so if this fails, it will expire by itself in <3 days
				log.WarnNoSentry(ctx, "error canceling subscription", zap.Error(stopErr))
			}
		}

		return fmt.Errorf("unable to find user with externalID %s and clientState %s: %w",
			userExternalID, v.ClientState, err)
	}

	service, err := dbGetServiceFunc(ctx, user.ServiceID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting service feature flags, continuing", zap.Error(err))
	}
	ctx = log.With(ctx, zap.String("emailAddress", user.EmailAddress), zap.String("resource", v.Resource))

	client, err := outlookConstructor(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		return err
	}

	// Subscriptions are short-lived; we must re-subscribe
	if hoursLeft := time.Until(user.WebhookExpiration).Hours(); hoursLeft < 24 {
		rewatchInbox(ctx, &user, client)
	}

	msgs := make([]msclient.Message, 1)
	msgs[0], err = client.GetMessageByID(
		ctx,
		v.ResourceData.ID,
		msclient.WithContentType(msclient.HTMLContentType),
	)
	if err != nil {
		// Check if it's a "resource not found" error and handle it gracefully
		if strings.Contains(err.Error(), "resource not found") ||
			strings.Contains(err.Error(), "ResourceNotFound") ||
			strings.Contains(err.Error(), "404") {

			log.Info(
				ctx,
				"message not found, may have been deleted",
				zap.String("messageId", v.ResourceData.ID),
				zap.String("userEmail", user.EmailAddress),
				zap.Error(err),
			)

			return nil
		}

		return fmt.Errorf("client.ByMessageId.Get(%s) failed: %w", v.ResourceData.ID, err)
	}

	backfillMsgs, err := getMsgsToBackfill(ctx, &user, client)
	// keep track of if we're running a backfill or not, for later processing logic
	backfilling := false
	if err != nil {
		log.Error(ctx, "backfill error", zap.Error(err))
	} else if len(backfillMsgs) > 0 {
		backfilling = true
		// Unlike Gmail, if there's a backfill already in progress, we should still process the new message
		// in this new webhook as the backfill's L14D query may not have included it & Outlook won't retry
		msgs = append(backfillMsgs, msgs...)

		defer func() {
			log.Info(ctx, "removing backfill lock")

			if err = updateBackfillLock(ctx, &user, models.NullTime{}); err != nil {
				log.Error(ctx, "error removing DB backfill lock", zap.Error(err))
			}
		}()
	}

	log.Info(ctx, "processing messages", zap.Int("count", len(msgs)))

	for _, msg := range msgs {
		ctx = log.With(
			ctx,
			zap.String("msgExternalId", msg.ID),
			zap.String("msgThreadId", msg.ConversationID),
			zap.String("msgSubject", msg.Subject),
		)
		// Add filter check before processing
		if !msclient.ShouldProcessMessage(ctx, msg, user.EmailAddress) {
			continue
		}

		if env.Vars.SkipDuplicates {
			_, err = getEmailFunc(ctx, msg.ID)
			if err == nil {
				log.Info(ctx, "skipping duplicate message")
				continue
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.ErrorNoSentry(ctx, "error querying email", zap.Error(err))
			}
		}
		// If record not found, send to processor. If other error, occasional duplicate processing is fine

		// Optimization/Privacy:
		// If the service is not enabled for load building or quote request extraction, then we don't need to
		// process attachments. If there was an error fetching the service, then we fail-open and process attachments.
		var s3Attachments []models.Attachment
		var hasPDFs bool
		if emails.ShouldProcessAttachments(&service) {
			s3Attachments, hasPDFs, err = processAttachments(ctx, client, user.EmailAddress, msg.ID)
			if err != nil {
				if !strings.Contains(err.Error(), "resource not found") &&
					!strings.Contains(err.Error(), "error reading response body") &&
					!strings.Contains(err.Error(), "504") {

					log.Error(ctx, "Failed to process attachments", zap.Error(err))
				}
			}
		}

		v := msg
		outlookMsg := emails.OutlookMessage{Message: &v}

		payload, err := emails.PrepareEmailPayload(
			ctx,
			&outlookMsg,
			emails.WithEmailAddress(user.EmailAddress),
			emails.WithUser(&user),
			emails.WithS3URL("temp-s3-url"),
			emails.WithAttachments(s3Attachments),
			emails.WithHasPDFs(hasPDFs),
		)
		if err != nil {
			return fmt.Errorf("unable to prepare email payload: %w", err)
		}

		var s3URL string
		if s3Uploader != nil {
			s3URL, err = s3Uploader.Outlook(ctx, payload, user.EmailAddress)
			if err != nil {
				// fail-open: continue processing even if S3 archive failed
				log.Error(ctx, "s3 archive failed", zap.Error(err))
			}
		}

		payload.S3URL = s3URL

		payloadBytes, err := jsoncfg.SpaceEfficientConfig.Marshal(payload)
		if err != nil {
			return fmt.Errorf("json marshal of sqs payload failed: %w", err)
		}

		log.Info(
			ctx,
			"sending SQS payload",
			zap.String("sqsUrl", env.Vars.SQSQueueURL),
			zap.Any("payload", payload.Sanitize()),
		)

		if _, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
			MessageBody: aws.String(string(payloadBytes)),
			QueueUrl:    &env.Vars.SQSQueueURL,
		}); err != nil {
			return fmt.Errorf("sqs send message to %s failed: %w", env.Vars.SQSQueueURL, err)
		}

		// when backfilling only, sleep for 100 milliseconds after each iteration to slow down processing
		if backfilling {
			time.Sleep(100 * time.Millisecond)
		}
	}

	// if backfilling, log complete msg for easy searching in cloudwatch
	if backfilling {
		log.Info(ctx, fmt.Sprintf("backfill complete, processed %d msgs from user", len(msgs)))
	}

	return nil
}

func extractUserID(input string) (string, error) {
	parts := strings.Split(input, "/")

	var userID string
	found := false

	for i, part := range parts {
		if strings.EqualFold(part, "Users") && i+1 < len(parts) {
			userID = parts[i+1]
			found = true
			break
		}
	}

	if !found {
		return "", fmt.Errorf("userID not found in the input string")
	}

	return userID, nil
}

func rewatchInbox(ctx context.Context, user *models.User, client msclient.Client) {
	// We can only rewatch an inbox here, so we pass in an empty string for the webhook url as we can't
	// create a new subscription.
	// The `WatchInbox` lambda handles the aforementioned edge case, if we run into a 404 when rewatching
	// the inbox.
	updatedSub, err := client.RewatchInbox(ctx, "", user)
	if err != nil {
		log.Warn(ctx, "error re-subscribing to Outlook inbox", zap.Error(err))
		return
	}
	log.Info(ctx, fmt.Sprint("successfully re-subscribed to inbox until ", updatedSub.ExpirationDateTime))

	user.WebhookExpiration = updatedSub.ExpirationDateTime
	if err = updateUser(ctx, *user); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(ctx, "error updating subscription expiration", zap.Error(err))
	}

}

func stopWatchingInbox(ctx context.Context, subID, userExternalID string) error {
	user, err := userDB.GetByExternalID(ctx, userExternalID)
	if err != nil {
		return err
	}

	client, err := outlookConstructor(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		return err
	}

	return client.StopWatchingInbox(ctx, subID)
}

func getMsgsToBackfill(
	ctx context.Context,
	user *models.User,
	client msclient.Client,
) (msgs []msclient.Message, err error) {

	isNew := user.CreatedAt.Equal(user.UpdatedAt)
	isStale := time.Since(user.UpdatedAt).Hours() > 72

	if !isNew && !isStale {
		return msgs, nil
	}

	if !helpers.ShouldBackfill(ctx, *user) {
		// Backfill already in progress; process just the 1 message from the webhook
		log.Info(ctx, fmt.Sprintf("backfill in progress since %s", user.BackfillStartTime.Time))
		return msgs, nil
	}

	if isNew {
		log.Info(ctx, "backfilling new user")
	} else {
		log.Info(ctx, "backfilling stale user", zap.Any("time_since_update", time.Since(user.UpdatedAt).Hours()))
	}

	backfillStartTime := models.NullTime{Time: time.Now(), Valid: true}
	if err = updateBackfillLock(ctx, user, backfillStartTime); err != nil {
		return msgs, fmt.Errorf("error acquiring DB lock to backfill: %w", err)
	}

	msgs, err = client.ListMessagesAfterDate(ctx, time.Now().Add(-time.Duration(env.Vars.BackfillHours)*time.Hour))
	if err != nil {
		return msgs, fmt.Errorf("error getting msgs from last %d hours: %w", env.Vars.BackfillHours, err)
	}

	return msgs, nil
}

func processAttachments(
	ctx context.Context,
	client msclient.Client,
	account,
	msgID string,
) (res []models.Attachment, hasPDFs bool, err error) {

	attachments, err := client.GetMessageAttachmentsByID(ctx, msgID)
	if err != nil {
		return nil, hasPDFs, fmt.Errorf("failed to get attachments: %w", err)
	}

	if len(attachments) == 0 {
		log.Info(ctx, "No attachments found for message", zap.String("msgID", msgID))
		return nil, hasPDFs, nil
	}

	for _, attachment := range attachments {
		if attachment.ODataType != "#microsoft.graph.fileAttachment" {
			continue
		}

		ctx = log.With(
			ctx,
			zap.String("attachmentName", attachment.Name),
			zap.String("attachmentType", attachment.ContentType),
		)

		// LLM Load Building and Quote Request pipelines currently only support PDFs for now
		if strings.Contains(strings.ToLower(attachment.ContentType), "pdf") {
			hasPDFs = true
		}

		data, err := base64.StdEncoding.DecodeString(attachment.ContentBytes)
		if err != nil {
			log.Error(ctx, "Failed to decode attachment content", zap.Error(err))
			continue
		}

		// S3 Cost Optimization: LLM Load Building and Quote Request pipelines currently only support PDFs for now
		// Store other attachments' metadata for displaying on FE
		var s3URL string
		if strings.Contains(strings.ToLower(attachment.ContentType), "pdf") {
			s3URL, err = s3Uploader.Attachment(ctx, models.Outlook, account, msgID, attachment.Name, data)
			if err != nil {
				log.Error(ctx, "Failed to upload attachment to S3", zap.Error(err))
			} else {
				log.Info(ctx, "Uploaded attachment to S3")
			}
		}

		res = append(res, models.Attachment{
			MessageExternalID:   msgID,
			ExternalID:          attachment.ID,
			MimeType:            attachment.ContentType,
			IsInline:            attachment.IsInline,
			OriginalFileName:    attachment.Name,
			TransformedFileName: s3backup.SanitizeFileName(attachment.Name),
			S3URL:               s3URL,
		})

	}

	return res, hasPDFs, nil
}
