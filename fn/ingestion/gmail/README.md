# Gmail Ingestion Lambda

A lambda function for getting emails via Google Cloud Pub/Sub push notifications and the Gmail API's Go SDK.

# Google Cloud Infrastructure

Any app using Google resources must create a Google Cloud Project and configure the Google APIs and scopes the app needs to access.

1. Follow the instructions [here](https://cloud.google.com/resource-manager/docs/creating-managing-projects?ref_topic=6158848&visit_id=637463496589315265-3245443433&rd=1#creating_a_project) to create a Google Cloud Project.

   a. Our Existing GCP: https://console.cloud.google.com/home/<USER>

2. To enable the Gmail API for the app, follow the instructions under [Activate an API in a standard Cloud project](https://developers.google.com/apps-script/guides/cloud-platform-projects#enabling_an_api_in_a_standard_gcp_project).
3. To enable inbox webhook notifications for the add-on project, follow the instructions [here](https://developers.google.com/gmail/api/guides/push) for creating a Pub/Sub topic.

   a. Existing Topic & Subscriptions: https://console.cloud.google.com/cloudpubsub/topic/list?project=beacon-demo-395521

4. To [publish and verify the app)(https://support.google.com/cloud/answer/10311615#publishing-status&zippy=]), follow the steps in the [Oauth Consent Screen](https://console.cloud.google.com/apis/credentials/consent?project=beacon-397017) of the GCP.

## Common GCP Operations

### 1. Purging Pub/Sub Messages

Google continually retries messages if it receives a non-2xx responses (despite the fact that we set a 15-second maximum backoff ¯\_(ツ)\_/¯). If we no longer need to process these old messages, to go the [Drumkit GCP -> Pub/Sub -> select `GmailInboxPush` -> select the `LambdaSub` subscription -> click "Purge Messages"](https://console.cloud.google.com/cloudpubsub/subscription/detail/LambdaSub?project=beacon-397017).

### 2. Configuring Authorized Domains

We use this to configure authorized customer domains, frontend/backend services, etc. To change these settings, go to the [Drumkit GCP -> Oauth consent screen -> Edit app -> Scroll down to 'Authorized Domains'](https://console.cloud.google.com/apis/credentials/consent/edit?project=beacon-397017)

### 3. Viewing Client Credentials

Our app needs these credentials in order to access Google resources. As such, **admin access to the Google Cloud Project should be limited to authorized engineers as they contain our app's credentials**. [Here](https://console.cloud.google.com/apis/credentials?orgonly=true&project=beacon-397017&supportedpurview=organizationId) is the page for viewing and downloading the Gmail API credentials Drumkit needs for those authorized.

# Development

### Testing Pub/Sub Subscription

Google [Pub/Sub](https://developers.google.com/gmail/api/guides/push) is Google's webhook notification service. We use this service to notify Drumkit of when there have been changes to the user's inbox that Drumkit needs to process.

1. Ensure that the user's inbox you'd like to watch is in the Drumkit RDS (<EMAIL> is already registered).
2. Send a [`/watchInbox`](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion?tab=testing) request at least once every 7 days to subscribe the user's inbox to the Pub/Sub topic (see `ExampleWatchInbox` test event in Lambda console).
3. Observe logs for when Pub/Sub sends push notifications to the Lambda URL every time the user's inbox changes (additions and/or deletions).

### Testing Without Pub/Sub Subscription

Additional fields have been provided in the Lambda request body so that developers can directly invoke the Ingestion Lambda to call the Gmail API without waiting for a Pub/Sub notification.

1. Ensure that the user is in the Drumkit RDS (<EMAIL> is already registered).
2. Invoke ['/inboxWebhook'] endpoint. See `ExampleWebhookNotif` test event in Lambda console for an example. Note how similar to the actual Pub/Sub payload body, the `message.data` field must be a base64 URL-encoded JSON object containing the desired user's `emailAddress`. History ID can be left empty. See additional fields you can provide for direct developer invocations in [main.go](./main.go)

```
{
  "rawPath": "/inboxWebhook",
  "headers": {
    "from": "<EMAIL>"
  },
  "body": "{\"message\":{\"data\":\"****************************************************\",\"messageId\":\"msg-f:1774971923102135034\",\"useMessageHistoryID\":false},\"subscription\":\"projects/beacon-397017/subscriptions/LambdaSub\"}"
}
```

### Backfills

When a new user signs-up, their first email webhook will trigger a 14-day backfill of their emails. If you need to manually trigger a backfill (for example, due to the Great Aljex IP Address Incident of 2024), do the following:

1. (Optional) If you need to trigger re-processing of messages already in the DB (such as, if there was a bug associating loads with emails or generating AI suggestions), set the `SKIP_DUPLICATES=false` in the [Gmail ingestion](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion-outlook?tab=code) AND [Processor](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-processor?tab=code) lambdas (Lambda console -> beacon-ingestion-outlook/beacon-processor -> Configuration tab -> Environment variables).
   - **Context:** As an optimization, for some AI suggestions (like load building) we don't create multiple suggestions for a thread if one already exists as users may continue to use the same thread for all communications related to that shipment. But there are cases where we need to backfill/correct an existing suggestion, hence the `SKIP_DUPLICATES=false` flag.
1. In the DB, identify the target user and record their `gmail_history_id` somewhere.
1. Set their `gmail_history_id` to 0 (use a SQL transaction! Postico (and Tableplus?) automatically performs updates in a transactions). This is the condition Gmail ingestion Lambda checks to know it should backfill.
1. Trigger backfill by either:
   1. Waiting for the Gmail ingestion Lambda to receive a webhook for that user. When complete, the `gmail_history_id` will no longer be 0.
   1. Another option is to manually trigger ingestion by replaying the last webhook associated with that historyID. Search the ingestion logs for that historyID and you'll find the webhook body, then use Lambda console's Test feature or Postman to re-POST that webhook. As the first option, the backfill is complete when the history ID no longer equals 0.
1. If you did step 1, be sure to reset `SKIP_DUPLICATES=true` when the backfill is complete.
