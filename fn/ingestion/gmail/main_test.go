package main

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/gmail/v1"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	mem "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/oauth"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

// The msg data is base64-encoded: `{"emailAddress":"<EMAIL>","historyId":1045507}`
const whTestBody = `{
    "message": {
        "data": "****************************************************************************",
        "messageId": "9431319009453080",
        "publishTime": "2023-10-03T19:34:46.792Z"
    },
	"subscription": "projects/drumkit-397017/subscriptions/LambdaSub"
}`

var (
	defaultGetEmailFunc = func(context.Context, string) (*models.Email, error) {
		return nil, gorm.ErrRecordNotFound
	}
)

type mockSQSClient struct {
	sentMessages []string
}

func (c *mockSQSClient) SendMessage(
	_ context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	c.sentMessages = append(c.sentMessages, aws.ToString(input.MessageBody))
	return &sqs.SendMessageOutput{}, nil
}

func TestHandleInboxWebhookNoMessages(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	memService := &mem.Service{
		Messages: make(map[string]*gmail.Message),
	}

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 123456, ServiceID: 555}, nil
	}

	dbGetServiceFunc = func(context.Context, uint) (models.Service, error) {
		return models.Service{}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.Equal(t, []string{"mem.WatchInbox()", "mem.ListHistory(123456)"}, memService.Calls)
}

func TestHandleInboxWebhookInvalidHistoryID(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {Id: "alpha", Payload: &gmail.MessagePart{}},
			"beta":  {Id: "beta", Payload: &gmail.MessagePart{}},
		},
	}

	getEmailFunc = defaultGetEmailFunc
	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 9999999}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-1*time.Minute), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		"mem.ListHistory(9999999)",
		fmt.Sprintf("mem.ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessages, 2)
	assert.Equal(t, 2, backfillCalls)

}

func TestHandleInboxWebhookNewUser(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {Id: "alpha", Payload: &gmail.MessagePart{}},
			"beta":  {Id: "beta", Payload: &gmail.MessagePart{}},
		},
	}

	getEmailFunc = defaultGetEmailFunc
	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 0}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-1*time.Minute), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		fmt.Sprintf("mem.ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessages, 2)
	assert.Equal(t, 2, backfillCalls)

}

func TestHandleInboxWebhookConcurrentBackfill(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{}

	getEmailFunc = defaultGetEmailFunc
	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{
				EmailAddress:       emailAddr,
				GmailLastHistoryID: 0,
				BackfillStartTime:  models.NullTime{Time: time.Now(), Valid: true}},
			nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	updateBackfillLock = func(_ context.Context, _ *models.User, _ models.NullTime) error {
		return fmt.Errorf("unexpected call to updateBackfillLock")
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusServiceUnavailable, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{"mem.WatchInbox()"}
	assert.Equal(t, expected, memService.Calls)

	// Verify no messages sent to S3/SQS
	assert.Empty(t, mockS3.GmailMessages)
}

func TestHandleInboxWebhookIncompleteBackfill(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {Id: "alpha", Payload: &gmail.MessagePart{}},
			"beta":  {Id: "beta", Payload: &gmail.MessagePart{}},
		},
	}

	getEmailFunc = defaultGetEmailFunc

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{
				EmailAddress:      emailAddr,
				BackfillStartTime: models.NullTime{Time: time.Now().Add(-10 * time.Minute), Valid: true}},
			nil
	}
	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}
	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	var backfillCalls int
	updateBackfillLock = func(_ context.Context, _ *models.User, startTime models.NullTime) error {
		backfillCalls++
		switch backfillCalls {
		case 1:
			assert.True(t, startTime.Valid)
			assert.WithinRange(t, startTime.Time, time.Now().Add(-1*time.Minute), time.Now())

		case 2:
			assert.Empty(t, startTime)
		default:
			return fmt.Errorf("unexpected number of calls: %d", backfillCalls)
		}

		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		fmt.Sprintf("mem.ListMessagesAfterDate(%s)", time.Now().AddDate(0, 0, -14).Format(time.DateOnly)),
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessages, 2)
	assert.Equal(t, 2, backfillCalls)

}

func TestHandleInboxWebhookDuplicateEmails(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24
	env.Vars.SkipDuplicates = true
	const historyID = 123456789

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {Id: "alpha", Payload: &gmail.MessagePart{}},
			"beta":  {Id: "beta", Payload: &gmail.MessagePart{}},
		},
	}

	getEmailFunc = func(_ context.Context, externalID string) (*models.Email, error) {
		switch externalID {
		case "alpha":
			return nil, gorm.ErrRecordNotFound
		case "beta":
			return &models.Email{ExternalID: externalID}, nil
		}

		return nil, fmt.Errorf("unexpected ID: %s", externalID)
	}

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: historyID}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		fmt.Sprintf("mem.ListHistory(%d)", historyID),
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessages, 1)

}

func TestHandleInboxWebhook(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.BackfillHours = 14 * 24

	// Replace RDS functions
	getEmailFunc = defaultGetEmailFunc

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		assert.Equal(t, "<EMAIL>", emailAddr)
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 123456}, nil
	}
	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {Id: "alpha", Payload: &gmail.MessagePart{}},
			"beta":  {Id: "beta", Payload: &gmail.MessagePart{}},
		},
	}

	gmailConstructor = func(
		context.Context,
		string, string,
		models.UserAccessor,
		...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	// invoke the main handler
	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		RawPath: "/inboxWebhook",
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handler(ctx, event)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		"mem.ListHistory(123456)",
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessages, 2)
}

func TestParseWebhook(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"

	whBody, msgData, err := parseWebhook(context.Background(), events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	})
	require.NoError(t, err)

	assert.Equal(t, "9431319009453080", whBody.Message.MessageID)
	assert.Equal(t, "projects/drumkit-397017/subscriptions/LambdaSub", whBody.Subscription)
	assert.Equal(t, "<EMAIL>", msgData.EmailAddress)
	assert.Equal(t, uint64(1045507), msgData.HistoryID)
}
