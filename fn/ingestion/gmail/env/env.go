package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/util/awsutil"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

var Vars envVars

//nolint:lll
type envVars struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv         string `envconfig:"APP_ENV" required:"true"`
	TraceOn        bool   `envconfig:"TRACE_ON"`
	BackfillHours  int    `envconfig:"BACKFILL_HOURS" required:"true"`
	SkipDuplicates bool   `envconfig:"SKIP_DUPLICATES" default:"true"`

	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	// Associated with the Drumkit Google app
	GoogleClientID string `envconfig:"GOOGLE_CLIENT_ID" required:"true"`

	S3BucketName string `envconfig:"S3_BUCKET_NAME" required:"true"`
	SQSQueueURL  string `envconfig:"SQS_QUEUE_URL" required:"true"`
	RedisURL     string `envconfig:"REDIS_URL"`
	// ProdTopicName    = "projects/beacon-397017/topics/GmailInboxPush"
	// StagingTopicName = "projects/beacon-staging-417618/topics/GmailInboxPush-Staging"
	GmailWebhookTopic string `envconfig:"GMAIL_WEBHOOK_TOPIC" default:"projects/beacon-397017/topics/GmailInboxPush"`
	// Incoming gmail hooks are only accepted from this subscription ID
	GmailSubscriptionID string `envconfig:"GMAIL_SUBSCRIPTION_ID" default:"projects/beacon-397017/subscriptions/LambdaSub"`

	// For prod only
	// TODO: rename (secrets are specific to beacon-ingestion, not beacon-api)
	APISecretARN string `envconfig:"API_SECRET_ARN"`

	// In dev, these are set directly.
	// In prod, this is loaded from APISecretARN at startup.
	GoogleClientSecret string `envconfig:"GOOGLE_CLIENT_SECRET"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey             string `json:"AES_KEY"`
	GoogleClientSecret string `json:"GOOGLE_CLIENT_SECRET"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file found")
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.GoogleClientSecret == "" {
			log.Warn(ctx, "missing GOOGLE_CLIENT_SECRET env var")
		}

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.APISecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.GoogleClientSecret == "" {
			return fmt.Errorf("%s is missing some fields", Vars.APISecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.GoogleClientSecret = secret.GoogleClientSecret
	}

	return nil
}
