package main

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"

	"go.uber.org/multierr"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/email"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

type ReplayConfig struct {
	Emails []*EmailMetadata `yaml:"emails"`
}

type EmailMetadata struct {
	S3Key          string    `yaml:"s3_key"`
	ExpectedDBVals *TableRow `yaml:"expected_db_vals"`
}

type TableRow struct {
	// Account and ExternalID can be inferred from the S3 key
	Account    string `yaml:"account"`
	ExternalID string `yaml:"external_id"`

	RFCMessageID       *string   `yaml:"rfc_message_id"`
	Subject            *string   `yaml:"subject"`
	Labels             *string   `yaml:"labels"`
	FreightTrackingID  *string   `yaml:"freight_tracking_id"`
	FreightTrackingIDs *[]string `yaml:"freight_tracking_ids"`
}

type IngestPayload struct {
	EmailAddress string `json:"emailAddress"`
	S3Key        string `json:"s3Key"`
}

// Run this from fn/ingestion/gmail (the parent dir) to read the .env file there
func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	if err := replayFile(ctx, "replay/emails.yml"); err != nil {
		log.Fatal(ctx, "replayFile failed", zap.Error(err))
	}
}

func replayFile(ctx context.Context, path string) error {
	emails, err := loadFile(path)
	if err != nil {
		return err
	}

	if err := openDB(ctx, emails); err != nil {
		return err
	}

	log.Info(ctx, "replaying emails from S3", zap.Int("count", len(emails)))

	var pass int
	for _, meta := range emails {
		if err := ingest(ctx, meta); err != nil {
			log.Error(ctx, "ingestion failed",
				zap.String("s3Key", meta.S3Key), zap.Error(err))
			continue
		}

		if err := verifyProcessedEmail(ctx, meta.ExpectedDBVals); err != nil {
			log.Error(ctx, "FAIL",
				zap.String("s3Key", meta.S3Key), zap.Error(err))
			continue
		}

		log.Info(ctx, "PASS", zap.String("s3Key", meta.S3Key))
		pass++
	}

	log.Info(ctx, "Done", zap.Int("passed", pass), zap.Int("total", len(emails)))
	return nil
}

func loadFile(path string) ([]*EmailMetadata, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var replay ReplayConfig
	if err := yaml.Unmarshal(data, &replay); err != nil {
		return nil, err
	}

	if len(replay.Emails) == 0 {
		return nil, fmt.Errorf("no emails found in config")
	}

	// Add Account and ExternalID fields if not already defined
	for _, meta := range replay.Emails {
		if meta.ExpectedDBVals.Account == "" || meta.ExpectedDBVals.ExternalID == "" {
			// infer from the S3 path (e.g. "gmail/<EMAIL>/18b1b9046aed69fc.json")
			parts := strings.Split(meta.S3Key, "/")
			if len(parts) != 3 {
				return nil, fmt.Errorf("%s invalid format: expected 2 '/' separators", meta.S3Key)
			}

			meta.ExpectedDBVals.Account = parts[1]
			meta.ExpectedDBVals.ExternalID = strings.TrimSuffix(parts[2], ".json")
		}
	}

	return replay.Emails, nil
}

func openDB(ctx context.Context, emails []*EmailMetadata) error {
	if err := env.Load(ctx); err != nil {
		return err
	}

	if err := rds.Open(ctx, env.Vars.EnvConfig); err != nil {
		return err
	}

	if err := rds.AutoMigrate(ctx); err != nil {
		return err
	}

	// Drop these externalIDs from the dev DB so that we can ingest them again (external_id is a unique index)

	externalIDs := make([]string, 0, len(emails))
	for _, meta := range emails {
		externalIDs = append(externalIDs, meta.ExpectedDBVals.ExternalID)
	}

	if err := rds.WithContext(ctx).Exec(`DELETE FROM emails WHERE external_id IN ?`, externalIDs).Error; err != nil {
		return fmt.Errorf("DELETE query failed: %w", err)
	}

	return nil
}

func ingest(ctx context.Context, meta *EmailMetadata) error {
	body, err := json.Marshal(IngestPayload{
		EmailAddress: strings.Split(meta.S3Key, "/")[1], // e.g. "gmail/<EMAIL>/abc123.json"
		S3Key:        meta.S3Key,
	})
	if err != nil {
		return fmt.Errorf("json marshal failed: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx,
		http.MethodPost, "http://localhost:5001/inboxWebhook", bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("failed to build POST request: %w", err)
	}

	req.Header.Add("Content-Type", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return fmt.Errorf("failed to send POST request: %w", err)
	}
	defer resp.Body.Close()

	if code := resp.StatusCode; code >= 400 {
		respBody, _ := io.ReadAll(resp.Body) //nolint:errcheck
		return fmt.Errorf("status %d: %s", code, respBody)
	}

	return nil
}

func verifyProcessedEmail(ctx context.Context, expected *TableRow) error {
	result, err := email.GetEmailByExternalID(ctx, expected.ExternalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("email '%s' not found in DB", expected.ExternalID)
		}
		return err
	}

	err = multierr.Append(err, compareString("account", &expected.Account, result.Account))
	err = multierr.Append(err, compareString("external_id", &expected.ExternalID, result.ExternalID))
	// FIXME refactor script to support multiple models.Email.Loads
	// err = multierr.Append(err, compareString(
	// 	"freight_tracking_id", expected.FreightTrackingID, result.FreightTrackingID))
	err = multierr.Append(err, compareString("labels", expected.Labels, result.Labels))
	err = multierr.Append(err, compareString("rfc_message_id", expected.RFCMessageID, result.RFCMessageID))
	err = multierr.Append(err, compareString("subject", expected.Subject, result.Subject))

	if expected.FreightTrackingIDs != nil {
		var resultFreightTrackingIDs []string
		for _, load := range result.Loads {
			resultFreightTrackingIDs = append(resultFreightTrackingIDs, load.FreightTrackingID)
		}

		if !util.SlicesContainSameElements(resultFreightTrackingIDs, *expected.FreightTrackingIDs) {
			slicesErr := fmt.Errorf(
				"expected %s '%s', found '%s'",
				"freight_tracking_ids",
				*expected.FreightTrackingIDs,
				resultFreightTrackingIDs)
			err = multierr.Append(err, slicesErr)
		}
	}

	return err
}

func compareString(field string, expected *string, result string) error {
	if expected == nil {
		return nil
	}

	if *expected != result {
		return fmt.Errorf("expected %s '%s', found '%s'", field, *expected, result)
	}

	return nil
}
