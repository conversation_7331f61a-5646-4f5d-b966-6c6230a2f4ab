package events

import (
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

type BackfillType string

const (
	BackfillTMSLoads BackfillType = "backfill-tms-loads"
)

// BackfillTMSLoadsEvent represents a request to backfill loads for a specific TMS integration
type BackfillTMSLoadsEvent struct {
	Type          BackfillType    `json:"type"` // Must be "backfill-tms-loads"
	IntegrationID uint            `json:"integration_id"`
	BackfillHours int             `json:"backfill_hours"` // Number of hours to backfill
	StartTime     models.NullTime `json:"start_time"`     // Start time to backfill from
}

func (b BackfillTMSLoadsEvent) Validate() error {
	if b.Type != BackfillTMSLoads {
		return fmt.Errorf("invalid backfill type: %s", b.Type)
	}

	if b.IntegrationID == 0 {
		return fmt.Errorf("integration ID is required")
	}

	if b.BackfillHours <= 0 {
		return fmt.Errorf("backfill hours must be greater than 0")
	}

	return nil
}
