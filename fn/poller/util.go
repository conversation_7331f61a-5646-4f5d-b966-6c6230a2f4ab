package main

import (
	"context"
	"fmt"

	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

var (
	// For unit testing
	dbFuncListFreightTrackingIDs      = loadDB.ListFreightTrackingIDs
	dbFuncListExternalTMSIDs          = loadDB.ListExternalTMSIDs
	dbFuncFindLoadIDsEmptyCarrierCost = loadDB.FindLoadIDsWithEmptyCarrierCost
	dbFuncFindRecentlyUpdatedLoadIDs  = loadDB.FindRecentlyUpdatedLoadIDs
)

type GetSetFunc func(ctx context.Context, integrationID uint) (map[string]struct{}, error)

// GetFreightTrackingIDSet fetches all freight tracking IDs for a given TMS integration ID from the database.
func GetFreightTrackingIDSet(ctx context.Context, integrationID uint) (map[string]struct{}, error) {
	freightTrackingIDs, err := dbFuncListFreightTrackingIDs(ctx, integrationID)
	if err != nil {
		return nil, fmt.Errorf("error getting list of IDs: %w", err)
	}

	set := make(map[string]struct{}, len(freightTrackingIDs))
	for _, freightTrackingID := range freightTrackingIDs {
		set[freightTrackingID] = struct{}{}
	}

	return set, nil
}

func GetExternalTMSIDSet(ctx context.Context, integrationID uint) (map[string]struct{}, error) {
	freightTrackingIDs, err := dbFuncListExternalTMSIDs(ctx, integrationID)
	if err != nil {
		return nil, fmt.Errorf("error getting list of IDs: %w", err)
	}

	set := make(map[string]struct{}, len(freightTrackingIDs))
	for _, freightTrackingID := range freightTrackingIDs {
		set[freightTrackingID] = struct{}{}
	}

	return set, nil
}

// GetLoadsWithEmptyCarrierCost returns a set of freight_tracking_ids from the database for loads
// that don't have ratedata_carrier_cost populated
func GetLoadsWithEmptyCarrierCost(ctx context.Context, tmsID uint) (map[string]struct{}, error) {
	result := make(map[string]struct{})

	loadIDs, err := dbFuncFindLoadIDsEmptyCarrierCost(ctx, tmsID)
	if err != nil {
		return nil, fmt.Errorf("error finding loads with empty carrier cost: %w", err)
	}

	for _, id := range loadIDs {
		if id != "" {
			result[id] = struct{}{}
		}
	}

	return result, nil
}

// GetRecentlyUpdatedLoadIDSet returns a set of freight_tracking_ids for loads
// that were updated within the given timeframe (in minutes)
func GetRecentlyUpdatedLoadIDSet(ctx context.Context, tmsID uint, minutes int) (map[string]struct{}, error) {
	result := make(map[string]struct{})

	loadIDs, err := dbFuncFindRecentlyUpdatedLoadIDs(ctx, tmsID, minutes)
	if err != nil {
		return nil, fmt.Errorf("error finding recently updated loads: %w", err)
	}

	for _, id := range loadIDs {
		if id != "" {
			result[id] = struct{}{}
		}
	}

	return result, nil
}
