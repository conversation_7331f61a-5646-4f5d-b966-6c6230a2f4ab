package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/pricing/globaltranz"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

func pollGlobalTranzLaneHistoryCombinations(
	ctx context.Context,
	globalTranzIntegration models.Integration,
) (err error) {
	ctx, attrs := otel.LogWithIntegrationAttrs(ctx, globalTranzIntegration)
	ctx, metaSpan := otel.StartSpan(ctx, "pollGlobalTranzLaneHistoryCombinations", attrs)

	defer func() {
		if err != nil {
			log.Error(ctx, string(globalTranzIntegration.Name)+" polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	client, _, err := globaltranz.New(ctx, globalTranzIntegration)
	if err != nil {
		return fmt.Errorf("error building TMS client: %w", err)
	}

	startTime := time.Now()
	processedCombinations := 0
	skippedCombinations := 0
	errorCount := 0

	supportedTransportTypes := []models.TransportType{
		models.VanTransportType,
		models.ReeferTransportType,
		models.FlatbedTransportType,
	}

	for _, transportType := range supportedTransportTypes {

		for _, originStateInfo := range util.ListOfUSAStatesWithLargestCities {
			originCity := originStateInfo.LargestCity
			originState := originStateInfo.State

			stateProcessed := 0
			stateSkipped := 0
			stateErrors := 0

			for _, destStateInfo := range util.ListOfUSAStatesWithLargestCities {
				destCity := destStateInfo.LargestCity
				destState := destStateInfo.State

				processedCombinations++

				req := &globaltranz.GetLaneHistoryRequest{
					OriginCity:       originCity,
					OriginState:      originState,
					DestinationCity:  destCity,
					DestinationState: destState,
					TransportType:    string(transportType),
				}

				// Check if data exists in cache first (e.g. redundant same-state combinations)
				cachedData, err := globaltranz.GetRedisLaneHistory(ctx, req)
				if err == nil && cachedData != nil {
					skippedCombinations++
					stateSkipped++

					log.Info(
						ctx,
						"Skipping cached lane history combination",
						zap.String("transport", string(transportType)),
						zap.String("origin", originState),
						zap.String("dest", destState),
					)

					continue
				}

				laneHistory, err := client.GetLaneHistory(ctx, req)
				if err != nil {
					errorCount++
					stateErrors++

					log.Error(
						ctx,
						"Failed to get lane history",
						zap.Error(err),
						zap.String("transport", string(transportType)),
						zap.String("origin", originState),
						zap.String("dest", destState),
					)

					continue
				}

				if err := globaltranz.SetRedisLaneHistory(ctx, req, laneHistory); err != nil {
					errorCount++
					stateErrors++

					log.Error(ctx, "Failed to cache lane history", zap.Error(err))
				} else {
					stateProcessed++
				}

				time.Sleep(100 * time.Millisecond)
			}

			log.Info(ctx, "Completed processing origin state", zap.String("state", originState))
		}

		log.Info(ctx, "Completed processing transport type", zap.String("transport", string(transportType)))
	}

	totalDuration := time.Since(startTime)
	avgSecondsPerCombination := totalDuration.Seconds() / float64(processedCombinations-skippedCombinations)

	log.Info(
		ctx,
		"Completed processing all lane history combinations",
		zap.Int("total_processed", processedCombinations),
		zap.Int("total_skipped", skippedCombinations),
		zap.Int("total_errors", errorCount),
		zap.Duration("total_duration", totalDuration),
		zap.Float64("avg_seconds_per_combination", avgSecondsPerCombination),
	)

	return nil
}
