package main

import (
	"context"
	"encoding/json"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	customEvents "github.com/drumkitai/drumkit/fn/poller/events"
)

const INTERVAL = 10 * time.Minute

// In production, AWS triggers Lambda every 10 minutes
// When running locally, we poll every 10 minutes
func localHandler(ctx context.Context) {
	if testBackfill := os.Getenv("TEST_BACKFILL"); testBackfill == "true" {
		log.Info(ctx, "Running custom backfill test")

		integrationID, backfillHours, startTime, ok := getBackfillParamsFromEnv(ctx)
		if !ok {
			return
		}

		if err := runCustomBackfillTest(ctx, integrationID, backfillHours, startTime); err != nil {
			log.Error(ctx, "Backfill test failed", zap.Error(err))
			return
		}
		return
	}

	// Run scheduled job
	ticker := time.NewTicker(INTERVAL)

	for ; true; <-ticker.C {
		if err := handler(ctx); err != nil {
			log.ErrorNoSentry(ctx, "all polling jobs failed", zap.Error(err))
		}
	}
}

func runCustomBackfillTest(
	ctx context.Context,
	integrationID uint,
	backfillHours int,
	startTime models.NullTime,
) error {

	log.Info(
		ctx,
		"Starting custom backfill test",
		zap.Uint("integrationID", integrationID),
		zap.Int("backfillHours", backfillHours),
		zap.Any("startTime", startTime),
	)

	// Create backfill event
	backfillEvent := customEvents.BackfillTMSLoadsEvent{
		Type:          customEvents.BackfillTMSLoads,
		IntegrationID: integrationID,
		BackfillHours: backfillHours,
		StartTime:     startTime,
	}

	backfillJSON, err := json.Marshal(backfillEvent)
	if err != nil {
		return err
	}

	// Mocked SQS event with custom backfill event
	sqsEvent := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				Body: string(backfillJSON),
			},
		},
	}

	if err := handlerWithLogging(ctx, sqsEvent); err != nil {
		return err
	}

	log.Info(ctx, "Backfill test completed successfully")
	return nil
}

// Helper function to parse and validate backfill params from environment variables
func getBackfillParamsFromEnv(
	ctx context.Context,
) (
	integrationID uint,
	backfillHours int,
	startTime models.NullTime,
	ok bool,
) {

	integrationIDStr := os.Getenv("BACKFILL_INTEGRATION_ID")
	if integrationIDStr == "" {
		log.Warn(ctx, "No BACKFILL_INTEGRATION_ID provided, skipping backfill test")
		return 0, 0, models.NullTime{}, false
	}
	id, err := strconv.ParseUint(integrationIDStr, 10, 32)
	if err != nil {
		log.Warn(ctx, "Invalid BACKFILL_INTEGRATION_ID, skipping backfill test", zap.Error(err))
		return 0, 0, models.NullTime{}, false
	}

	backfillHoursStr := os.Getenv("BACKFILL_HOURS_OVERRIDE")
	if backfillHoursStr == "" {
		log.Warn(ctx, "No BACKFILL_HOURS_OVERRIDE provided, skipping backfill test")
		return 0, 0, models.NullTime{}, false
	}
	hours, err := strconv.Atoi(backfillHoursStr)
	if err != nil {
		log.Warn(ctx, "Invalid BACKFILL_HOURS_OVERRIDE, skipping backfill test", zap.Error(err))
		return 0, 0, models.NullTime{}, false
	}

	startTimeStr := os.Getenv("BACKFILL_START_TIME")
	if startTimeStr != "" {
		log.Info(
			ctx,
			"Parsing BACKFILL_START_TIME",
			zap.String("startTimeStr", startTimeStr),
		)
		parsedTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			log.Warn(
				ctx,
				"Invalid BACKFILL_START_TIME format, must be RFC3339 (e.g. 2025-03-20T00:00:00Z)",
				zap.String("providedValue", startTimeStr),
				zap.Error(err),
			)
			return 0, 0, models.NullTime{}, false
		}
		startTime = models.NullTime{Time: parsedTime, Valid: true}
	}

	return uint(id), hours, startTime, true
}
