package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
)

// Gets the highest external TMS ID in DB (*not* FreightTrackignID) and polls loads from that ID to the current ID.
// Best used for TMSes that increment their IDs and don't provide APIs for listing IDs/querying by time.
// NOTE: ExternalTMSID is sometimes the same as FreightTrackingID (e.g. Aljex) and sometimes different (e.g. Relay),
// so use this poller accordingly
func pollByIncrementingExternalTMSID(ctx context.Context, tmsVar models.Integration) (err error) {
	ctx, attrs := otel.LogWithIntegrationAttrs(ctx, tmsVar)
	ctx, metaSpan := otel.StartSpan(ctx, "pollByIncrementingID", attrs)
	defer func() {
		if err != nil {
			log.Error(ctx, string(tmsVar.Name)+" polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	highestID, err := loadDB.GetHighestExternalTMSID(ctx, tmsVar.ID)
	if err != nil {
		return fmt.Errorf("error fetching highest external TMS id: %w", err)
	}
	if highestID <= 0 {
		log.Info(ctx, "no loads in DB yet, skipping polling to avoid potentially overwhelming backfill")
		return nil
	}
	log.Info(ctx, "fetching load IDs from latest", zap.Int("highestID", highestID), zap.Int("adjustedID", highestID-20))
	highestID -= 20

	loadSet, err := GetExternalTMSIDSet(ctx, tmsVar.ID)
	if err != nil {
		return fmt.Errorf("error getting external TMS ID set: %w", err)
	}

	client, err := tms.New(ctx, tmsVar)
	if err != nil {
		return fmt.Errorf("error building TMS client: %w", err)
	}

	countSuccess := 0
	consecutiveNoLoads := 0
	for {
		highestID++
		if _, ok := loadSet[fmt.Sprint(highestID)]; ok {
			continue
		}

		load, _, err := client.GetLoad(ctx, fmt.Sprint(highestID))
		if err != nil || load.IsEmpty() {
			consecutiveNoLoads++
		} else {
			err = loadDB.UpsertLoad(ctx, &load, &tmsVar)
			if err != nil {
				return fmt.Errorf("error upserting load %d: %w", highestID, err)
			}
			countSuccess++
			consecutiveNoLoads = 0
		}

		if consecutiveNoLoads > 20 {
			break
		}

		// Sleep to account for rate limits. (Turvo = 10 API calls per second)
		time.Sleep(100 * time.Millisecond)

	}

	log.Infof(ctx, "successfully upserted loads to DB",
		zap.Int("countSuccess", countSuccess))
	return nil
}
