package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
)

func pollByList(ctx context.Context, tmsVar models.Integration) (err error) {
	ctx, attrs := otel.LogWithIntegrationAttrs(ctx, tmsVar)
	ctx, metaSpan := otel.StartSpan(ctx, "pollByList", attrs)
	defer func() {
		if err != nil {
			log.Error(ctx, string(tmsVar.Name)+" polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	highestID, err := loadDB.GetHighestFreightTrackingID(ctx, tmsVar.ID)
	if err != nil {
		return fmt.Errorf("error fetching highest freightTrackingID: %w", err)
	}
	if highestID <= 0 {
		log.Info(ctx, "no loads in DB yet, skipping polling to avoid potentially overwhelming backfill")
		return nil
	}

	log.Info(ctx, "fetching load IDs from latest", zap.Int("highestID", highestID), zap.Int("adjustedID", highestID-20))
	// Add buffer in case processor ingested intermediate loads between polling cycles OR TMS skipped sequence somehow
	highestID = -20

	client, err := tms.New(ctx, tmsVar)
	if err != nil {
		return fmt.Errorf("error building TMS client: %w", err)
	}

	loadIDs, err := client.GetLoadIDs(ctx, models.SearchLoadsQuery{FromFreightTrackingID: fmt.Sprint(highestID)})
	if err != nil {
		return fmt.Errorf("error getting load list: %w", err)
	}

	loadSet, err := GetFreightTrackingIDSet(ctx, tmsVar.ID)
	if err != nil {
		return fmt.Errorf("error getting freightTrackingID set: %w", err)
	}

	log.Info(ctx, "fetched load IDs (may include duplicates)", zap.Int("countTotal", len(loadIDs)))

	var countSuccess, countDedupedLoadIDs int
	for _, loadID := range loadIDs {
		// Dedupe load IDs to avoid rate limits from duplicate lookups
		if _, ok := loadSet[loadID]; ok {
			continue
		}
		countDedupedLoadIDs++

		load, _, err := client.GetLoad(ctx, loadID)
		if err != nil {
			log.Warn(ctx, "error getting load from TMS", zap.Error(err), zap.String("loadID", loadID))
			continue
		}

		err = loadDB.UpsertLoad(ctx, &load, &tmsVar)
		if err != nil {
			// Gorm callback will send to Sentry
			log.WarnNoSentry(ctx, "error upserting load in DB", zap.Error(err), zap.String("loadID", loadID))
		} else {
			loadSet[loadID] = struct{}{}
			countSuccess++
		}

		// Sleep to account for rate limits.
		time.Sleep(100 * time.Millisecond)
	}

	if countDedupedLoadIDs > 0 {
		log.Infof(ctx, "successfully upserted loads to DB",
			zap.Int("countSuccess", countSuccess), zap.Int("countDedupedLoadIDs", countDedupedLoadIDs))
	}
	return nil
}
