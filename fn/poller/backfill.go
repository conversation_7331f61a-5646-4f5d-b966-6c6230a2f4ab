package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/poller/env"
	customEvents "github.com/drumkitai/drumkit/fn/poller/events"
)

// handleBackfillEvent is a helper function that handles a custom backfill event triggered by a lambda test event
// it will pull loads from the TMS integration and upsert them into the DB
func handleBackfillEvent(ctx context.Context, event customEvents.BackfillTMSLoadsEvent) error {
	ctx, metaSpan := otel.StartSpan(ctx, "handleBackfillEvent", nil)
	defer metaSpan.End(nil)

	// Get the TMS integration
	tmsIntegration, err := integrationDB.Get(ctx, event.IntegrationID)
	if err != nil {
		return fmt.Errorf("error getting TMS integration: %w", err)
	}

	log.Info(
		ctx,
		"executing backfill for TMS integration",
		zap.String("tms", string(tmsIntegration.Name)),
		zap.Uint("integrationID", event.IntegrationID),
		zap.Int("backfillHours", event.BackfillHours),
		zap.Any("startTime", event.StartTime),
	)

	// Override the backfill hours in env vars temporarily
	originalBackfillHours := env.Vars.BackfillHours
	env.Vars.BackfillHours = event.BackfillHours
	defer func() {
		env.Vars.BackfillHours = originalBackfillHours
	}()

	// Force backfill by TMS type
	switch tmsIntegration.Name {
	case models.McleodEnterprise, models.Turvo:
		return pollByTime(ctx, tmsIntegration, true, event.StartTime)
	case models.Ascend, models.Relay, models.Aljex:
		log.Warn(ctx, "backfill not supported for this TMS type", zap.String("tms", string(tmsIntegration.Name)))
		return nil
	case models.Tai, models.Webhook:
		log.Info(ctx, "no polling needed for this TMS type", zap.String("tms", string(tmsIntegration.Name)))
		return nil
	case models.TruckMate, models.Revenova:
		return fmt.Errorf("polling not implemented for TMS: %s", tmsIntegration.Name)
	default:
		return fmt.Errorf("unsupported TMS: %s", tmsIntegration.Name)
	}
}
