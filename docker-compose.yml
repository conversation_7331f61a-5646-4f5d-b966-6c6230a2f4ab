services:
  api:
    build:
      context: .
      dockerfile: ./fn/api/Dockerfile
    env_file:
      - .env
    ports:
      - 5000:5000
    volumes:
      - api_vol:/usr/src/api
      - ./:/app
    networks:
      - drumkit_net
  processor:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/processor/
    env_file:
      - .env
    ports:
      - 5005:5005
    volumes:
      - processor_vol:/usr/src/processor/
    networks:
      - drumkit_net
  gmail_ingestion:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/ingestion/gmail
    env_file:
      - .env
    ports:
      - 5001:5001
    volumes:
      - gmail_ingestion_vol:/usr/src/ingestion/gmail
    networks:
      - drumkit_net
  outlook_ingestion:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/ingestion/outlook
    env_file:
      - .env
    ports:
      - 5006:5006
    volumes:
      - outlook_ingestion_vol:/usr/src/ingestion/outlook
    networks:
      - drumkit_net
  front_ingestion:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/ingestion/front
    env_file:
      - .env
    ports:
      - 5007:5007
    volumes:
      - front_ingestion_vol:/usr/src/ingestion/front
    networks:
      - drumkit_net
  send_email:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/sendemail/
    env_file:
      - .env
    ports:
      - 5002:5002
    volumes:
      - send_email_vol:/usr/src/sendemail/
    networks:
      - drumkit_net
  watch_inbox:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/watchinbox
    env_file:
      - .env
    ports:
      - 5010:5010
    volumes:
      - watch_inbox_vol:/usr/src/watchinbox/
    networks:
      - drumkit_net
  on_prem:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        project: ./fn/onprem
    env_file:
      - .env
    ports:
      - 5011:5011
    volumes:
      - on_prem_vol:/usr/src/onprem/
    networks:
      - drumkit_net

volumes:
  api_vol:
  processor_vol:
  outlook_ingestion_vol:
  front_ingestion_vol:
  gmail_ingestion_vol:
  send_email_vol:
  ascend_load_getter_vol:
  turvo_load_getter_vol:
  watch_inbox_vol:
  on_prem_vol:

networks:
  drumkit_net:
    driver: bridge
