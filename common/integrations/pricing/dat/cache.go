package dat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

// Redis keys last 30 minutes in order to match the DAT Token duration
const redisKeyTTL = 30 * time.Minute

type SerializableDATClient struct {
	IdentityHost  string
	AnalyticsHost string
	UserEmail     string
	OrgToken      ClientToken
	UserToken     ClientToken
}

func (d *Client) MarshalDATClient() (string, error) {
	sDAT := SerializableDATClient{
		IdentityHost:  d.identityHost,
		AnalyticsHost: d.analyticsHost,
		UserEmail:     d.userEmail,
		OrgToken:      d.orgToken,
		UserToken:     d.userToken,
	}

	bytes, err := json.Marshal(sDAT)
	return string(bytes), err
}

func UnmarshalDATClient(data string) (*Client, error) {
	var sDAT SerializableDATClient
	err := json.Unmarshal([]byte(data), &sDAT)
	if err != nil {
		return nil, err
	}

	datClient := &Client{
		identityHost:  sDAT.IdentityHost,
		analyticsHost: sDAT.AnalyticsHost,
		userEmail:     sDAT.UserEmail,
		orgToken:      sDAT.OrgToken,
		userToken:     sDAT.UserToken,
	}

	return datClient, nil
}

func redisClientKey(datEmailAddress string) string {
	return fmt.Sprintf("dat-email-%s", strings.ToLower(datEmailAddress))
}

func retrieveRedisClient(ctx context.Context, datEmailAddress string) (*Client, error) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return nil, nil
	}

	str, err := redis.RDB.Get(ctx, redisClientKey(datEmailAddress)).Result()
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get DAT client from Redis", zap.Error(err))
	}

	if str != "" {
		client, err := UnmarshalDATClient(str)
		if err != nil {
			log.Warn(ctx, "failed to unmarshal DAT client from Redis", zap.Error(err))
			return nil, err
		}

		if client != nil {
			log.Info(ctx, "re-using existing DAT client")
		}

		return client, nil
	}

	return nil, nil
}

func (d *Client) dumpRedisClient(ctx context.Context) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return
	}

	serializedDATClient, err := d.MarshalDATClient()
	if err != nil {
		log.Warn(ctx, "failed to json marshal DAT client for Redis", zap.Error(err))
	}

	redisKey := redisClientKey(d.userEmail)
	err = redis.RDB.Set(ctx, redisKey, serializedDATClient, redisKeyTTL).Err()
	if err != nil {
		log.Warn(ctx, "failed to set DAT client in Redis", zap.Error(err))
	}
}
