package dat

import (
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	// Auth
	OrgAuthRequestBody struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	UserAuthRequestBody struct {
		Username string `json:"username"`
	}

	TokenResponse struct {
		AccessToken string    `json:"accessToken"`
		ExpiresWhen time.Time `json:"expiresWhen"`
	}

	// Lane Rate
	GetLaneRateResponse struct {
		Transaction   string             `json:"transaction"`
		Created       string             `json:"created"`
		RateResponses []RateResponseItem `json:"rateResponses"`
	}

	RateResponseItem struct {
		Response RateResponse `json:"response"`
		Request  RateRequest  `json:"request"`
	}

	RateResponse struct {
		Rate       Rate       `json:"rate"`
		Escalation Escalation `json:"escalation"`
	}

	RateRequest struct {
		Origin      InputLocation        `json:"origin"`
		Destination InputLocation        `json:"destination"`
		Equipment   models.TransportType `json:"equipment"`
		RateType    RateType             `json:"rateType"`
		// We don't allow customization of these fields yet
		// IncludeMyRate    bool                 `json:"includeMyRate"`
		// TargetEscalation InputEscalation      `json:"targetEscalation"`
		// RateTimePeriod   RateTimePeriod       `json:"rateTimePeriod"`
	}

	InputLocation struct {
		PostalCode      string `json:"postalCode,omitempty"`
		City            string `json:"city,omitempty"`
		StateOrProvince string `json:"stateOrProvince,omitempty"`
	}

	Rate struct {
		Mileage                        float64    `json:"mileage"`
		Reports                        int        `json:"reports"`
		Companies                      int        `json:"companies"`
		StandardDeviation              float64    `json:"standardDeviation"`
		PerMile                        PriceRange `json:"perMile"`
		AverageFuelSurchargePerMileUsd float64    `json:"averageFuelSurchargePerMileUsd"`
		PerTrip                        PriceRange `json:"perTrip"`
		AverageFuelSurchargePerTripUsd float64    `json:"averageFuelSurchargePerTripUsd"`
	}

	Escalation struct {
		Timeframe   RateTimeframe `json:"timeframe"`
		Origin      Location      `json:"origin"`
		Destination Location      `json:"destination"`
	}

	Location struct {
		Name string       `json:"name"`
		Type LocationType `json:"type"`
	}

	PriceRange struct {
		RateUSD float64 `json:"rateUsd"`
		HighUSD float64 `json:"highUsd"`
		LowUSD  float64 `json:"lowUsd"`
	}

	// Lane Rate History
	// LaneRateHistoryReq struct{}
	// LaneRateHistoryPricing struct{}
)
