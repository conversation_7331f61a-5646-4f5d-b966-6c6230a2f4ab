package dat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
)

const (
	stagingIdentityHost  = "identity.api.staging.dat.com"  // Identity host is used for authentication
	stagingAnalyticsHost = "analytics.api.staging.dat.com" // Analytics host is used for fetching pricing data
	prodIdentityHost     = "identity.api.dat.com"
	prodAnalyticsHost    = "analytics.api.dat.com"
)

type (
	API interface {
		GetIntegrationModel() models.Integration
		GetLaneHistory(ctx context.Context, req *[]RateRequest) (*GetLaneRateResponse, error)
	}

	ClientToken struct {
		AccessToken string
		ExpiresWhen models.NullTime
	}

	Client struct {
		identityHost  string
		analyticsHost string
		userEmail     string
		integration   models.Integration
		orgToken      ClientToken
		userToken     ClientToken
	}
)

func New(ctx context.Context, integration models.Integration, userEmail string) (*Client, error) {

	cachedClient, err := retrieveRedisClient(ctx, userEmail)
	if err != nil {
		return nil, err
	}

	if cachedClient != nil {
		cachedClient.integration = integration
		return cachedClient, nil
	}

	username := integration.Username
	password := integration.EncryptedPassword

	if username == "" || password == nil {
		return nil, errors.New("missing DAT username or password")
	}

	var datClient Client
	datClient.userEmail = userEmail
	datClient.integration = integration

	// Workaround for handling integration hosts without having to change the env variables,
	// which allows staging and prod integrations to coexist within the same environment.
	if integration.Tenant == "prod" || integration.Tenant == "production" {
		datClient.identityHost = prodIdentityHost
		datClient.analyticsHost = prodAnalyticsHost
	} else {
		datClient.identityHost = stagingIdentityHost
		datClient.analyticsHost = stagingAnalyticsHost
	}

	_, err = datClient.authenticate(ctx)
	if err != nil {
		return nil, fmt.Errorf("authenticate failed: %w", err)
	}

	datClient.dumpRedisClient(ctx)

	return &datClient, nil
}

func NewOnboard(ctx context.Context, integration models.Integration) (*Client, models.PricingOnBoardResp, error) {
	username := integration.Username
	password := integration.EncryptedPassword

	if username == "" || password == nil {
		return nil, models.PricingOnBoardResp{}, errors.New("missing DAT username or password")
	}

	var datClient Client
	datClient.integration = integration

	if integration.Tenant == "prod" || integration.Tenant == "production" {
		datClient.identityHost = prodIdentityHost
	} else {
		datClient.identityHost = stagingIdentityHost
	}

	resp, err := datClient.authenticateOnboard(ctx)
	if err != nil {
		return nil, models.PricingOnBoardResp{}, fmt.Errorf("authenticate failed: %w", err)
	}

	return &datClient, resp, nil
}

func (d *Client) post(
	ctx context.Context,
	path string,
	body io.Reader,
	out any,
	auth *string,
) (util.APIResponse, error) {
	return d.do(ctx, http.MethodPost, path, nil, body, out, auth)
}

func (d *Client) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	reqBody io.Reader,
	out any,
	auth *string,
) (util.APIResponse, error) {

	host := d.analyticsHost
	if strings.Contains(path, "token") {
		host = d.identityHost
	}

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     host,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, reqBody)
	if err != nil {
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}

	if auth != nil {
		req.Header.Add("Authorization", *auth)
	}

	if method == http.MethodPost {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, d.integration, err)
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", method, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, d.integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(d.integration, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for DAT response body",
				zap.ByteString("body", body))

			return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", method, reqURL, err)
		}

		log.Debug(ctx, "received DAT response",
			zap.String("method", method), zap.String("url", reqURL), zap.Any("body", out))
	}

	return util.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}
