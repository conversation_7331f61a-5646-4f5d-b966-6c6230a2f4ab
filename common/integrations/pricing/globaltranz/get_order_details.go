package globaltranz

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	OrderDetails struct {
		OrderBK         int            `json:"orderBK"`
		ServiceTypeID   int            `json:"serviceTypeId"`
		ServiceTypeName string         `json:"serviceTypeName"`
		CarrierName     string         `json:"carrierName"`
		Miles           float64        `json:"miles"`
		Cost            CostDetails    `json:"cost"`
		Revenue         RevenueDetails `json:"revenue"`
		Weight          float64        `json:"weight"`
		ShipmentDate    string         `json:"shipmentDate"`
	}

	CityStateLocation struct {
		City  string `json:"city"`
		State string `json:"state"`
	}
	CostDetails struct {
		Total float64 `json:"total"`
	}

	RevenueDetails struct {
		Total float64 `json:"total"`
	}

	OrderDetailsResponse struct {
		DidError         bool         `json:"didError"`
		ErrorMessages    any          `json:"errorMessages"`
		Message          any          `json:"message"`
		Model            OrderDetails `json:"model"`
		ObjectWasCreated bool         `json:"objectWasCreated"`
	}
)

func (c Client) GetOrderDetails(ctx context.Context, orderBK string) (orderDetails *OrderDetails, err error) {

	// Check if data exists in cache first
	cachedOrderDetails, err := getRedisOrderDetails(ctx, orderBK)
	if err == nil {
		log.Debug(
			ctx,
			"found cached GlobalTranz order details in Redis",
			zap.String("loadID", orderBK),
			zap.Any("orderDetails", cachedOrderDetails),
		)

		return &cachedOrderDetails, nil
	}

	orderBKInt, err := strconv.Atoi(orderBK)
	if err != nil {
		return nil, fmt.Errorf("invalid order number: %w", err)
	}

	query := make(url.Values)
	query.Add("orderBK", fmt.Sprintf("%d", orderBKInt))
	query.Add("modeType", "3")

	var result OrderDetailsResponse
	_, err = c.doWithRetry(ctx, http.MethodGet, c.tmsHost, GetOrderDetailsPath, query, nil, &result)

	if err != nil {
		return nil, err
	}

	log.Info(ctx, "found order details", zap.Any("result", result))

	if err := setRedisOrderDetails(ctx, orderBK, result.Model); err != nil {
		log.Error(ctx, "Failed to cache order details", zap.Error(err))
	} else {
		log.Debug(ctx, "cached GlobalTranz order details in Redis", zap.String("loadID", orderBK))
	}

	return &result.Model, nil
}
