package globaltranz

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	GetLaneHistoryRequest struct {
		DateFrom         string `json:"dateFrom"`
		DateTo           string `json:"dateTo"`
		DestinationCity  string `json:"destinationCity"`
		DestinationState string `json:"destinationState"`
		DestinationZip   string `json:"destinationZip"`
		OriginCity       string `json:"originCity"`
		OriginState      string `json:"originState"`
		OriginZip        string `json:"originZip"`
		TransportType    string `json:"transportType"`
	}

	Item struct {
		TotalCarrierCost   float64 `json:"totalCarrierCost"`
		Revenue            float64 `json:"revenue"`
		PickupDateTime     string  `json:"pickupDateTime"`
		DeliveredDateTime  string  `json:"deliveredDateTime"`
		TransportType      string  `json:"transportType"`
		OriginCountry      string  `json:"originCountry"`
		OriginState        string  `json:"originState"`
		OriginCity         string  `json:"originCity"`
		OriginZip          string  `json:"originZip"`
		DestinationCountry string  `json:"destinationCountry"`
		DestinationState   string  `json:"destinationState"`
		DestinationCity    string  `json:"destinationCity"`
		DestinationZip     string  `json:"destinationZip"`
		CarrierName        string  `json:"carrierName"`
		CarrierPhoneNumber string  `json:"carrierPhoneNumber"`
		CarrierEmail       string  `json:"carrierEmail"`
		Distance           float64 `json:"distance"`
		RatePerMile        float64 `json:"ratePerMile"`
	}

	PlaceCoords struct {
		Lat  float32
		Long float32
	}

	GetLaneHistoryResponse struct {
		OriginCoords      PlaceCoords
		DestinationCoords PlaceCoords
		Items             []Item `json:"items"`
	}
)

const (
	FTLOrderServiceTypeID      = 1
	LowCostAndRevenueThreshold = 100
	NumHistoryRecordsByCarrier = 100
)

var thirtyDaysAgo = time.Now().Add(-30 * 24 * time.Hour)

// GetLaneHistory retrieves history data for all carriers on a given lane.
func (c Client) GetLaneHistory(
	ctx context.Context,
	req *GetLaneHistoryRequest,
) (*GetLaneHistoryResponse, error) {
	if req.OriginZip == "" && (req.OriginCity == "" || req.OriginState == "") {
		return nil, fmt.Errorf("origin location is incomplete")
	}

	if req.DestinationZip == "" && (req.DestinationCity == "" || req.DestinationState == "") {
		return nil, fmt.Errorf("destination location is incomplete")
	}

	originLocation, err := c.GetLocationSearch(ctx, req.OriginCity, req.OriginState, req.OriginZip)
	if err != nil {
		return nil, err
	}

	if originLocation == nil || len(originLocation.Locations) == 0 {
		return nil, fmt.Errorf(
			"no locations found for origin %s %s %s",
			req.OriginCity,
			req.OriginState,
			req.OriginZip,
		)
	}

	destinationLocation, err := c.GetLocationSearch(
		ctx,
		req.DestinationCity,
		req.DestinationState,
		req.DestinationZip,
	)
	if err != nil {
		return nil, err
	}

	if destinationLocation == nil || len(destinationLocation.Locations) == 0 {
		return nil, fmt.Errorf(
			"no locations found for destination %s %s %s",
			req.DestinationCity,
			req.DestinationState,
			req.DestinationZip,
		)
	}

	equipmentTypeID := getEquipmentType(req.TransportType)
	if equipmentTypeID == -1 {
		return nil, fmt.Errorf("unsupported transport type: %s", req.TransportType)
	}

	laneCarrierListReq := GetLaneCarrierListRequest{
		Origin:      getLocationForCarrierLookup(originLocation.Locations[0]),
		Destination: getLocationForCarrierLookup(destinationLocation.Locations[0]),
		Services: Services{
			ServiceType:        1,
			AdditionalServices: []string{},
		},
		ServiceType: ServiceType{
			ID:          1,
			DisplayText: "Full",
		},
		EquipmentType:          equipmentTypeID,
		AdditionalFleetOptions: []string{},
		ServiceOptions:         []string{},
		InsuranceCost:          []string{},
		Compliance:             []string{},
		Accessorials:           []string{},
		SafetyRatings:          []string{},
		BookableCarriersOnly:   true,
		IncludeLoads:           true,
		IncludeQuotes:          true,
	}

	carrierList, err := c.GetLaneCarrierList(ctx, &laneCarrierListReq)
	if err != nil {
		return nil, err
	}

	carrierListSubgroups := splitCarrierListIntoSubgroups(carrierList.Carriers, 10)

	var laneHistoryResp GetLaneHistoryResponse
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, carrierSubgroup := range carrierListSubgroups {
		wg.Add(1)

		go func(carrierSubgroup []CarrierItem) {
			defer wg.Done()

			for _, carrier := range carrierSubgroup {
				c.fetchAndParseCarrierHistory(
					ctx,
					carrier,
					&laneHistoryResp,
					req,
					originLocation,
					destinationLocation,
					&mu,
				)
			}
		}(carrierSubgroup)
	}

	wg.Wait()

	if len(originLocation.Locations) > 0 {
		laneHistoryResp.OriginCoords = PlaceCoords{
			Lat:  originLocation.Locations[0].Latitude,
			Long: originLocation.Locations[0].Longitude,
		}
	}

	if len(destinationLocation.Locations) > 0 {
		laneHistoryResp.DestinationCoords = PlaceCoords{
			Lat:  destinationLocation.Locations[0].Latitude,
			Long: destinationLocation.Locations[0].Longitude,
		}
	}

	return &laneHistoryResp, nil
}

func (c *Client) fetchAndParseCarrierHistory(
	ctx context.Context,
	carrier CarrierItem,
	laneHistoryResp *GetLaneHistoryResponse,
	req *GetLaneHistoryRequest,
	originLocation *GetLocationSearchResponse,
	destinationLocation *GetLocationSearchResponse,
	mu *sync.Mutex,
) {
	if carrier.CarrierBk == 0 {
		return
	}

	carrierHistory, err := getRedisCarrierHistory(ctx, carrier.CarrierName)

	if err != nil {
		carrierHistoryResp, err := c.GetCarrierHistory(ctx, &GetCarrierHistoryRequest{
			VendorBk:    carrier.CarrierBk,
			RecordCount: NumHistoryRecordsByCarrier,
		})
		if err != nil {
			log.Warn(ctx, "failed to get carrier history", zap.Error(err))
			return
		}

		if err := setRedisCarrierHistory(ctx, carrier.CarrierName, carrierHistoryResp.HistoryItems); err != nil {
			log.Warn(ctx, "failed to set cached carrier history", zap.Error(err))
		}

		carrierHistory = carrierHistoryResp.HistoryItems
	}

	mu.Lock()
	defer mu.Unlock()

	for _, historyItem := range carrierHistory {
		pickupDate, err := time.Parse(time.RFC3339, historyItem.PickupDate)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to parse pickup date for carrier history item", zap.Error(err))
			continue
		}

		if pickupDate.Before(thirtyDaysAgo) {
			log.Debug(ctx, "skipping carrier history item past date threshold")
			continue
		}

		deliveryDate, err := time.Parse(time.RFC3339, historyItem.DeliveryDate)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to parse delivery date for carrier history item", zap.Error(err))
			continue
		}

		if deliveryDate.Before(thirtyDaysAgo) {
			log.Debug(ctx, "skipping carrier history item past date threshold")
			continue
		}

		if !strings.EqualFold(historyItem.EquipmentType, req.TransportType) {
			log.Debug(ctx, "skipping carrier history item with wrong equipment type")
			continue
		}

		if !strings.EqualFold(
			util.GetStateFullName(ctx, historyItem.Origin.State),
			originLocation.Locations[0].State,
		) {
			log.Debug(ctx, "skipping carrier history item with wrong origin state")
			continue
		}

		if !strings.EqualFold(
			util.GetStateFullName(ctx, historyItem.Destination.State),
			destinationLocation.Locations[0].State,
		) {
			log.Debug(ctx, "skipping carrier history item with wrong destination state")
			continue
		}

		if historyItem.TotalCost < LowCostAndRevenueThreshold ||
			historyItem.Revenue < LowCostAndRevenueThreshold {
			log.Info(ctx, "skipping carrier history item with low cost or revenue")
			continue
		}

		if historyItem.BolNumber == "" {
			log.Warn(ctx, "BolNumber is empty, skipping order details retrieval")
			continue
		}

		// GlobalTranz only supports full truckload (FTL) and less than truckload (LTL)
		// We only want to include FTL in our history and GlobalTranz LTL loads are ServiceID 0
		orderDetails, err := c.GetOrderDetails(ctx, historyItem.BolNumber)
		if err != nil {
			log.Error(ctx, "error getting order details", zap.Any("error", err))
			continue
		}

		if orderDetails.ServiceTypeID != FTLOrderServiceTypeID {
			log.WarnNoSentry(ctx, "skipping GlobalTranz history item with non-FTL load")
			continue
		}

		if orderDetails.Miles == 0 {
			log.WarnNoSentry(
				ctx,
				fmt.Sprintf("zero-mile distance found for globaltranz load %s, using fallback", historyItem.BolNumber),
				zap.Any("historyItem", historyItem),
			)

			// Fallback to redis cache or GS Staging if GlobalTranz load mileage is zero
			mileDistance, err := quote.GetRedisMileDistanceBetweenLocationsWithFallback(
				ctx,
				historyItem.Origin.City,
				historyItem.Origin.State,
				historyItem.Destination.City,
				historyItem.Destination.State,
			)
			if err != nil {
				log.Error(ctx, "error getting mile distance from redis or Greenscreens fallback", zap.Error(err))
				continue
			}

			if mileDistance == 0 {
				log.WarnNoSentry(
					ctx,
					"load distance is zero after fallback, skipping",
					zap.Any("historyItem", historyItem),
				)
				continue
			}

			orderDetails.Miles = mileDistance
		}

		laneHistoryResp.Items = append(laneHistoryResp.Items, Item{
			TotalCarrierCost:   historyItem.TotalCost,
			Revenue:            historyItem.Revenue,
			PickupDateTime:     pickupDate.Format("2006-01-02"),
			DeliveredDateTime:  deliveryDate.Format("2006-01-02"),
			TransportType:      req.TransportType,
			OriginState:        req.OriginState,
			OriginCity:         req.OriginCity,
			OriginZip:          req.OriginZip,
			DestinationState:   req.DestinationState,
			DestinationCity:    req.DestinationCity,
			DestinationZip:     req.DestinationZip,
			CarrierName:        carrier.CarrierName,
			CarrierPhoneNumber: carrier.PhoneNumber,
			Distance:           orderDetails.Miles,
			RatePerMile:        historyItem.TotalCost / orderDetails.Miles,
		})
	}
}

// Split carrier list into subgroups of approximately equal size
func splitCarrierListIntoSubgroups(carrierList []CarrierItem, numSubgroups int) [][]CarrierItem {
	var subgroups [][]CarrierItem
	groupSize := len(carrierList) / numSubgroups

	for i := 0; i < numSubgroups; i++ {
		start := i * groupSize
		end := (i + 1) * groupSize
		if i == numSubgroups-1 {
			// Add any remaining items to the last group
			end = len(carrierList)
		}
		subgroups = append(subgroups, carrierList[start:end])
	}
	return subgroups
}

/**
 * getEquipmentType returns the corresponding equipment ID for a given transport type
 * based on GlobalTranz's internal mapping.
 */
func getEquipmentType(transportType string) int {
	switch transportType {
	case string(models.VanTransportType):
		return 0
	case string(models.ReeferTransportType):
		return 26 // Maps to "Reefer Intermodal"
	case string(models.FlatbedTransportType):
		return 1
	case string(models.HotShotTransportType):
		return 21
	default:
		return -1
	}
}

func getLocationForCarrierLookup(location LocationItem) Location {
	return Location{
		Latitude:     location.Latitude,
		Longitude:    location.Longitude,
		RadiusMiles:  100,
		IncludeState: true,
		ExcludeState: false,
		City:         location.City,
		StateCode:    location.StateCode,
		ZipCode:      location.Zip,
		CountryID:    location.CountryCode,
	}
}

// TODO: Incorporate more data other than quote rate
func CreateQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	quoteRequestID uint,
	transportType models.TransportType,
	quoteRate quote.RateData,
	stops []models.Stop,
) *models.QuickQuote {
	return quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		0,
		"",
		quoteRequestID,
		stops,
		transportType,
		time.Time{},
		time.Time{},
		quoteRate,
		0,
		0,
		0,
		models.GlobalTranzSource,
		models.GlobalTranzHistoryType,
		nil,
	)
}
