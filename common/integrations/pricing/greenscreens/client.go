package greenscreens

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	API interface {
		GetIntegrationModel() models.Integration
		GetQuote(ctx context.Context, email, laneRatePredictionID string, req *GetQuoteRequest) (*QuoteDetail, error)
		GetLaneHistory(ctx context.Context, req *GetLaneHistoryRequest) (*GetLaneHistoryResponse, error)
		//nolint:lll
		GetLaneRatePrediction(ctx context.Context, serviceID uint, userEmail string, req *GetRatePredictionRequest) (*RatePredictionDetail, error)
		//nolint:lll
		GetNetworkLaneRatePrediction(ctx context.Context, serviceID uint, userEmail string, req *GetRatePredictionRequest) (*RatePredictionDetail, error)
	}

	AccessTokenIntegrationData struct {
		AccessToken               string
		AccessTokenExpirationDate models.NullTime
	}

	Client struct {
		accessToken string
		host        string
		integration models.Integration
	}

	authenticateResponse struct {
		AccessToken      string `json:"access_token"`
		ExpiresIn        int    `json:"expires_in"`
		RefreshExpiresIn int    `json:"refresh_expires_in"`
		TokenType        string `json:"token_type"`
		NotBeforePolicy  int    `json:"not_before_policy"`
		Scope            string `json:"scope"`
	}
)

const (
	BaseProdURL = "api.greenscreens.ai"
	BaseTestURL = "testapi.greenscreens.ai"
)

func New(ctx context.Context, greenscreensIntegration models.Integration) (API, models.PricingOnBoardResp, error) {
	username := greenscreensIntegration.Username
	password := greenscreensIntegration.EncryptedPassword

	if username == "" || password == nil {
		return nil, models.PricingOnBoardResp{}, errors.New("missing Greenscreens username or password")
	}

	var client Client
	client.integration = greenscreensIntegration
	client.host = BaseProdURL

	// Controlling Greenscreens environment through tenant field rather than lambda env variable allows
	// us to use Greenscreens staging in Drumkit prod.
	if greenscreensIntegration.Tenant == "staging" {
		client.host = "testapi.greenscreens.ai"
	}

	tokenData, err := client.authenticateAndReturnClaims(
		ctx,
		username,
		password,
	)
	if err != nil {
		log.Error(ctx, "Could not authenticate Greenscreens client", zap.Error(err))
		return nil, models.PricingOnBoardResp{}, fmt.Errorf("error authenticating greenscreens for new client: %w", err)
	}

	client.accessToken = tokenData.AccessToken

	onBoardResp := models.PricingOnBoardResp{
		Username:                  username,
		AccessToken:               tokenData.AccessToken,
		AccessTokenExpirationDate: tokenData.AccessTokenExpirationDate,
		EncryptedPassword:         password,
	}

	log.Info(ctx, "successfully created Greenscreens client", zap.String("username", username))
	return client, onBoardResp, nil
}

var cachedClient API
var cachedClientMutex sync.Mutex

func CachedClient(ctx context.Context, greenscreensIntegration models.Integration) (API, error) {
	cachedClientMutex.Lock()
	defer cachedClientMutex.Unlock()

	var err error

	if cachedClient != nil &&
		cachedClient.GetIntegrationModel().Username == greenscreensIntegration.Username &&
		cachedClient.GetIntegrationModel().AccessToken != "" &&
		!cachedClient.GetIntegrationModel().NeedsRefresh() {
		log.Debug(ctx, "re-using existing Greenscreens client")
		return cachedClient, nil
	}

	cachedClient, tokenData, err := New(ctx, greenscreensIntegration)
	if err != nil {
		log.ErrorNoSentry(ctx, "failed to create greenscreens client", zap.Any("integration", greenscreensIntegration))
		return nil, fmt.Errorf("getting greenscreens client failed: %w", err)
	}

	greenscreensIntegration.AccessToken = tokenData.AccessToken
	greenscreensIntegration.AccessTokenExpirationDate = tokenData.AccessTokenExpirationDate

	if errUpdate := integrationDB.Update(ctx, &greenscreensIntegration); errUpdate != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to update greenscreens token on integrations table",
			zap.Any("integration", greenscreensIntegration),
		)
		return nil, fmt.Errorf("integrations table update failed: %w", errUpdate)
	}

	return cachedClient, err
}

func (c Client) GetIntegrationModel() models.Integration {
	return c.integration
}

func (c Client) authenticateAndReturnClaims(
	ctx context.Context,
	username string,
	encryptedPassword []byte,
) (*AccessTokenIntegrationData, error) {
	query := make(url.Values)
	query.Add("grant_type", "client_credentials")
	query.Add("client_id", username)

	password, err := crypto.DecryptAESGCM(ctx, string(encryptedPassword), nil)
	if err != nil {
		return nil, err
	}

	query.Add("client_secret", password)

	var auth authenticateResponse
	if _, err := c.postform(ctx, "auth/token", query, &auth); err != nil {
		return nil, err
	}

	greenscreensClaims := jwt.GreenscreensTokenClaims{}
	err = jwt.ParseGreenscreens(auth.AccessToken, &greenscreensClaims)
	if err != nil {
		return nil, fmt.Errorf("parsing greenscreens jwt claims failed: %w", err)
	}

	return &AccessTokenIntegrationData{
		AccessToken: auth.AccessToken,
		AccessTokenExpirationDate: models.NullTime{
			Time:  greenscreensClaims.ExpiresAt.Time,
			Valid: true,
		},
	}, nil
}

func (c Client) postform(ctx context.Context, path string, form url.Values, out any) (util.APIResponse, error) {
	body := strings.NewReader(form.Encode())
	return c.do(ctx, http.MethodPost, path, nil, body, out)
}

func (c Client) post(
	ctx context.Context,
	path string,
	body io.Reader,
	out any,
) (util.APIResponse, error) {

	return c.do(ctx, http.MethodPost, path, nil, body, out)
}

func (c Client) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	reqBody io.Reader,
	out any,
) (util.APIResponse, error) {

	apiVersion := "v1/"
	if path == "prediction/rates" || path == "prediction/network-rates" {
		apiVersion = "v3/"
	}

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     c.host,
		Path:     apiVersion + path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, reqBody)
	if err != nil {
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}

	if path == "auth/token" {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}

	if path != "auth/token" && (method == http.MethodPost || method == http.MethodPut) {
		req.Header.Set("Content-Type", "application/json")
	}

	// Bearer token required for all endpoints except POST /auth/token (which creates the token)
	if path != "auth/token" {
		req.Header.Add("Authorization", "Bearer "+c.accessToken)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, c.integration, err)
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", method, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, c.integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(c.integration, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for Greenscreens response body",
				zap.ByteString("body", body))

			return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", method, reqURL, err)
		}

		if path != "auth/token" {
			log.Debug(ctx, "received Greenscreens response",
				zap.String("method", method), zap.String("url", reqURL), zap.Any("body", out))
		}
	}

	return util.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}
