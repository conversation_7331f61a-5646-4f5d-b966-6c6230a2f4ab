package greenscreens

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

type (
	RatePredictionStop struct {
		Order   int    `json:"order"`
		Country string `json:"country"` // 2-letter country code in ISO 3166-1 alpha-2
		State   string `json:"state" `  // 2-letter state code
		City    string `json:"city" `
		Zip     string `json:"zip" validate:"required_without_all=City State"` // 5-digit zip code
	}

	GetRatePredictionRequest struct {
		PickupDateTime time.Time            `json:"pickupDateTime"`
		TransportType  TransportType        `json:"transportType"`
		Stops          []RatePredictionStop `json:"stops"`
		Commodity      string               `json:"commodity"`
		Tag            string               `json:"tag"`
		Currency       Currency             `json:"currency"`
	}

	RatePredictionDetail struct {
		ID              string
		TargetBuyRate   float64
		LowBuyRate      float64
		HighBuyRate     float64
		StartBuyRate    float64
		FuelRate        float64
		Distance        float64
		ConfidenceLevel int
		AcceptanceRates []float64
		Tag             string
		Currency        Currency
	}
)

// GetLaneRatePrediction retrieves a lane rate prediction.
// https://connect.greenscreens.ai/#tag/Prediction/operation/predictionRates
func (c Client) GetLaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper, not service
	req *GetRatePredictionRequest,
) (*RatePredictionDetail, error) {

	var result RatePredictionDetail
	var apiResp util.APIResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if apiResp, err = c.post(ctx, "prediction/rates", body, &result); err != nil {
		return nil, err
	}

	if aws.S3Uploader != nil && userEmail != "" {
		if _, err = aws.S3Uploader.LaneRatePrediction(ctx, serviceID, userEmail, apiResp); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Error(ctx, "s3 archive lane rate prediction failed", zap.Error(err))
		}
	}

	return &result, nil
}
