package truckstop

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

func (c Client) GetBookedHistory(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *BookedRateTrendline,
) (*BookedHistoryResp, error) {
	var result BookedHistoryResp
	var apiResp util.APIResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if apiResp, err = c.Post(ctx, "/modeledrate/v3/booked/history/threeyear", nil, body, &result); err != nil {
		return nil, err
	}

	if aws.S3Uploader != nil {
		if _, err = aws.S3Uploader.BookedHistory(ctx, serviceID, userEmail, apiResp); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Error(ctx, "s3 archive booked history failed", zap.Error(err))
		}
	}

	return &result, nil
}
