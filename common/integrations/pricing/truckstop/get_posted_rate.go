package truckstop

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

func (c Client) GetPostedRate(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *PostedRateTrendline,
) (PostedRateResp, error) {
	var result PostedRateResp
	var apiResp util.APIResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return PostedRateResp{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if apiResp, err = c.Post(ctx, "/modeledrate/v3/posted/rateestimate", nil, body, &result); err != nil {
		return PostedRateResp{}, err
	}

	if aws.S3Uploader != nil {
		if _, err = aws.S3Uploader.PostedRate(ctx, serviceID, userEmail, apiResp); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Error(ctx, "s3 archive posted rate failed", zap.Error(err))
		}
	}

	return result, nil
}
