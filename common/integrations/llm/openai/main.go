package openai

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	openaiSDK "github.com/openai/openai-go"
	openaiOption "github.com/openai/openai-go/option"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type Service interface {
	GetResponse(
		ctx context.Context,
		email models.Email,
		braintrustProjectDetails braintrustsdk.ProjectDetails,
		options ...ResponseOptions,
	) (GetResponseOutput, error)
	GetEmbedding(ctx context.Context, input string) ([]float64, error)
}

type service struct {
	client openaiSDK.Client
}

// NewService creates a new, singleton OpenAI service.
// It will load the OpenAI API key from the environment variables and create a new OpenAI client.
// If the OpenAI API key is not set, it will return an error.
func NewService(ctx context.Context) (Service, error) {

	clientOnce.Do(func() {
		// Load the environment variables
		if err := env.Load(ctx); err != nil {
			clientErr = fmt.Errorf("failed to load OpenAI environment variables: %w", err)
			return
		}

		apiKey := env.Secrets.OpenAIAPIKey
		if apiKey == "" {
			clientErr = errors.New("OpenAI API key is not set")
			return
		}

		openaiClient = openaiSDK.NewClient(
			openaiOption.WithAPIKey(apiKey),
			openaiOption.WithHTTPClient(&http.Client{
				Transport: otelhttp.NewTransport(http.DefaultTransport,
					otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter)),
			}),
		)
	})

	return &service{client: openaiClient}, clientErr
}
