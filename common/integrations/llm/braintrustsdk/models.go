package braintrustsdk

import "github.com/drumkitai/drumkit/common/models"

type (
	BraintrustLogInput struct {
		ID             string
		ProjectDetails ProjectDetails

		// Prompt fields
		UserPrompt           string
		DeveloperPrompt      string
		OpenAIConversationID string

		// Email fields
		Email      models.Email
		Attachment models.Attachment

		// Portal fields
		PortalCategory   models.SourceCategory
		PortalName       models.IntegrationName
		PortalExternalID string // Required
		// Optional; URL of the portal page that contains the quote request IFF the portal
		// supports direct links to the quote request page.
		PortalURL string
	}

	ProjectStepName string
	ProjectDetails  struct {
		ID             string
		StepName       ProjectStepName
		HasAttachments bool
	}
)
