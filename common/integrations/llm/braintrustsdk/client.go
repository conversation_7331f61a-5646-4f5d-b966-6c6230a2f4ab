package braintrustsdk

import (
	"net/http"
	"os"
	"sync"

	"github.com/braintrustdata/braintrust-go"
	"github.com/braintrustdata/braintrust-go/option"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"

	"github.com/drumkitai/drumkit/common/util/otel"
)

type Client struct {
	client *braintrust.Client
}

var (
	singletonClient *Client
	once            sync.Once
)

func New() *Client {
	return &Client{
		client: braintrust.NewClient(
			option.WithBaseURL(os.Getenv("BRAINTRUST_BASE_URL")),
			option.WithAPIKey(os.Getenv("BRAINTRUST_API_KEY")),
			option.WithHTTPClient(&http.Client{
				Transport: otelhttp.NewTransport(http.DefaultTransport,
					otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter))}),
		),
	}
}

// GetClient returns a singleton instance of the Braintrust client
func GetClient() *Client {
	once.Do(func() {
		singletonClient = New()
	})
	return singletonClient
}
