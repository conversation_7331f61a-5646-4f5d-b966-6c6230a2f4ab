package braintrustsdk

import (
	"encoding/json"
	"fmt"
	"math"

	"github.com/xrash/smetrics"
)

// Levenshtein scores based on the Levenshtein distance, e.g. how many transformations
// are required to transform one string into the other.
// Output is in the 0-1 range, higher scores indicate higher similarity.
func LevenshteinScorer(expected, actual any) (float64, error) {
	// Convert the expected and actual values to strings
	expectedStr, okExpected := expected.(string)
	actualStr, okActual := actual.(string)

	// If either value is not a string, try to convert from JSON
	if !okExpected {
		if jsonBytes, err := json.Marshal(expected); err == nil {
			expectedStr = string(jsonBytes)
			okExpected = true
		}
	}

	if !okActual {
		if jsonBytes, err := json.Marshal(actual); err == nil {
			actualStr = string(jsonBytes)
			okActual = true
		}
	}

	// Return an error if either value cannot be treated as a string
	if !okExpected || !okActual {
		return 0, fmt.Errorf("LevenshteinScorer: expected and actual values must be strings or JSON-serializable")
	}

	// Handle empty strings case
	if expectedStr == "" && actualStr == "" {
		return 1.0, nil // Perfect match
	}
	if expectedStr == "" || actualStr == "" {
		return 0.0, nil // Complete mismatch
	}

	// Calculate Levenshtein distance using smetrics
	distance := smetrics.Ukkonen(expectedStr, actualStr, 1, 1, 1)

	// Normalize the distance to a score between 0 and 1
	// We use the max length as denominator and subtract from 1 to get similarity
	maxLength := math.Max(float64(len(expectedStr)), float64(len(actualStr)))
	score := 1.0 - (float64(distance) / maxLength)

	return score, nil
}
