package llm

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

// promptLLMForCustomer prompts the LLM to extract a customer's name and email address from the email body.
// Used by ExtractQuoteRequestSuggestions and ExtractNewLoadSuggestions.
func promptLLMForCustomer(
	ctx context.Context,
	tmsID uint,
	email models.Email,
	attachmentData any, // Markdown string, Textract, or nil
	attachmentContentType string,
	openaiService openai.Service,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	rds RDSInterface,
) (_ *models.TMSCustomer, braintrustLogID string, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "promptLLMForCustomer", attrs)
	defer func() { metaSpan.End(err) }()

	accountDomain := extractDomain(email.Account)

	//nolint:lll
	prompt := fmt.Sprintf(
		`
		You are a freight broker receiving a request for a new shipment from a shipper/customer. You are tasked with determining
		the shipper's name. The following is the information you should extract:
		- shipper_name: Look for the shipper (the company requesting quotes/a new load tender) in the email body (including signature).
			If not found in the email body, look for it in the attachment data, if available. This should be a human readable
			company name such as "LMNOP Goods", NOT an email domain.
			Note that the shipper is not necessarily the same as the pickup or dropoff warehouse names.
			Do NOT include suffixes such as LLC, INC, and CO in your response.
		- original_sender_email: If the email is a forwarding chain, then look for the original external
			sender's email address in the body. By external sender, we mean the first email address does not have the
			same domain as the recipient, which is %s.


		Return your answer in the following JSON structure:
		{
			"shipper_name": string,
			"original_sender_email": string
		}
		e.g.
		{
			"shipper_name": "LMNOP Goods",
			"original_sender_email": "<EMAIL>"
		}

		Do not return any other text in your response, just the JSON.
		If you're unable to find the shipper's name, then return an empty string "" for that field.
		If you're unable to find the original sender's email address, then return an empty string "" for that field.

		Here are a few examples:

		-----EXAMPLE 1: Load Tender Request w/ PDF

		From: <EMAIL>
		Subject: Pickup tmrw please
		Email Body Type: markdown
		Email Body: I need a pickup TMRW. See attached for details.

		Attachment Data Type: markdown
		Attachment Data:
		Argo Fine Imports

		3045 Ridgelake Drive
		Suite 316
		Metairie, LA 70002
		Phone: ************ |

		Release # 96436646

		Reference:
		Vessel;


		Dispatch Date: 9/10/2024

		Prepaid

		PICKUP AT:

		Warehouse: Sesame WAREHOUSE, LLC
		Strictly by Appointment by 12:00 pm previous day

		986 B Sesame Street
		SAVANNAH. GA 31408
		************

		Address:

		Call Argo ASAP at (************* if there are anypickup issues/overweights

		SHIP TO

		RELEASE TO

		SOCCER INDUSTRIES-FLORIDA

		999 TED LASSO LANE
		LAKELAND, FL 33801
		SOCCER INDUSTRIES

		RECEIVING HOURS: MON - THURS 7:00 AM TO 3:30 PM, FRIDAY,

		7:00 AM TO 10:30 AM; NO APPT NECESSARY, CAN ACCOMMODATE

		FLATS AND VANS.

		BROKER/TRUCKER WAREHOUSE/PICKUP NOTES: 24 HOUR NOTICE REQUIRED NO SAME DAY APPTS. BROKER MUST CALL CBC

		TO MAKE AN APPPOINTMENT & GET A CONFIRMATION #! NO APPT BY EMAILS ACCEPTED. DRIVER MUST HAVE CONFIRMATION

		Your Expected Response {
			"shipper_name": "Argo Fine Imports",
			"original_sender_email": "<EMAIL>"
		}

		---- EXAMPLE 2: Quote Request with no PDFs
		Subject: Quote for 2/10
		Email Body Type: markdown
		Email Body: I've got an 850-lb flatbed shipment going from 02116 on 3/18 to 73301 on 3/20.

		Thanks,
		**\|**John Doe **\|** Traffic Manager

		**\|** 1800 Aston Ave, Suite 150, Carlsbad CA 92008

		**\|** [<EMAIL>](mailto:<EMAIL>|)
		**[\|](mailto:<EMAIL>|)** **(P)** 760-438-8030


		Your Expected Response:
		{
			"shipper_name": "MJB Wood",
			"original_sender_email": "<EMAIL>"
		}

		-----EXAMPLE 3: Forward chain
		Subject: FW: RFQ- Alliance, OH to Crawfordsville, IN \*\*\*HOTSHOT or PARTIAL w/ TARP

		Email Body Type: markdown
		Email Body:
		**From:** Candice Perez <<EMAIL>>
		**Sent:** Friday, February 21, 2025 2:53:04 PM (UTC-06:00) Central Time (US & Canada)
		**To:** C Perez <<EMAIL>>; K McMaster <<EMAIL>>
		**Subject:** RFQ- Alliance, OH to Crawfordsville, IN \*\*\*HOTSHOT or PARTIAL w/ TARP\*\*\*

		Please provide a quote to move the following load for Monday, 2/24 (Loading 0700 -1300)

		|     |     |     |     |     |     |     |     |
		| --- | --- | --- | --- | --- | --- | --- | --- |
		| **SHIPPING TO ADDRESS:** |  | **SHIPPING FROM ADDRESS:** |
		|  |
		| CRAWFORDSVILLE, IN 47933-0907 |  | ALLIANCE, OH 44601 |
		|  |  |  |  |  |  |
		|  |  |  |  |  |  |
		|  | **Tarps Req'd** | YES |  |  | **Freight Terms:** | DDP |
		|  | **Permits** | NO |  |  |  |  |
		|  | **Work Order** **(for freight costs)** | 26526 |  |  |  |  |
		|  |  |  |  |  |  |  |
		| **Customer Contact** |  |  |  |  |  |
		| Name | CHRIS DAVIES |  |  |  |  |
		| Phone # | ************ |  |  |  |  |
		| **Customer PO.** | **734476** |  |  |  |  |  |
		|  |
		| QTY | PART NUMBER | DESCRIPTION | EST. WT. EA. (#) | EST. WT. TOTAL (#) | SIZE OF PART OR pallets
		**L-W-H** **(INCHES)** | SPECIAL NOTES FOR SHIPMENT |
		| 1 | 24004A-1200 | ENTRY PPL HPU ASSEMBLY | 5241 | 5241 | 123-50-42 |  |  |
		|  |  |  |  |  |  |  |
		|  |  |  |  |  |  |  |  |


		Your Expected Response:
		{
			"shipper_name": "LMNOP Goods",
			"original_sender_email": "<EMAIL>"
		}

		The above are just examples. Do not reuse them in your response.
		`,
		accountDomain,
	)

	userPrompt := fmt.Sprintf(
		`
			Here is this email's data:

			From: %s
			Subject: %s
			Email Body Type: %s
			Email Body: %s

			Attachment Data Type: %s
			Attachment Data:
			%v
			`,
		email.Sender, email.Subject, email.BodyType, email.Body,
		attachmentContentType, attachmentData)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			Schema:          extractor.GenerateSchema[CustomerInfo](),
			UserPrompt:      userPrompt,
		},
	)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	llmOutput, err := extractor.StructExtractor[CustomerInfo](response.Content)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	log.Debug(ctx, "customer LLM output", zap.Any("customerLLMOutput", llmOutput))
	// Try lookup by name parsed by LLM
	if llmOutput.ShipperName != "" && !strings.Contains(strings.ToLower(llmOutput.ShipperName), "lmnop") {
		mappedCustomer, err := mapCustomer(ctx, llmOutput.ShipperName, tmsID, email, attachmentData, rds)
		if err != nil {
			log.WarnNoSentry(ctx, "error mapping customer by LLM parsed name",
				zap.Error(err),
				zap.String("name", llmOutput.ShipperName))
		} else if mappedCustomer != nil {
			return mappedCustomer, response.BraintrustLogID, nil
		}
	}

	// If error or no name found, lookup by domain
	if llmOutput.OriginalSenderEmail == "" ||
		strings.Contains(strings.ToLower(llmOutput.OriginalSenderEmail), "lmnop") {

		return nil,
			response.BraintrustLogID,
			fmt.Errorf("LLM did not return a customer name or domain: %v", llmOutput)
	}

	domain := extractor.ExtractSecondLevelDomain(llmOutput.OriginalSenderEmail)
	if domain == "" {
		domain = llmOutput.OriginalSenderEmail
	}

	mappedCustomer, err := mapCustomer(ctx, domain, tmsID, email, attachmentData, rds)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("failed to map domain %s to customer: %w", domain, err)
	}

	return mappedCustomer, response.BraintrustLogID, nil
}

func mapCustomer(
	ctx context.Context,
	parsedName string,
	tmsID uint,
	email models.Email,
	attachmentData any,
	rds RDSInterface,
) (mappedCustomer *models.TMSCustomer, err error) {

	if tmsID == 0 {
		return nil, errors.New("unable to map customer, TMS ID is 0")
	}

	var name string
	joinedEmail := fmt.Sprintf("%s %s", email.Subject, email.Body)
	edgeCaseCustomers := append(FetchFreightCustomers, TridentCustomers...) //nolint:gocritic
	// Check email & attachment data for hard coded Trident customers
	for _, customer := range edgeCaseCustomers {
		check := isStringInAttachment(ctx, customer, attachmentData) || // Attachment case
			util.IsStringFullMatch(joinedEmail, customer) // Email case
		if check {
			switch customer {
			case "MJB":
				name = "Liberty Woods International"
			case "ARGO":
				name = "ARGO Fine Imports"

			case "Steel Equipment Specialists", "SES, LLC", "seseng":
				name = "SES"
			}
			break
		}
	}
	if name == "" {
		name = parsedName
	}

	dbCustomer, err := rds.GetCustomerByName(ctx, tmsID, name)
	if err != nil {
		return nil, fmt.Errorf("db error getting customer: %w", err)
	}

	return &dbCustomer, nil
}
