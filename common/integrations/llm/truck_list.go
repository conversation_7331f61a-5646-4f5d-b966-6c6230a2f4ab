package llm

import (
	"context"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

func ExtractTruckListSuggestions(
	ctx context.Context,
	email models.Email,
	openAI openai.Service,
) (res models.TruckList, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractTruckListSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	type (
		Address struct {
			City    string `json:"city" jsonschema_description:"The full city name of the pickup/dropoff location"`
			State   string `json:"state" jsonschema_description:"The 2 letter state abbreviation of the pickup/dropoff location in uppercase"`
			Zip     string `json:"zip_code" jsonschema_description:"The zip code of the pickup/dropoff location if provided"`
			Country string `json:"country" jsonschema_description:"The country of the pickup/dropoff location"`
		}

		CarrierInformation struct {
			Name         string `json:"name" jsonschema_description:"The name of the carrier"`
			MC           string `json:"mc" jsonschema_description:"The motor carrier number"`
			DOT          string `json:"dot" jsonschema_description:"The US DOT number"`
			ContactEmail string `json:"contact_email" jsonschema_description:"The email address that is the 'sender' at the bottom of the email chain"`
			ContactName  string `json:"contact_name" jsonschema_description:"The contact name that corresponds to the contact email"`
		}

		Truck struct {
			PickupLocation  Address          `json:"pickup_location" jsonschema_description:"The address where the truck is picking up from"`
			PickupDate      models.NullTime  `json:"pickup_date" jsonschema_description:"The pickup date and time in format mm/dd/yyyy, hh:mmAM/PM if time is given, otherwise mm/dd/yyyy"`
			DropoffLocation Address          `json:"dropoff_location" jsonschema_description:"The address where the truck is dropping off to"`
			DropoffDate     models.NullTime  `json:"dropoff_date" jsonschema_description:"The dropoff date and time in format mm/dd/yyyy, hh:mmAM/PM if time is given, otherwise mm/dd/yyyy"`
			Type            models.TruckType `json:"type" jsonschema_description:"The truck type: VAN, REEFER, or FLATBED; default to VAN if undetermined"`
			Length          float32          `json:"length" jsonschema_description:"The truck length in feet, typically 48 or 53"`
			Notes           string           `json:"notes" jsonschema_description:"Relevant notes pertaining to this truck"`
		}

		PromptResponse struct {
			Carrier CarrierInformation `json:"carrier" jsonschema_description:"Information about the carrier company"`
			Trucks  []Truck            `json:"truck_list" jsonschema_description:"List of trucks available for scheduling"`
		}
	)

	//nolint:lll
	var prompt = `You are Drumkit - a bleeding edge logistics scheduling assistant that interfaces via email.
    Make sure your final answers are definitive, complete and well formatted.

    You'll receive an e-mail body from the user. You should look for truck lists and the corresponding carrier info.
    The information we're most concerned with for truck lists are pickup location, pickup date, dropoff location,
    dropoff date, type, and notes. These can appear in the emails in any number of formats. They will usually appear all
    together in the same list. As for carrier information, it usually appears in the signature at the bottom of the email.


    A pickup or dropoff location is an address that holds city, state, zip code, and country. We mostly care about city
    and state, though we want to capture all aforementioned fields. If we know it's a US state, then we can fill out the
    country field for example. If we encounter a nickname for a city, then we want to expand it to its full name and use
    the corresponding state name for state. If we encounter multiple states for a dropoff like 'OH/NC' then we want to
    use the first entry for the state. If a pickup location was given and a dropoff location was not, then set the
    dropoff location country to the pickup location country.

    Pickup and dropoff date are usually dates with time that we want to capture. If the pickup time is ready now but no
    time was given, then set the pickup time to the time the email was sent.

    Type indicates the truck type, which is either a 'VAN', 'REEFER', or 'FLATBED'. If you cannot determine the type,
    default to 'VAN'. For example, if the truck type is '53' dry van' use 'VAN' as the type. Do not use 'REEFER' or
    'FLATBED' unless the email body explicitly states that the truck is a reefer or flatbed.

    Length indicates the truck length, which is usually 48 or 53 feet long.

    One email can contain multiple truck lists for different dates.

    Your responses should be a JSON with this type structure:

       {
         "carrier": {
           "name": string,
           "mc": string,
           "dot": string,
           "contact_email": string,
           "contact_name": string
         },
         "truck_list": [
           {
             "pickup_date": string,
             "pickup_location": {
               "city": string,
               "state": string,
               "zip_code": string,
               "country": string
             },
             "dropoff_date": string,
             "dropoff_location": {
               "city": string,
               "state": string,
               "zip_code": string,
               "country": string
             },
             "type": string,
             "length": float32,
             "notes": string
           }
         ]
       }

    Here's additional context of what values we expect for the response:

       {
         "carrier": {
           "name": "the name of the carrier",
           "mc": "the motor carrier number",
           "dot": "the US DOT number",
           "contact_email": "the email address that is the 'sender' at the bottom of the email chain",
           "contact_name": "the contact name that corresponds to the contact email"
         },
         "truck_list": [
           {
             "pickup_date": "the pickup date and time",
             "pickup_location": {
               "city": "the full city name of the pickup location",
               "state": "the 2 letter state abbreviation of the pickup location",
               "zip_code": "the zip code of the pickup location if provided",
               "country": "the country of the pickup location"
             },
             "dropoff_date": "the dropoff date and time",
             "dropoff_location": {
               "city": "the full city of the dropoff location",
               "state": "the 2 letter state abbreviation of the dropoff location",
               "zip_code": "the zip code of the dropoff location if provided",
               "country": "the country of the dropoff location"
             },
             "type": "VAN, REEFER, or FLATBED",
             "length": "the truck length",
             "notes": "relevant notes pertaining to this truck"
           }
         ]
       }

    "pickup_date" should be the date when the load is ready for pickup in this format "mm/dd/yyyy, hh:mmAM/PM" if a time
    was given otherwise default to "mm/dd/yyyy".
    "dropoff_date" should be the date when the load is ready for dropoff in this format "mm/dd/yyyy, hh:mmAM/PM" if a
    time was given otherwise default to "mm/dd/yyyy".

    For both "pickup_date" and "dropoff_date", if the year isn't provided then infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `).

    Always respect visual hierarchy. This means that a truck's fields are all on the same line like pickup and dropoff
    location. A new line or row indicates a new truck that we're parsing. We'll also encounter airport names than are
    abbreviated (at times), which we have to expand to the corresponding city and state pair.

    Let's make sure that every city state pair entry is correct. If we parse "OH/NC" for a state entry, then pick the
    first item "OH". "OH/NC" is not a city entry so don't use it as such when you encounter these state formats.
    Validate that every entry for city and state exists. Use the 2 letter abbreviation in uppercase for the latter and
    the full name for the former.

    Never process .eml attachments.

    Examples:

    Example #1:

    Sender: <EMAIL>
    Subject: Truck lists

    Good morning all please see below for NDL open dry vans for the remainder of the week

    THURSDAY 3/7

    El Dorado KS

    FRIDAY 3/8

    Grand Prairie TX

    Lithonia GA

    Statesville NC

    Response:

    {
      "carrier": {
        "name": "",
        "mc": "",
        "dot": "",
        "contact_email": "<EMAIL>",
        "contact_name": ""
      },
      "truck_list": [
        {
          "pickup_date": "03/07/2024",
          "pickup_location": {
            "city": "El Dorado",
            "state": "KS",
            "zip_code": "",
            "country": "US"
          },
          "dropoff_date": '',
          "dropoff_location": {
            "city": "",
            "state": "",
            "zip_code": "",
            "country": ""
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "03/08/2024",
          "pickup_location": {
            "city": "Grand Prairie",
            "state": "TX",
            "zip_code": "",
            "country": "US"
          }
          "dropoff_date": '',
          "dropoff_location": {
            "city": "",
            "state": "",
            "zip_code": "",
            "country": ""
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "03/08/2024",
          "pickup_location": {
            "city": "Lithonia",
            "state": "GA",
            "zip_code": "",
            "country": "US"
          }
          "dropoff_date": '',
          "dropoff_location": {
            "city": "",
            "state": "",
            "zip_code": "",
            "country": ""
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "03/08/2024",
          "pickup_location": {
            "city": "Statesville",
            "state": "NC",
            "zip_code": "",
            "country": "US"
          }
          "dropoff_date": '',
          "dropoff_location": {
            "city": "",
            "state": "",
            "zip_code": "",
            "country": ""
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        }
      ]
    }

    Example #2:

    [EXTERNAL EMAIL]

    Tuesday 7/9

    Wednesday 7/10
    Le Suer, Mn Empty around 1130. Back to Mi.
    Clanton, Al Empty around 1300. Back to Mi.

    Thursday 7/11
    Omaha, Ne Empty around 0930. Back to Mi.
    Fort Worth, Tx Empty around 1000. Back to Mi.

    Response:

    {
      "carrier": {
        "name": "",
        "mc": "",
        "dot": "",
        "contact_email": "",
        "contact_name": ""
      },
      "truck_list": [
        {
          "pickup_date": "07/10/2024, 11:30AM",
          "dropoff_date": "",
          "pickup_location": {
            "city": "Le Suer",
            "state": "MN",
            "zip_code": "",
            "country": "US"
          },
          "dropoff_location": {
            "city": "",
            "state": "MI",
            "zip_code": "",
            "country": "US"
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "07/10/2024, 01:00PM",
          "dropoff_date": "",
          "pickup_location": {
            "city": "Clanton",
            "state": "AL",
            "zip_code": "",
            "country": "US"
          },
          "dropoff_location": {
            "city": "",
            "state": "MI",
            "zip_code": "",
            "country": "US"
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "07/11/2024, 09:30AM",
          "dropoff_date": "",
          "pickup_location": {
            "city": "Omaha",
            "state": "NE",
            "zip_code": "",
            "country": "US"
          },
          "dropoff_location": {
            "city": "",
            "state": "MI",
            "zip_code": "",
            "country": "US"
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        },
        {
          "pickup_date": "07/11/2024, 10:00AM",
          "dropoff_date": "",
          "pickup_location": {
            "city": "Fort Worth",
            "state": "TX",
            "zip_code": "",
            "country": "US"
          },
          "dropoff_location": {
            "city": "",
            "state": "MI",
            "zip_code": "",
            "country": "US"
          },
          "type": "VAN",
          "length": 53,
          "notes": ""
        }
      ]
    }

    Special case:

    If no truck list was found, return:

    {
      "carrier": {},
      "truck_list": []
    }

    Important note: If you find that the carrier information extracted appears to have the same domain as the email
    account or recipients, it is likely incorrect. In such cases, attempt to extract carrier information from earlier
    emails in the thread to find more accurate details.

    Final note: Sometimes customers include key information about the truck lists at the beginning or end of the email.
    For example, a customer may state, "we are looking for loads coming back close to home here in Weyers Cave, VA
    unless otherwise noted". In this case, you should set all the truck dropoff locations to Weyers Cave, VA unless
    otherwise noted in the truck list.

    The user e-mails are in English - US date format.`

	userPrompt := "Sender:" + email.Sender +
		"\n" + "Subject: " + email.Subject +
		"\n" + "Body Type: " + string(email.BodyType) +
		"\n" + email.Body

	response, err := openAI.GetResponse(
		ctx,
		email,
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		openai.ResponseOptions{
			UserPrompt:      userPrompt,
			DeveloperPrompt: prompt,
		},
	)
	if err != nil {
		log.ErrorNoSentry(ctx, "OpenAI truck list parsing failed", zap.Error(err))
		return res, nil
	}

	log.Infof(
		ctx,
		"OpenAI %s response for email id %d: %s",
		string(models.TruckListPipeline),
		email.ID,
		response.Content,
	)

	promptResponse, err := extractor.StructExtractor[PromptResponse](response.Content)
	if err != nil {
		log.ErrorNoSentry(ctx, "failed to extract JSON from OpenAI response", zap.Error(err))
		return res, nil
	}

	var trucks []models.Truck

	for _, t := range promptResponse.Trucks {
		setAddress := func(src Address) (models.Address, bool) {
			dest := models.Address{
				Country: src.Country,
			}

			switch {
			case src.City != "" && src.State != "":
				dest.City = src.City
				dest.State = src.State
				dest.Zip = src.Zip
				dest.Country = src.Country
				return dest, true
			case src.State != "":
				dest.State = src.State
				dest.Zip = src.Zip
				dest.Country = src.Country
				return dest, true
			case src.Zip != "":
				dest.Zip = src.Zip
				dest.Country = src.Country
				return dest, true
			default:
				return dest, false
			}
		}

		pickupLocation, pickupValid := setAddress(t.PickupLocation)
		dropoffLocation, dropoffValid := setAddress(t.DropoffLocation)

		// Check if the dropoff location is equivalent to the pickup location based on specific fields
		if dropoffValid && addressesAreEquivalent(dropoffLocation, pickupLocation) {
			// Reset the dropoff location
			dropoffLocation = models.Address{}
		}

		if pickupValid {
			truck := models.Truck{
				UserID:    email.UserID,
				ServiceID: email.ServiceID,
				EmailID:   email.ID,
				ThreadID:  email.ThreadID,
			}

			truck.PickupDate.Suggestion = t.PickupDate
			truck.PickupLocation.Suggestion = pickupLocation
			truck.DropoffDate.Suggestion = t.DropoffDate
			truck.DropoffLocation.Suggestion = dropoffLocation
			if util.IsStringInArray(models.TruckTypesThatMapToFlatbed, string(t.Type)) {
				truck.Type.Suggestion = models.FlatbedTruckType
			} else {
				truck.Type.Suggestion = t.Type
			}
			truck.Length.Suggestion = t.Length
			truck.Notes.Suggestion = t.Notes

			trucks = append(trucks, truck)
		}
	}

	// Extract domains to check if the contact email is from the same domain
	// as the account or any recipient, indicating potentially incorrect parsing.
	contactEmailDomain := extractDomain(promptResponse.Carrier.ContactEmail)
	accountDomain := extractDomain(email.Account)
	recipientsDomains := extractDomains(email.Recipients)

	// set contact email as sender if the LLM didn't extract it
	if promptResponse.Carrier.ContactEmail == "" {
		promptResponse.Carrier.ContactEmail = email.Sender
	}

	// If the contact email domain matches the account or any of the recipients' domains,
	// it suggests that we might have misinterpreted the carrier information, so we reset it.
	if contactEmailDomain == accountDomain || recipientsDomains[contactEmailDomain] {
		promptResponse.Carrier.Name = ""
		promptResponse.Carrier.MC = ""
		promptResponse.Carrier.DOT = ""
		promptResponse.Carrier.ContactEmail = ""
		promptResponse.Carrier.ContactName = ""
	}

	res = models.TruckList{
		UserID:    email.UserID,
		ServiceID: email.ServiceID,
		EmailID:   email.ID,
		ThreadID:  email.ThreadID,
		Carrier: models.CarrierInformation{
			Name:         promptResponse.Carrier.Name,
			MC:           promptResponse.Carrier.MC,
			DOT:          promptResponse.Carrier.DOT,
			ContactEmail: promptResponse.Carrier.ContactEmail,
			ContactName:  promptResponse.Carrier.ContactName,
		},
		Trucks:  trucks,
		IsDraft: true,
	}

	log.Debug(ctx, "parsed truck list", zap.Any("carrier", res.Carrier), zap.Any("truck", res.Trucks))
	return res, err
}

// extractDomain extracts the domain from an email address
func extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 1 {
		return parts[1]
	}

	return ""
}

// extractDomains parses a comma-delimited string of email addresses and returns a map of domains
func extractDomains(emails string) map[string]bool {
	domains := make(map[string]bool)

	addresses := strings.Split(emails, ",")
	for _, address := range addresses {
		domain := extractDomain(strings.TrimSpace(address))
		if domain != "" {
			domains[domain] = true
		}
	}

	return domains
}

func addressesAreEquivalent(a, b models.Address) bool {
	return a.City == b.City && a.State == b.State
}
