package llm

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// TODO: Give LLM selective thread context ENG-2213
// Extract total cost, availability, and notes from a carrier quote response email.
// This should be run on a email reply to a generated carrier quote request email (sent out via Carrier Quote)
func ExtractCarrierQuoteResponse(
	ctx context.Context,
	email models.Email,
	openAI openai.Service,
) (res models.CarrierQuote, err error) {

	//nolint:lll
	var prompt = `You are Drumkit - a cutting edge logistics assistant that interfaces via email.
	Make sure your final answers are definitive, complete and well formatted.

	You'll receive an e-mail body from a carrier potentially providing a rate/quote for a new shipment.
	You should look for confirmation of the carrier's availability, their rate, and any special notes.

	Your responses should be a JSON with the structure:
	{
	   "isAvailable": boolean,
	   "notes": string,
	   "totalCost": float,
	}

	In the above output format you should assign:
	'totalCost' with the total amount the carrier is charging,
	'isAvailable' as true or false if the carrier is available for this job,
	'notes' with any notable information that the carrier provides about the quote, like: wait time pricing, fuel surcharge, or time window.

	Below are 3 example input emails and responses.

	Example 1:

		Hey Sharon!

		We don't do a whole lot of AZ so our price will probably be too inflated.
		We could get it for $3499 all-in.

		Sincerely,
		John

		Response:
		{
			"isAvailable": true,
			"notes": "We don't do a whole lot of AZ so our price will probably be too inflated.",
			"totalCost": 3499,
		}

	Example 2:

		Hi!

		The rate would be $899 + FSC. I've got drivers available starting tmrw 9 - 3

		Best,
		Jane

		Response:

		{
			"isAvailable": true,
			"notes": "FSC separate. Drivers available tomorrow 9am - 3pm",
			"totalCost": 899,
		}

	Example 3:

		Sorry, our team is at capacity.

		Response:

		{
			"isAvailable": false,
			"notes": "Team at capacity",
			"totalCost": 0,
		}

	You can assume that if the user provides a quote that it means they are available to complete the shipment.
	You should keep notes brief and summarize caveats the user mentions about that quote, primarily if the rate is all-in or if other fees are separate.
	If no additional notes are provided, then return the empty string "" for notes.
	If no quote is provided, just return null instead of a struct with all empty fields.

	Read the provided input email and generate an output with the described JSON format.`

	userPrompt := email.Subject + "\n" + email.BodyWithoutSignature

	response, err := openAI.GetResponse(
		ctx,
		email,
		braintrustsdk.CreateProjectDetails(braintrustsdk.CQExtractCarrierQuoteResponse, false),
		openai.ResponseOptions{
			UserPrompt:      userPrompt,
			DeveloperPrompt: prompt,
			Schema:          extractor.GenerateSchema[models.CarrierQuoteData](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("failed to get OpenAI response: %w", err)
	}

	log.Infof(ctx, "OpenAI response: %s", response.Content)

	resp, err := extractor.StructExtractor[models.CarrierQuoteData](response.Content)
	if err != nil {
		return res, fmt.Errorf("failed to extract JSON from OpenAI response: %w", err)
	}

	quote := models.CarrierQuote{
		EmailID:     email.ID,
		ThreadID:    email.ThreadID,
		ServiceID:   email.ServiceID,
		Status:      models.RespondedCarrierQuoteStatus,
		RecipientID: email.UserID,
		SuggestedQuote: &models.CarrierQuoteData{
			TotalCost:   resp.TotalCost,
			Currency:    "USD",
			IsAvailable: resp.IsAvailable,
			Notes:       resp.Notes,
		},
	}

	return quote, nil
}
