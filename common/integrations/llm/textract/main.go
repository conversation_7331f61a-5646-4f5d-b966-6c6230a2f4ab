package textract

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	awstextract "github.com/aws/aws-sdk-go-v2/service/textract"
	"github.com/aws/aws-sdk-go-v2/service/textract/types"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	Client interface {
		AnalyzeDocument(
			ctx context.Context,
			input *awstextract.AnalyzeDocumentInput,
			opts ...func(*awstextract.Options),
		) (*awstextract.AnalyzeDocumentOutput, error)
	}

	// Block wraps the AWS Textract Block for safer access.
	Block struct {
		types.Block
	}

	ShippingOrder struct {
		Lines         []string            `json:"lines"`
		KeyValuePairs []map[string]string `json:"keyValuePairs"`
		Tables        []any               `json:"tables"`
	}
)

var (
	client Client
	once   sync.Once
)

func GetTextractClient(ctx context.Context) Client {
	once.Do(func() {
		cfg, err := config.LoadDefaultConfig(ctx,
			config.WithRegion("us-east-1"),
			config.WithClientLogMode(aws.LogRequestWithBody|aws.LogResponseWithBody),
		)
		if err != nil {
			log.Error(ctx, "failed to load AWS config", zap.Error(err))
		}

		client = awstextract.NewFromConfig(cfg)
	})

	return client
}

func ExtractData(ctx context.Context, attachmentURL string, textractClient Client) (result ShippingOrder, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "textract.extract_data", []attribute.KeyValue{
		attribute.String("attachment_url", attachmentURL),
	})
	defer func() { metaSpan.End(err) }()

	parsedURL, err := url.Parse(attachmentURL)
	if err != nil {
		return ShippingOrder{}, fmt.Errorf("failed to parse S3 URL: %w", err)
	}

	queryParams := parsedURL.Query()

	objectKey := queryParams.Get("prefix")
	bucketName := strings.TrimPrefix(parsedURL.Path, "/s3/object/")

	if bucketName == "" || objectKey == "" {
		return ShippingOrder{}, fmt.Errorf("empty bucket or object key in S3 URL %s", attachmentURL)
	}

	formsFeatureType := types.FeatureTypeForms
	tablesFeatureType := types.FeatureTypeTables

	input := &awstextract.AnalyzeDocumentInput{
		Document: &types.Document{
			S3Object: &types.S3Object{
				Bucket: aws.String(bucketName),
				Name:   aws.String(objectKey),
			},
		},
		FeatureTypes: []types.FeatureType{formsFeatureType, tablesFeatureType},
	}

	output, err := textractClient.AnalyzeDocument(ctx, input)
	if err != nil {
		return ShippingOrder{}, fmt.Errorf("error analyzing document with textract: %w", err)
	}

	if output == nil {
		err := fmt.Errorf("nil output from textract for %s", attachmentURL)
		return ShippingOrder{}, err
	}

	extractedData := extractRelevantTextractData(output)
	if len(extractedData.Lines) == 0 {
		err := fmt.Errorf("no relevant data parsed from the textract output for %s", attachmentURL)
		return ShippingOrder{}, err
	}

	return extractedData, nil
}

// Safe accessor methods for TextractBlock.
func (b *Block) GetText() string {
	if b.Text != nil {
		return *b.Text
	}

	return ""
}

func (b *Block) GetBlockType() string {
	return string(b.BlockType)
}

func (b *Block) GetID() string {
	if b.Id != nil {
		return *b.Id
	}

	return ""
}

func (b *Block) GetEntityTypes() []types.EntityType {
	return b.EntityTypes
}

func (b *Block) GetRelationships() []types.Relationship {
	return b.Relationships
}

func (b *Block) GetRowIndex() int {
	if b.RowIndex != nil {
		return int(*b.RowIndex)
	}

	return 0
}

func (b *Block) GetColumnIndex() int {
	if b.ColumnIndex != nil {
		return int(*b.ColumnIndex)
	}

	return 0
}

func wrapBlocks(blocks []types.Block) map[string]*Block {
	blockMap := make(map[string]*Block)
	for i := range blocks {
		blockCopy := blocks[i]
		block := &Block{Block: blockCopy}
		blockMap[block.GetID()] = block
	}

	return blockMap
}

func extractLines(blocks map[string]*Block) []string {
	lines := []string{}
	for _, block := range blocks {
		if block.GetBlockType() == "LINE" {
			lines = append(lines, block.GetText())
		}
	}

	return lines
}

func extractKeyValuePairs(blocks map[string]*Block) []map[string]string {
	keyValuePairs := []map[string]string{}
	for _, block := range blocks {
		if block.GetBlockType() != "KEY_VALUE_SET" {
			continue
		}

		isKey := false
		for _, entityType := range block.GetEntityTypes() {
			if entityType == types.EntityTypeKey {
				isKey = true
				break
			}
		}

		if !isKey {
			continue
		}

		key := extractTextFromRelationships(blocks, block, "CHILD")
		value := ""
		for _, rel := range block.GetRelationships() {
			if rel.Type == types.RelationshipTypeValue {
				for _, valueID := range rel.Ids {
					if valueBlock, ok := blocks[valueID]; ok {
						value += extractTextFromRelationships(blocks, valueBlock, "CHILD")
					}
				}
			}
		}

		keyValuePairs = append(keyValuePairs, map[string]string{
			"Key":   strings.TrimSpace(key),
			"Value": strings.TrimSpace(value),
		})
	}

	return keyValuePairs
}

func extractTables(blocks map[string]*Block) []any {
	tables := []any{}
	for _, block := range blocks {
		if block.GetBlockType() != "TABLE" {
			continue
		}

		table := []map[string]any{}
		for _, relationship := range block.GetRelationships() {
			if relationship.Type == types.RelationshipTypeChild {
				for _, childID := range relationship.Ids {
					if cellBlock, ok := blocks[childID]; ok && cellBlock.GetBlockType() == "CELL" {
						cellText := extractTextFromRelationships(blocks, cellBlock, "CHILD")
						table = append(table, map[string]any{
							"RowIndex":    cellBlock.GetRowIndex(),
							"ColumnIndex": cellBlock.GetColumnIndex(),
							"Content":     strings.TrimSpace(cellText),
						})
					}
				}
			}
		}
		tables = append(tables, table)
	}

	return tables
}

func extractTextFromRelationships(
	blocks map[string]*Block,
	block *Block,
	relationshipType types.RelationshipType,
) string {

	text := ""
	for _, rel := range block.GetRelationships() {
		if rel.Type == relationshipType {
			for _, childID := range rel.Ids {
				if wordBlock, ok := blocks[childID]; ok && wordBlock.GetBlockType() == "WORD" {
					text += wordBlock.GetText() + " "
				} else if childBlock, ok := blocks[childID]; ok {
					// Recursively extract text if it's not a WORD block.
					text += extractTextFromRelationships(blocks, childBlock, relationshipType)
				}
			}
		}
	}

	return text
}

func extractRelevantTextractData(textractResponse *awstextract.AnalyzeDocumentOutput) ShippingOrder {
	blockMap := wrapBlocks(textractResponse.Blocks)
	lines := extractLines(blockMap)
	keyValuePairs := extractKeyValuePairs(blockMap)
	tables := extractTables(blockMap)

	extractedData := ShippingOrder{
		Lines:         lines,
		KeyValuePairs: keyValuePairs,
		Tables:        tables,
	}

	return extractedData
}
