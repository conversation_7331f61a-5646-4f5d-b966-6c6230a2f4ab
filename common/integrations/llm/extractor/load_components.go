package extractor

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/timezone"
)

//nolint:lll
const (
	GenericInstructions = `
	Missing Information: If any required information is absent in the shipping order, set the field's value to empty.
	Data Integrity: Only extract information directly from the shipping order; do not make any assumptions or add any data not explicitly provided.
	IMPORTANT: Provide ONLY the JSON object in your response, nothing else. Do not include any explanatory text, markdown formatting, or code blocks around the JSON.
	Do not use the example output provided here in the final response. The example is for reference purposes only.
	`
)

//nolint:lll
var (
	dateTimeNotes = `
	Date/Time Formatting:
	If a specific date & time is provided, format it as "mm/dd/yyyy, hh:MM+AM/PM".
		Example: "03/07/2024, 02:00AM".
	Do not mix formats - if time is in 24-hour format, always convert to 12-hour time with AM/PM indicators.
	If no specific time is given, use only the date in the format "mm/dd/yyyy".
		Example: "03/07/2024".
	If year is not specified, infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `).
	Avoid partial or incorrect formats, such as "03/07/2024, " or "03/07". If the date is incomplete (lacking month, day, or year), set the field to null.
	`
)

// extractBasicInfo extracts basic load information from either an email body or attachment
func extractBasicInfo(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	initialResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result BasicInfo, rawResult BasicInfo, braintrustLogID string, err error) {
	var refNumNotes string

	// Handle special case of LLM getting confused between MJB shipping order and customer PO
	if email.ServiceID == TridentServiceID {
		if check := checkForStringInUserPrompt("MJB", userPrompt); check {
			refNumNotes = `Note in the case of MJB, the ref # is the shipping order number listed after 'Ship To'.
			And the Customer PO should be assigned to the poNums field.`
		}
	}

	//nolint:lll
	prompt := fmt.Sprintf(`
    Extract the following basic information:
    Instructions:
		"refNumber": Extract the shipping order number or the release number. Never extract a warehouse code as the refNumber.
		If both a PO # and a order/release/shipping order # are provided, this field should be the order/release number, not the PO #.
		Never include additional text here, only extract the ref number itself.
		%s
		"poNums": Extract the PO # if provided. If both a PO # and a order/release/shipping order # are provided, this field should be the PO #

    %s
    `, refNumNotes, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			Schema:             GenerateSchema[BasicInfo](),
			DeveloperPrompt:    prompt,
			PreviousResponseID: initialResponseID,
		},
	)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[BasicInfo](response.Content)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return result, result, response.BraintrustLogID, nil
}

// extractCustomer extracts customer information from either an email body or attachment
func extractCustomer(
	ctx context.Context,
	openaiService openai.Service,
	tmsID uint,
	email models.Email,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (
	tmsCustomer *models.TMSCustomer,
	rawLLMCustomer CustomerInfo,
	initialResponseID string,
	braintrustLogID string,
	err error,
) {

	accountDomain := extractDomain(email.Account)

	//nolint:lll
	prompt := fmt.Sprintf(`
	Extract the customer company name. This information is typically found at the top of the document.
	The following is the information you should extract:
		- shipper_name: Look for the shipper (the company requesting quotes/a new load tender) in the email body (including signature).
			If not found in the email body, look for it in the attachment data, if available. This should be a human readable
			company name such as "LMNOP Goods", NOT an email domain.
			Note that the shipper is not necessarily the same as the pickup or dropoff warehouse names.
			Do NOT include suffixes such as LLC, INC, and CO in your response.
		- original_sender_email: If the email is a forwarding chain, then look for the original external
			sender's email address in the body. By external sender, we mean the first email address that does not have the
			same domain as the recipient, which is %s.
			If no external sender email is found in the body, use the email account of the sender.

		If you're unable to find the shipper's name, then return an empty string "" for that field.
		%s
		`, accountDomain, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			Schema:          GenerateSchema[CustomerInfo](),
			UserPrompt:      userPrompt,
		},
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("error getting response: %w", err)
	}

	result, err := StructExtractor[CustomerInfo](response.Content)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	rawLLMCustomer = result

	// Try looking up by name parsed from LLM
	if result.ShipperName != "" && !strings.Contains(strings.ToLower(result.ShipperName), "lmnop") {
		// If the shipper name is not LMNOP, then we need to find the original sender email
		// and use that to map to a TMSCustomer
		mappedCustomer, err := MapCustomer(
			ctx, tmsID, result.ShipperName, userPrompt,
		)
		if err != nil {
			return tmsCustomer,
				rawLLMCustomer,
				response.ResponseID,
				response.BraintrustLogID,
				fmt.Errorf("failed to map customer %s to customer: %w", result.ShipperName, err)
		} else if mappedCustomer != nil {
			return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
		}
	}

	// If no mapped customer, then try looking up by original sender email
	if result.OriginalSenderEmail == "" || strings.Contains(strings.ToLower(result.OriginalSenderEmail), "lmnop") {
		log.WarnNoSentry(
			ctx, "no original sender email found",
			zap.String("originalSenderEmail", result.OriginalSenderEmail),
		)

		return tmsCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
	}

	domain := ExtractSecondLevelDomain(result.OriginalSenderEmail)
	if domain == "" {
		domain = result.OriginalSenderEmail
	}

	mappedCustomer, err := MapCustomer(
		ctx, tmsID, domain, userPrompt,
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to map domain %s to customer: %w", domain, err)
	}

	return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
}

// extractRateData extracts rate information from either an email body or attachment
func extractRateData(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tms models.Integration,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result RateDataInfo, rawResult RateDataInfo, braintrustLogID string, err error) {

	//nolint:lll
	prompt := fmt.Sprintf(`
    Extract the following rate information:
    Instructions:

		Field Mapping:
		"collectionMethod": Must be one of the following values: "COD", "Collect", "Third-Party", or "Prepaid". If the collection method is not explicitly stated in the shipping order, default to "Prepaid".
		"customerRateType": Must be one of the following values: "Flat", "Distance", "CWT" (hundredweight), or "Tons". If the rate type is not explicitly stated, default to "Flat".
		"customerLineHaulRate": Set this field to the specified rate if explicitly mentioned. If not, set it to null.


		%s
    `, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[RateDataSchema](),
			PreviousResponseID: previousResponseID,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[RateDataInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if email.ServiceID != TridentServiceID {
		// Skip Mcleod specific rate data processing
		return result, rawResult, response.BraintrustLogID, nil
	}

	if !util.IsStringInArray([]string{"COD", "Collect", "Third-Party", "Prepaid"}, result.RateData.CollectionMethod) {
		// HACK: Historical data shows most of Payton's customers are using Third-Party
		if strings.Contains(email.Account, "<EMAIL>") ||
			strings.Contains(email.Account, "<EMAIL>") {

			result.RateData.CollectionMethod = "Third-Party"
		} else {
			result.RateData.CollectionMethod = "Prepaid"
		}

		// HACK: Hard-code revenue code for Payton because his email is different in Mcleod
		if strings.Contains(email.Account, "<EMAIL>") ||
			strings.Contains(email.Account, "<EMAIL>") {

			result.RateData.RevenueCode = "Charleston"

		} else if tms.Name == models.McleodEnterprise {
			tmsUser, err := tmsUserDB.GetByEmail(ctx, email.Account, tms.ID)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error getting TMS user by email for revenue code",
					zap.Error(err),
					zap.String("emailAddress", email.Account),
				)
			} else {
				result.RateData.RevenueCode = tmsUser.RevenueCode
			}
		}
	}
	if !util.IsStringInArray([]string{"Flat", "Distance", "CWT", "Tons"}, result.RateData.CustomerRateType) {
		result.RateData.CustomerRateType = "Flat"
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// extractPickup extracts pickup information from either an email body or attachment
func extractPickup(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tmsID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result PickupInfo, rawResult PickupInfo, braintrustLogID string, err error) {

	prompt := fmt.Sprintf(`
    Extract the following pickup information:

    IMPORTANT: Look for sections labeled "Ship From:", "Pickup From:", "Origin:", or "Shipper:".
    Do NOT extract the "Ship To:" or destination location information here.

    Format the response as a JSON object with a "pickup" field containing:
    - name: Company or facility name (do not include address in this field)
    - addressLine1: Street address
    - addressLine2: Suite, unit, etc.
    - city: City name
    - state: State code
    - zipCode: Postal code
    - country: Country code (e.g., "US")
    - contact: Contact person name
    - phone: Phone number (formatted with hyphens: ************)
    - email: Email address
    - businessHours: Business hours if specified
    - refNumber: Reference number for this location
    - readyTime: When shipment is ready for pickup (format: "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy")
    - apptType: Appointment type
    - apptStartTime: Appointment start time (same format as readyTime)
    - apptEndTime: Appointment end time (same format as readyTime)
    - apptNote: Notes about the appointment
    - timezone: Timezone code (e.g., "EDT", "CST")

    %s

    Example:
    In a document with "Ship From: ABC Company, Dallas TX" and "Ship To: XYZ Company, Miami FL",
    you should extract ABC Company as the pickup, not XYZ Company.

	%s
    `, dateTimeNotes, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[PickupSchema](),
			PreviousResponseID: previousResponseID,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[PickupInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if !result.Pickup.ApptStartTime.Valid {
		result.Pickup.ApptStartTime = result.Pickup.ReadyTime
	}

	tz, err := timezone.GetTimezoneByZipOrCity(ctx, result.Pickup.Zipcode, result.Pickup.City, result.Pickup.State, "")
	if err != nil {
		log.WarnNoSentry(ctx, "error getting pickup timezone", zap.Error(err))
	} else {
		// Start time and end time are in UTC, so we need to denormalize them to the pickup's timezone
		denormalizedTime, err := timezone.DenormalizeUTC(
			result.Pickup.ApptStartTime.Time, tz)
		if err == nil {
			result.Pickup.ApptStartTime.Time = denormalizedTime
		}

		denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ApptEndTime.Time, tz)
		if err == nil {
			result.Pickup.ApptEndTime.Time = denormalizedTime
		}

		denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ReadyTime.Time, tz)
		if err == nil {
			result.Pickup.ReadyTime.Time = denormalizedTime
		}
	}

	result.Pickup.CompanyCoreInfo = ValidateStop(ctx, "pickup", result.Pickup.CompanyCoreInfo, tmsID)

	return result, rawResult, response.BraintrustLogID, nil
}

// extractConsignee extracts consignee information from either an email body or attachment
func extractConsignee(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tmsID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result ConsigneeInfo, rawResult ConsigneeInfo, braintrustLogID string, err error) {

	prompt := fmt.Sprintf(`
    Extract the consignee (delivery/destination) location information.
    The consignee is the FINAL DELIVERY LOCATION for the shipment.

    IMPORTANT: Look for sections labeled "Ship To:", "Deliver To:", "Consignee:", or "Destination:".
    Do NOT extract the "Ship From:" or origin location information here.

    If there are multiple stops, extract the LAST stop as the consignee.

    Format the response as a JSON object with a "consignee" field containing:
    - name: Company or facility name (do not include address in this field)
    - addressLine1: Street address
    - addressLine2: Suite, unit, etc.
    - city: City name
    - state: State code
    - zipCode: Postal code
    - country: Country code (e.g., "US")
    - contact: Contact person name
    - phone: Phone number (formatted with hyphens: ************)
    - email: Email address
    - businessHours: Business hours if specified
    - refNumber: Reference number for this location
    - mustDeliver: Latest date/time shipment must be delivered (format: "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy")
    - apptType: Appointment type
    - apptStartTime: Appointment start time (same format as mustDeliver)
    - apptEndTime: Appointment end time (same format as mustDeliver)
    - apptNote: Notes about the appointment
    - timezone: Timezone code (e.g., "EDT", "CST")
    - externalTMSID: Set to null.

	%s

    Example:
    In a document with "Ship From: ABC Company, Dallas TX" and "Ship To: XYZ Company, Miami FL",
    you should extract XYZ Company as the consignee, not ABC Company.

	%s
    `, dateTimeNotes, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[ConsigneeSchema](),
			PreviousResponseID: previousResponseID,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[ConsigneeInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if !result.Consignee.ApptEndTime.Valid {
		result.Consignee.ApptEndTime = result.Consignee.MustDeliver
	}

	tz, err := timezone.GetTimezoneByZipOrCity(ctx,
		result.Consignee.Zipcode, result.Consignee.City, result.Consignee.State, "")
	if err != nil {
		log.WarnNoSentry(ctx, "error getting consignee timezone", zap.Error(err))
	} else {
		// Start time and end time are in UTC, so we need to denormalize them to the consignee's timezone
		denormalizedTime, err := timezone.DenormalizeUTC(
			result.Consignee.ApptStartTime.Time, tz)
		if err == nil {
			result.Consignee.ApptStartTime.Time = denormalizedTime
		}

		denormalizedTime, err = timezone.DenormalizeUTC(
			result.Consignee.ApptEndTime.Time, tz)
		if err == nil {
			result.Consignee.ApptEndTime.Time = denormalizedTime
		}

		denormalizedTime, err = timezone.DenormalizeUTC(result.Consignee.MustDeliver.Time, tz)
		if err == nil {
			result.Consignee.MustDeliver.Time = denormalizedTime
		}

	}

	result.Consignee.CompanyCoreInfo = ValidateStop(ctx, "consignee", result.Consignee.CompanyCoreInfo, tmsID)

	return result, rawResult, response.BraintrustLogID, nil
}

// extractSpecifications extracts specification information from either an email body or attachment
func extractSpecifications(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tms models.Integration,
	previousResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	options Options,
) (result SpecificationsInfo, rawResult SpecificationsInfo, braintrustLogID string, err error) {

	prompt := fmt.Sprintf(`
    Extract the shipment specifications:

    Format your response as a JSON object with a "specifications" field containing:
    - transportType: One of "VAN", "REEFER", "FLATBED", "BOX TRUCK", or "HOTSHOT" (default to "VAN" if not specified)
    - serviceType: Service type if specified
    - transportSize: Vehicle size (e.g., "53 ft")
    - totalInPalletCount: Number of pallets/crates
    - commodities: Description of goods being shipped
    - totalWeight: Object with "val" (number) and "unit" (string, usually "lbs")
    - billableWeight: Object with "val" (number) and "unit" (string, usually "lbs")
    - minTempFahrenheit: Minimum temperature (for refrigerated loads)
    - maxTempFahrenheit: Maximum temperature (for refrigerated loads)

	%s
    `, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[SpecificationsSchema](),
			PreviousResponseID: previousResponseID,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[SpecificationsInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.Specifications.TransportType = string(
		validateTransportType(
			ctx, models.TransportType(result.Specifications.TransportType), userPrompt, options.Config),
	)

	// @Sophie plans to push code to introduce a solution to handle TMS specific fields.
	// For now, we conditionally set fields based on the TMS;

	switch tms.Name {
	case models.Turvo:
		turvoSpecs := turvo.BuildTurvoSpecifications(result.Specifications)
		result.Specifications = turvoSpecs
	case models.McleodEnterprise:
		// associate commodities with their Mcleod commodity code
		mcleodCommodityCode := mcleodenterprise.ToTMSCommodity(tms.Tenant, result.Specifications.Commodities)
		result.Specifications.Commodities = mcleodCommodityCode
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// IsAttachmentNewLoadInformation checks if a userPrompt contains load building or quote request information
// We currently do not use this function, but it is kept here for future reference.
func IsAttachmentNewLoadInformation(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (isLoadBuilding bool, braintrustLogID string, err error) {

	//nolint:lll
	prompt := `
	You are an expert logistics email analyzer. Your task is to identify if this email contains load building or quote request information.

	Load building definition:
	- load building: About creating/building a new load or order with specific details
			Detailed Example Email:
				Subject: New shipment details - please create order

				Warehouse Code:
				Address:
				115-0010
				Ship To:
				5019494
				08/21/2024
				Shipping Order
				Shipping Order No.:
				Order Date:
				Customer PO:
				Ship From:
				Kronospan PB, LLC
				Kronospan PB, LLC
				1 Kronospan Way
				Eastaboga, AL United States
				MJB Anniston
				1608 Frank Akers Road
				Anniston, AL 36207
				1585 High Meadows Way
				Cedar Hill, TX 75104
				www.mjbwood.com
				************
				Page 1 of 1
				Phone No.:
				Phone No.:
				Salesperson
				Ship Mode
				Freight Terms
				VAN
				MILL
				Due Date
				09/30/2024
				Order Qty.
				Balance
				Received
				Cust. Part No.
				Description
				Footage
				Est. Weight
				Line Item Ref. No.
				Qty. / Pallet
				585
				45
				585 PC
				500544 - 5/8" x 49" x 97" PBD Raw
				Kronospan-Eastaboga PBD(95086)
				0.00 SF
				41,842.73 lbs
				Location:
				Shipping Notes:
				Total Footage:
				Total Estimated Weight:
				Carrier Name:
				Only the products that are identified as such on this document are FSC® certified.
				Unique Item Count:
				Total Qty.:
				1
				585 PC
				Carrier is to deliver to specified "Ship To" address only. If a change of delivery address is requested, please contact your
				MJB Wood Group representative.
				EPA TSCA Title VI & CARB 93120 Ph2 Compliant, for applicable products.
				Load must be 100% tarped - no exposed material. Load must be completely tarped before leaving the loading facility -
				do NOT untarp until instructed to do so by the receiver.
				0.00 SF
				41,842.73 lbs
				Total Pallets:
				13

	Quote request definition:
	- quote request: Explicitly requests pricing for shipping services
		Detailed Example Email:
			Subject: Need rate for Orlando to Miami shipment ASAP

					Hello,

					We are looking for a competitive rate for the following shipment:

					Origin: Orlando, FL
					Destination: Miami, FL
					Pickup Date: July 25, 2023
					Delivery Date: July 26, 2023
					Commodity: Electronics (palletized)
					Weight: 5,000 lbs
					Dimensions: 4 pallets, 48"x40"x50" each
					Special Requirements: Liftgate needed at delivery

					Can you please provide your best rate for this lane? We need to book this shipment by tomorrow.

					Thank you,
					Procurement Team



    Answer with ONLY "YES" if this is a new load or quote request document or "NO" if it is not.
    `

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			UserPrompt:      userPrompt,
		},
	)
	if err != nil {
		return false, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	return strings.Contains(strings.ToUpper(response.Content), "YES"), response.BraintrustLogID, nil
}
