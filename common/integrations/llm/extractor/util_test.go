package extractor

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

// Simple test struct with various field types
type TestStruct struct {
	StringField  string  `json:"stringField"`
	IntField     int     `json:"intField"`
	FloatField   float64 `json:"floatField"`
	Bool<PERSON>ield    bool    `json:"boolField"`
	NestedObject struct {
		SubField string `json:"subField"`
	} `json:"nestedObject"`
	ArrayField []string `json:"arrayField"`
}

// TestGenerateSchema tests the GenerateSchema function using the PickupInfo struct.
func TestGenerateSchema(t *testing.T) {
	// Generate schema for PickupInfo
	schema := GenerateSchema[PickupInfo]()

	// Schema should not be nil
	assert.NotNil(t, schema)

	// Convert schema to JSON for easier inspection
	schemaJSON, err := json.<PERSON>(schema)
	assert.NoError(t, err)

	// Unmarshal into a map to test specific fields
	var schemaMap map[string]any
	err = json.Unmarshal(schemaJSON, &schemaMap)
	assert.NoError(t, err)

	// Test schema type
	assert.Equal(t, "object", schemaMap["type"])

	// Verify properties exist
	properties, ok := schemaMap["properties"].(map[string]any)
	assert.True(t, ok, "Schema should have properties map")
	assert.NotEmpty(t, properties, "Properties should not be empty")

	// Check for pickup object in properties
	pickup, ok := properties["pickup"].(map[string]any)
	assert.True(t, ok, "Schema should have pickup property")

	// Test nested pickup properties
	pickupProperties, ok := pickup["properties"].(map[string]any)
	assert.True(t, ok, "Pickup object should have properties")

	// Verify some specific fields exist with correct types
	expectedFields := []string{"name", "addressLine1", "city", "state", "zipCode", "country", "email", "phone"}
	for _, field := range expectedFields {
		fieldObj, ok := pickupProperties[field].(map[string]any)
		assert.True(t, ok, fmt.Sprintf("Field %s should exist", field))
		assert.Equal(t, "string", fieldObj["type"], fmt.Sprintf("Field %s should be type string", field))
	}

	schemaJSON, err = json.MarshalIndent(schema, "", "  ")
	assert.NoError(t, err)
	t.Logf("Schema: %s", schemaJSON)
}

// TestExtractDomain tests the extractDomain function
func TestExtractDomain(t *testing.T) {
	tests := []struct {
		email    string
		expected string
	}{
		{"<EMAIL>", "example.com"},
		{"<EMAIL>", "company.co.uk"},
		{"noatsign", ""},
		{"@leadingat", "leadingat"},
		{"", ""},
	}

	for _, test := range tests {
		t.Run(test.email, func(t *testing.T) {
			result := extractDomain(test.email)
			assert.Equal(t, test.expected, result)
		})
	}
}

// TestExtractSecondLevelDomain tests the ExtractSecondLevelDomain function
func TestExtractSecondLevelDomain(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"example.com", "example"},
		{"<EMAIL>", "example"},
		{"sub.example.com", "example"},
		{"@example.com", "example"},
		{"example.co.uk", "example"},
		{"sub.example.co.uk", "example"},
		{"app.company.org", "company"},
		{"com", ""},
		{"", ""},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := ExtractSecondLevelDomain(test.input)
			assert.Equal(t, test.expected, result)
		})
	}
}

// TestIsInsufficientAddressData tests the IsInsufficientAddressData function
func TestIsInsufficientAddressData(t *testing.T) {
	tests := []struct {
		name     string
		addr     models.CompanyCoreInfo
		expected bool
	}{
		{
			name:     "Complete address with zipcode",
			addr:     models.CompanyCoreInfo{City: "Austin", State: "TX", Zipcode: "78701"},
			expected: false,
		},
		{
			name:     "No zipcode but has city and state",
			addr:     models.CompanyCoreInfo{City: "Austin", State: "TX"},
			expected: false,
		},
		{
			name:     "Has zipcode only",
			addr:     models.CompanyCoreInfo{Zipcode: "78701"},
			expected: false,
		},
		{
			name:     "Missing state",
			addr:     models.CompanyCoreInfo{City: "Austin"},
			expected: true,
		},
		{
			name:     "Missing city",
			addr:     models.CompanyCoreInfo{State: "TX"},
			expected: true,
		},
		{
			name:     "Empty address",
			addr:     models.CompanyCoreInfo{},
			expected: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := IsInsufficientAddressData(test.addr)
			assert.Equal(t, test.expected, result)
		})
	}
}

// TestStructExtractor tests the structExtractor function
func TestStructExtractor(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    TestStruct
		expectError bool
	}{
		{
			name: "Valid JSON",
			input: `{
				"stringField": "test",
				"intField": 42,
				"floatField": 3.14,
				"boolField": true,
				"nestedObject": {
					"subField": "nested"
				},
				"arrayField": ["one", "two", "three"]
			}`,
			expected: TestStruct{
				StringField: "test",
				IntField:    42,
				FloatField:  3.14,
				BoolField:   true,
				NestedObject: struct {
					SubField string `json:"subField"`
				}{
					SubField: "nested",
				},
				ArrayField: []string{"one", "two", "three"},
			},
			expectError: false,
		},
		{
			name: "JSON in markdown",
			input: "```json\n" + `{
				"stringField": "test",
				"intField": 42,
				"floatField": 3.14,
				"boolField": true,
				"nestedObject": {
					"subField": "nested"
				},
				"arrayField": ["one", "two", "three"]
			}` + "\n```",
			expected: TestStruct{
				StringField: "test",
				IntField:    42,
				FloatField:  3.14,
				BoolField:   true,
				NestedObject: struct {
					SubField string `json:"subField"`
				}{
					SubField: "nested",
				},
				ArrayField: []string{"one", "two", "three"},
			},
			expectError: false,
		},
		{
			name:        "Invalid JSON",
			input:       `{"stringField": "missing closing brace"`,
			expectError: true,
		},
		{
			name:        "Not JSON at all",
			input:       "This is just plain text",
			expectError: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := StructExtractor[TestStruct](test.input)

			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expected.StringField, result.StringField)
				assert.Equal(t, test.expected.IntField, result.IntField)
				assert.Equal(t, test.expected.FloatField, result.FloatField)
				assert.Equal(t, test.expected.BoolField, result.BoolField)
				assert.Equal(t, test.expected.NestedObject.SubField, result.NestedObject.SubField)
				assert.Equal(t, test.expected.ArrayField, result.ArrayField)
			}
		})
	}
}

// TestValidateTransportType tests the validateTransportType function
func TestValidateTransportType(t *testing.T) {
	tests := []struct {
		name           string
		input          models.TransportType
		mockResponse   string
		userPrompt     string
		config         models.QuickQuoteConfig
		expectedOutput models.TransportType
	}{
		{
			name:           "Empty input defaults to VAN",
			input:          "",
			mockResponse:   "NO",
			userPrompt:     "Some user prompt",
			config:         models.QuickQuoteConfig{},
			expectedOutput: models.VanTransportType,
		},
		{
			name:           "Valid input type (case insensitive)",
			input:          "flatbed",
			mockResponse:   "NO",
			userPrompt:     "Some user prompt",
			config:         models.QuickQuoteConfig{},
			expectedOutput: models.FlatbedTransportType,
		},
		{
			name:           "Unknown type defaults to VAN",
			input:          "UNKNOWN_TYPE",
			mockResponse:   "NO",
			userPrompt:     "Some user prompt",
			config:         models.QuickQuoteConfig{},
			expectedOutput: models.VanTransportType,
		},
		{
			name:           "Partial match accepted",
			input:          "BOX",
			mockResponse:   "NO",
			userPrompt:     "Some user prompt",
			config:         models.QuickQuoteConfig{},
			expectedOutput: models.BoxTruckTransportType,
		},
		{
			name:           "Hot shot with space is normalized",
			input:          "HOT SHOT",
			mockResponse:   "NO",
			userPrompt:     "Some user prompt",
			config:         models.QuickQuoteConfig{},
			expectedOutput: "HOTSHOT",
		},
		{
			name:         "Special equipment in content returns SPECIAL",
			input:        "VAN",
			mockResponse: "YES",
			userPrompt:   "Need a refrigerated truck",
			config: models.QuickQuoteConfig{
				SpecialEquipment: []string{"refrigerated"},
			},
			expectedOutput: models.SpecialTransportType,
		},
		{
			name:         "Custom transport type from config",
			input:        "CUSTOM_TYPE",
			mockResponse: "NO",
			userPrompt:   "Some user prompt",
			config: models.QuickQuoteConfig{
				OtherTransportTypes: []string{"CUSTOM_TYPE"},
			},
			expectedOutput: "CUSTOM_TYPE",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Skip("Skipping until proper mocking is implemented")
			// These tests would require proper mocking of the OpenAI client
			// which would need access to the actual OpenAI SDK implementation
		})
	}
}

// TestMapCustomer tests the MapCustomer function
func TestMapCustomer(t *testing.T) {
	t.Skip("Skipping test that requires DB and OpenAI client mocking")
	/*
		This test requires:
		1. Mocking the OpenAI client for checkForStringInUserPrompt
		2. Mocking database access to tmsCustomerDB.GetCustomerByName

		A proper test would check various scenarios:
		- Mapping standard customer names
		- Handling edge case customers (Trident, FetchFreight)
		- Error conditions (DB errors, no name found)
		- TMS ID validation

		Example structure:

		ctx := context.Background()

		tests := []struct {
			name           string
			tmsID          uint
			parsedName     string
			userPrompt     string
			mockLLMResponse string
			mockDBCustomer  *models.TMSCustomer
			mockDBError     error
			expectedCustomer *models.TMSCustomer
			expectError     bool
		}{
			{
				name:           "Standard customer mapping",
				tmsID:          123,
				parsedName:     "ABC Shipping",
				userPrompt:     "Email from ABC Shipping",
				mockLLMResponse: "NO", // Not a special case
				mockDBCustomer: &models.TMSCustomer{
					ID:   456,
					Name: "ABC Shipping",
				},
				mockDBError:     nil,
				expectedCustomer: &models.TMSCustomer{
					ID:   456,
					Name: "ABC Shipping",
				},
				expectError: false,
			},
			{
				name:           "Edge case - MJB to Liberty Woods",
				tmsID:          123,
				parsedName:     "Some Other Name", // Will be overridden
				userPrompt:     "Email from MJB Shipping",
				mockLLMResponse: "YES", // LLM finds MJB in the content
				mockDBCustomer: &models.TMSCustomer{
					ID:   789,
					Name: "Liberty Woods International",
				},
				mockDBError:     nil,
				expectedCustomer: &models.TMSCustomer{
					ID:   789,
					Name: "Liberty Woods International",
				},
				expectError: false,
			},
			// Additional test cases would go here
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				// Would need to set up mocks here
				// mockClient := &MockClient{response: test.mockLLMResponse}
				// mockDBFunc := func(ctx context.Context, tmsID uint, name string) (models.TMSCustomer, error) {
				//    return *test.mockDBCustomer, test.mockDBError
				// }

				// Inject mocks and call function
				// result, err := MapCustomer(ctx, mockClient, test.tmsID, test.parsedName, test.userPrompt)

				// Verify results
				// if test.expectError {
				//     assert.Error(t, err)
				// } else {
				//     assert.NoError(t, err)
				//     assert.Equal(t, test.expectedCustomer, result)
				// }
			})
		}
	*/
}

// TestS3PDFToMarkdown tests the S3PDFToMarkdown function
func TestS3PDFToMarkdown(t *testing.T) {
	t.Skip("Skipping test that requires S3 mocking")
	/*
		This test requires:
		1. Mocking the s3fetcher.New function
		2. Mocking the GetAttachment method
		3. Mocking the fitz.NewFromMemory function

		Example structure:

		ctx := context.Background()

		tests := []struct {
			name          string
			attachmentURL string
			mockPDFData   []byte
			mockS3Error   error
			expectedMD    string
			expectError   bool
		}{
			{
				name:          "Valid PDF conversion",
				attachmentURL: "https://example.com/s3/object/mybucket?prefix=folder/document.pdf",
				mockPDFData:   []byte("mock PDF data"),
				mockS3Error:   nil,
				expectedMD:    "# Document Title\n\nSome content",
				expectError:   false,
			},
			{
				name:          "Invalid S3 URL",
				attachmentURL: "invalid-url",
				mockPDFData:   nil,
				mockS3Error:   nil,
				expectedMD:    "",
				expectError:   true,
			},
			{
				name:          "S3 fetch error",
				attachmentURL: "https://example.com/s3/object/mybucket?prefix=folder/document.pdf",
				mockPDFData:   nil,
				mockS3Error:   fmt.Errorf("s3 error"),
				expectedMD:    "",
				expectError:   true,
			},
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				// Would need complex mocking for S3 and PDF library
				// result, err := S3PDFToMarkdown(ctx, test.attachmentURL)

				// if test.expectError {
				//     assert.Error(t, err)
				// } else {
				//     assert.NoError(t, err)
				//     assert.Contains(t, result, test.expectedMD)
				// }
			})
		}
	*/
}

// TestExtractJSONFromText tests the ExtractJSONFromText function and its helpers
//
//nolint:lll
func TestExtractJSONFromText(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
		wantErr  bool
	}{
		{
			name: "JSON in markdown code block with json tag",
			input: "Here's the JSON:\n\n" +
				"```json\n" +
				`{
` +
				`  "name": "John Doe",
` +
				`  "age": 30,
` +
				`  "address": {
` +
				`    "city": "New York",
` +
				`    "state": "NY"
` +
				`  }
` +
				`}
` +
				"```" +
				"\n\nHope this helps!",
			expected: `{
` +
				`  "name": "John Doe",
` +
				`  "age": 30,
` +
				`  "address": {
` +
				`    "city": "New York",
` +
				`    "state": "NY"
` +
				`  }
` +
				`}`,
			wantErr: false,
		},
		{
			name: "JSON in generic markdown code block",
			input: "Here's the JSON:\n\n" +
				"```\n" +
				`{
` +
				`  "name": "John Doe",
` +
				`  "age": 30
` +
				`}
` +
				"```" +
				"\n\nHope this helps!",
			expected: `{
` +
				`  "name": "John Doe",
` +
				`  "age": 30
` +
				`}`,
			wantErr: false,
		},
		{
			name:     "Plain JSON object",
			input:    `{"name": "John Doe", "age": 30, "address": {"city": "New York", "state": "NY"}}`,
			expected: `{"name": "John Doe", "age": 30, "address": {"city": "New York", "state": "NY"}}`,
			wantErr:  false,
		},
		{
			name:     "JSON array",
			input:    `[{"name": "John"}, {"name": "Jane"}, {"name": "Bob"}]`,
			expected: `[{"name": "John"}, {"name": "Jane"}, {"name": "Bob"}]`,
			wantErr:  false,
		},
		{
			name: "JSON embedded in text",
			input: "Based on your request, here's the information:\n\n" +
				`{"name": "John Doe", "age": 30}` +
				"\n\nLet me know if you need anything else.",
			expected: `{"name": "John Doe", "age": 30}`,
			wantErr:  false,
		},
		{
			name:     "JSON with escaped quotes",
			input:    `{"text": "This has \"quoted\" words", "name": "John"}`,
			expected: `{"text": "This has \"quoted\" words", "name": "John"}`,
			wantErr:  false,
		},
		{
			name:     "Nested complex JSON",
			input:    `{"users": [{"name": "John", "permissions": {"admin": true}}, {"name": "Jane", "permissions": {"admin": false}}], "metadata": {"version": 1.0}}`,
			expected: `{"users": [{"name": "John", "permissions": {"admin": true}}, {"name": "Jane", "permissions": {"admin": false}}], "metadata": {"version": 1.0}}`,
			wantErr:  false,
		},
		{
			name:     "Empty object",
			input:    `{}`,
			expected: `{}`,
			wantErr:  false,
		},
		{
			name:     "Empty array",
			input:    `[]`,
			expected: `[]`,
			wantErr:  false,
		},
		{
			name:     "JSON with unclosed brackets in string value",
			input:    `{"description": "This string contains a { character", "valid": true}`,
			expected: `{"description": "This string contains a { character", "valid": true}`,
			wantErr:  false,
		},
		{
			name: "Claude explanatory response with JSON object",
			input: "I've analyzed your data and created the requested JSON structure:\n\n" +
				`{
` +
				`  "analysis": {
` +
				`    "totalItems": 42,
` +
				`    "categories": [
` +
				`      {"name": "Category A", "count": 15},
` +
				`      {"name": "Category B", "count": 27}
` +
				`    ],
` +
				`    "status": "complete"
` +
				`  }
` +
				`}
` +
				"\nThe analysis shows that Category B has the highest count. Let me know if you need further details.",
			expected: `{
` +
				`  "analysis": {
` +
				`    "totalItems": 42,
` +
				`    "categories": [
` +
				`      {"name": "Category A", "count": 15},
` +
				`      {"name": "Category B", "count": 27}
` +
				`    ],
` +
				`    "status": "complete"
` +
				`  }
` +
				`}`,
			wantErr: false,
		},
		{
			name: "Claude explanatory response with JSON array",
			input: "Okay, here is the list of items you requested as a JSON array:\n\n" +
				`[
` +
				`  {"id": 1, "item": "Apple"},
` +
				`  {"id": 2, "item": "Banana"}
` +
				`]
` +
				"\nThis includes the ID and item name. ",
			expected: `[
` +
				`  {"id": 1, "item": "Apple"},
` +
				`  {"id": 2, "item": "Banana"}
` +
				`]`,
			wantErr: false,
		},
		{
			name:     "Invalid JSON - unclosed brackets",
			input:    `{"name": "John", "address": {"city": "New York"`,
			expected: ``,
			wantErr:  true,
		},
		{
			name:     "Invalid JSON - missing quotes",
			input:    `{name: "John"}`,
			expected: ``,
			wantErr:  true,
		},
		{
			name:     "No JSON in text",
			input:    "This text doesn't contain any JSON object or array.",
			expected: ``,
			wantErr:  true,
		},
		{
			name: "JSON with code comments",
			input: "Here's the JSON with comments that Claude might generate:\n\n" +
				`{
` +
				`  // User information
` +
				`  "name": "John Doe",
` +
				`  "age": 30,
` +
				`  /* 
` +
				`   * Address details
` +
				`   */
` +
				`  "address": {
` +
				`    "city": "New York",
` +
				`    "state": "NY"
` +
				`  }
` +
				`}`,
			expected: ``, // Standard JSON doesn't support comments, extraction might work but validation fails
			wantErr:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Use require for checking errors before proceeding
			require := require.New(t)
			// Use assert for checks that don't necessarily stop the test
			assert := assert.New(t)

			result, err := ExtractJSONFromText(tc.input)

			if tc.wantErr {
				assert.Error(err)
			} else {
				// Use require here to stop test if error occurs unexpectedly
				require.NoError(err)
				assert.JSONEq(tc.expected, result)
			}
		})
	}
}

// TestCheckForStringInUserPrompt tests the checkForStringInUserPrompt function
func TestCheckForStringInUserPrompt(t *testing.T) {
	tests := []struct {
		name       string
		searchStr  string
		userPrompt string
		expected   bool
	}{
		// Markdown tests
		{
			name:      "Find word in markdown",
			searchStr: "customer",
			userPrompt: `# Customer Order Details
			
			Here is information about the **customer** order:
			
			* Order ID: 12345
			* Products: Widget, Gadget
			* Status: Shipped`,
			expected: true,
		},
		{
			name:      "Word not in markdown",
			searchStr: "invoice",
			userPrompt: `# Customer Order Details
			
			Here is information about the order:
			
			* Order ID: 12345
			* Products: Widget, Gadget
			* Status: Shipped`,
			expected: false,
		},
		{
			name:      "Case-insensitive match in markdown",
			searchStr: "Customer",
			userPrompt: `# CUSTOMER Order Details
			
			Here is information about the customer order:`,
			expected: true,
		},

		// Email chain tests
		{
			name:      "Find word in email chain",
			searchStr: "shipment",
			userPrompt: `---------- Forwarded message ---------
			From: Jane Doe <<EMAIL>>
			Date: Mon, Jun 10, 2023 at 2:14 PM
			Subject: Re: Shipment Status
			To: John Smith <<EMAIL>>
			
			The shipment has been delayed due to weather conditions.
			
			On Mon, Jun 10, 2023 at 1:30 PM, John Smith <<EMAIL>> wrote:
			> Can you provide an update on the shipment status?
			>
			> Thanks,
			> John`,
			expected: true,
		},
		{
			name:      "Word not in email chain",
			searchStr: "refund",
			userPrompt: `---------- Forwarded message ---------
			From: Jane Doe <<EMAIL>>
			Date: Mon, Jun 10, 2023 at 2:14 PM
			Subject: Re: Shipment Status
			To: John Smith <<EMAIL>>
			
			The shipment has been delayed due to weather conditions.`,
			expected: false,
		},
		{
			name:      "Partial word should not match in email",
			searchStr: "ship",
			userPrompt: `Subject: Shipment Status
			
			The shipment will arrive tomorrow.`,
			expected: false, // Won't match due to word boundary \b in regex
		},

		// JSON data tests
		{
			name:      "Find word in JSON data",
			searchStr: "ARGO",
			userPrompt: `{
				"customer": "ARGO Fine Imports",
				"orderId": "ORD-12345",
				"items": [
					{"name": "Furniture", "quantity": 5},
					{"name": "Decorations", "quantity": 10}
				]
			}`,
			expected: true,
		},
		{
			name:      "Word not in JSON data",
			searchStr: "MJB",
			userPrompt: `{
				"customer": "ARGO Fine Imports",
				"orderId": "ORD-12345",
				"items": [
					{"name": "Furniture", "quantity": 5},
					{"name": "Decorations", "quantity": 10}
				]
			}`,
			expected: false,
		},

		// Special cases
		{
			name:       "Find word with special characters",
			searchStr:  "SES, LLC",
			userPrompt: "We received an order from SES, LLC yesterday.",
			expected:   true,
		},
		{
			name:       "Word as part of another word should not match",
			searchStr:  "SES",
			userPrompt: "The process is assessed by our team.",
			expected:   false, // Won't match due to word boundary \b in regex
		},
		{
			name:       "Empty search string",
			searchStr:  "",
			userPrompt: "Some text here",
			expected:   false,
		},
		{
			name:       "Empty user prompt",
			searchStr:  "test",
			userPrompt: "",
			expected:   false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := checkForStringInUserPrompt(test.searchStr, test.userPrompt)
			assert.Equal(t, test.expected, result, "Expected %v but got %v for search string '%s' in user prompt",
				test.expected, result, test.searchStr)
		})
	}
}

func TestExtractFromCodeBlock(t *testing.T) {
	assert := assert.New(t)
	testCases := []struct {
		name     string
		input    string
		expected string
		found    bool
	}{
		{
			name:     "JSON code block with json tag",
			input:    "```json\n{\"name\": \"John\"}\n```",
			expected: "{\"name\": \"John\"}",
			found:    true,
		},
		{
			name:     "Generic code block",
			input:    "```\n{\"name\": \"John\"}\n```",
			expected: "{\"name\": \"John\"}",
			found:    true,
		},
		{
			name:     "Not a code block",
			input:    "{\"name\": \"John\"}",
			expected: "",
			found:    false,
		},
		{
			name:     "Markdown code block with extra content",
			input:    "Here's JSON:\n```json\n{\"name\": \"John\"}\n```\nMore text",
			expected: "",
			found:    false, // Fails because check is strict starts/ends with
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(_ *testing.T) {
			result, found := extractFromCodeBlock(tc.input)
			assert.Equal(tc.found, found)
			if found {
				assert.Equal(tc.expected, result)
			}
		})
	}
}

func TestFindJSONStart(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	testCases := []struct {
		name      string
		input     string
		startPos  int
		startChar byte
		endChar   byte
		wantErr   bool
	}{
		{
			name:      "JSON object",
			input:     `{"name": "John"}`,
			startPos:  0,
			startChar: '{',
			endChar:   '}',
			wantErr:   false,
		},
		{
			name:      "JSON array",
			input:     `[1, 2, 3]`,
			startPos:  0,
			startChar: '[',
			endChar:   ']',
			wantErr:   false,
		},
		{
			name:      "JSON object in text",
			input:     `Here is JSON: {"name": "John"}`,
			startPos:  14, // Adjusted index
			startChar: '{',
			endChar:   '}',
			wantErr:   false,
		},
		{
			name:      "No JSON",
			input:     "Plain text",
			startPos:  -1,
			startChar: 0,
			endChar:   0,
			wantErr:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(_ *testing.T) {
			startPos, startChar, endChar, err := findJSONStart(tc.input)

			if tc.wantErr {
				assert.Error(err)
			} else {
				require.NoError(err)
				assert.Equal(tc.startPos, startPos)
				assert.Equal(tc.startChar, startChar)
				assert.Equal(tc.endChar, endChar)
			}
		})
	}
}

func TestExtractJSONContent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	testCases := []struct {
		name      string
		input     string
		startPos  int
		startChar byte
		endChar   byte
		expected  string
		wantErr   bool
	}{
		{
			name:      "Simple JSON object",
			input:     `{"name": "John"}`,
			startPos:  0,
			startChar: '{',
			endChar:   '}',
			expected:  `{"name": "John"}`,
			wantErr:   false,
		},
		{
			name:      "Nested JSON object",
			input:     `{"user": {"name": "John"}}`,
			startPos:  0,
			startChar: '{',
			endChar:   '}',
			expected:  `{"user": {"name": "John"}}`,
			wantErr:   false,
		},
		{
			name: "JSON with escaped quotes",
			// Use raw string literal for input containing escaped quotes
			input:     `{"text": "Hello \"world\""}`,
			startPos:  0,
			startChar: '{',
			endChar:   '}',
			expected:  `{"text": "Hello \"world\""}`,
			wantErr:   false,
		},
		{
			name:      "Unclosed JSON",
			input:     `{"name": "John"`,
			startPos:  0,
			startChar: '{',
			endChar:   '}',
			expected:  "",
			wantErr:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(_ *testing.T) {
			result, err := extractJSONContent(tc.input, tc.startPos, tc.startChar, tc.endChar)

			if tc.wantErr {
				assert.Error(err)
			} else {
				require.NoError(err)
				assert.Equal(tc.expected, result)
			}
		})
	}
}

func TestValidateJSON(t *testing.T) {
	assert := assert.New(t)
	testCases := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "Valid JSON",
			input:   `{"name": "John"}`,
			wantErr: false,
		},
		{
			name:    "Invalid JSON - missing quotes",
			input:   `{name: "John"}`,
			wantErr: true,
		},
		{
			name:    "Invalid JSON - trailing comma",
			input:   `{"name": "John",}`,
			wantErr: true,
		},
		{
			name:    "Valid empty object",
			input:   `{}`,
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(_ *testing.T) {
			err := validateJSON(tc.input)

			if tc.wantErr {
				assert.Error(err)
			} else {
				assert.NoError(err)
			}
		})
	}
}
