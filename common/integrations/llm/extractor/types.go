package extractor

import "github.com/drumkitai/drumkit/common/models"

// These are the structs we use to generate the JSON schema for the LLM and unmarshal the data returned from the LLM.
//
//nolint:lll
type (
	BasicInfo struct {
		MoreThanTwoStops bool   `json:"moreThanTwoStops" jsonschema_description:"Whether the shipment has more than two stops"`
		RefNumber        string `json:"refNumber" jsonschema_description:"Extract the shipping order number or the release number. Never extract a warehouse code as the refNumber. If both a PO # and a order/release/shipping order # are provided, this field should be the order/release number, not the PO #. Never include additional text here, only extract the ref number itself."`
		PONums           string `json:"poNums" jsonschema_description:"Extract the PO # if provided. If both a PO # and a order/release/shipping order # are provided, this field should be the PO #."`
	}
	CustomerInfo struct {
		ShipperName         string `json:"shipper_name" jsonschema_description:"Look for the shipper (the company requesting quotes/a new load tender) in the email body (including signature). If not found in the email body, look for it in the attachment data, if available. This should be a human readable company name such as 'LMNOP Goods', NOT an email domain. Note that the shipper is not necessarily the same as the pickup or dropoff warehouse names. Do NOT include suffixes such as LLC, INC, and CO in your response."`
		OriginalSenderEmail string `json:"original_sender_email" jsonschema_description:"If the email is a forwarding chain, then look for the original external sender's email address in the body. By external sender, we mean the first email address that does not have the same domain as the recipient"`
	}

	// These are the structs we use to generate the JSON schema for the LLM.
	SpecificationsSchema struct {
		Specifications Specifications `json:"specifications" jsonschema_description:"Specifications of the shipment"`
	}
	ConsigneeSchema struct {
		Consignee Consignee `json:"consignee" jsonschema_description:"Consignee information"`
	}
	PickupSchema struct {
		Pickup Pickup `json:"pickup" jsonschema_description:"Pickup information"`
	}
	RateDataSchema struct {
		RateData RateData `json:"rateData" jsonschema_description:"Rate data for the shipment"`
	}

	// These are the structs we use to unmarshal the data returned from the LLM.
	SpecificationsInfo struct {
		Specifications models.SuggestedSpecifications `json:"specifications"`
	}
	ConsigneeInfo struct {
		Consignee models.Consignee `json:"consignee"`
	}
	PickupInfo struct {
		Pickup models.Pickup `json:"pickup"`
	}
	RateDataInfo struct {
		RateData models.RateData `json:"rateData"`
	}
)

//nolint:lll
type (
	Specifications struct {
		ServiceType         string           `json:"serviceType" jsonschema_description:"Service type if specified"`
		TransportType       string           `json:"transportType" jsonschema_description:"One of 'VAN', 'REEFER', 'FLATBED', 'BOX TRUCK', or 'HOTSHOT' (default to 'VAN' if not specified)"`
		TransportSize       string           `json:"transportSize" jsonschema_description:"Vehicle size (e.g., '53 ft')"`
		TotalInPalletCount  float64          `json:"totalInPalletCount" jsonschema_description:"Number of pallets/crates"`
		TotalOutPalletCount float64          `json:"totalOutPalletCount" jsonschema_description:"Number of pallets/crates"`
		TotalPieces         models.ValueUnit `json:"totalPieces" jsonschema_description:"Total number of pieces"`
		Commodities         string           `json:"commodities" jsonschema_description:"Description of goods being shipped"`
		NumCommodities      int              `json:"numCommodities" jsonschema_description:"Number of commodities"`
		TotalWeight         models.ValueUnit `json:"totalWeight" jsonschema_description:"Object with 'val' (number) and 'unit' (string, usually 'lbs')"`
		NetWeight           models.ValueUnit `json:"netWeight" jsonschema_description:"Object with 'val' (number) and 'unit' (string, usually 'lbs')"`
		BillableWeight      models.ValueUnit `json:"billableWeight" jsonschema_description:"Object with 'val' (number) and 'unit' (string, usually 'lbs')"`
		TotalDistance       models.ValueUnit `json:"totalDistance" jsonschema_description:"Object with 'val' (number) and 'unit' (string, usually 'miles')"`
		MinTempFahrenheit   float64          `json:"minTempFahrenheit" jsonschema_description:"Minimum temperature (for refrigerated loads)"`
		MaxTempFahrenheit   float64          `json:"maxTempFahrenheit" jsonschema_description:"Maximum temperature (for refrigerated loads)"`
	}

	Consignee struct {
		models.CompanyCoreInfo
		ExternalTMSStopID string `json:"externalTMSStopID" jsonschema_description:"Should be set to an empty string."`
		BusinessHours     string `json:"businessHours" jsonschema_description:"Business hours if specified"`
		RefNumber         string `json:"refNumber" jsonschema_description:"Reference number for this location"`
		MustDeliver       string `json:"mustDeliver" jsonschema_description:"Latest date/time shipment must be delivered (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"` // Date that delivery has to happen by
		ApptRequired      bool   `json:"apptRequired" jsonschema_description:"Default to false."`
		ApptType          string `json:"apptType" jsonschema_description:"Appointment type: appointment, FCFS (first-come, first-serve), drop trailer, etc"` // e.g. appointment, FCFS (first-come, first-serve), drop trailer
		ApptStartTime     string `json:"apptStartTime" jsonschema_description:"Appointment start time (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"`
		ApptEndTime       string `json:"apptEndTime" jsonschema_description:"Appointment end time (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"` // Optional, TMS may not provide this
		ApptNote          string `json:"apptNote" jsonschema_description:"Notes about the appointment"`
		Timezone          string `json:"timezone" jsonschema_description:"Timezone code (e.g. 'EDT', 'CST')"` // IANA Timezone (e.g. "America/New_York")

	}

	Pickup struct {
		models.CompanyCoreInfo
		ExternalTMSStopID string `json:"externalTMSStopID" jsonschema_description:"Should be set to an empty string."`
		BusinessHours     string `json:"businessHours" jsonschema_description:"Business hours if specified"`
		RefNumber         string `json:"refNumber" jsonschema_description:"Reference number for this location"`
		ReadyTime         string `json:"readyTime" jsonschema_description:"When shipment is ready for pickup (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"`
		ApptRequired      bool   `json:"apptRequired" jsonschema_description:"Default to false."`
		ApptType          string `json:"apptType" jsonschema_description:"Appointment type: appointment, FCFS (first-come, first-serve), drop trailer, etc"`
		ApptStartTime     string `json:"apptStartTime" jsonschema_description:"Appointment start time (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"`
		ApptEndTime       string `json:"apptEndTime" jsonschema_description:"Appointment end time (format: 'mm/dd/yyyy, hh:MMam/pm' or just 'mm/dd/yyyy')"` // Optional, TMS may not provide this
		ApptNote          string `json:"apptNote" jsonschema_description:"Notes about the appointment"`
		Timezone          string `json:"timezone" jsonschema_description:"Timezone code (e.g. 'EDT', 'CST')"` // IANA Timezone (e.g. "America/New_York")

	}

	RateData struct {
		RevenueCode          string  `json:"revenueCode" jsonschema_description:"Should be set to an empty string."`
		CollectionMethod     string  `json:"collectionMethod" jsonschema_description:"Must be one of the following values: 'COD', 'Collect', 'Third-Party', or 'Prepaid'. If the collection method is not explicitly stated in the shipping order, default to 'Prepaid'."`
		CustomerRateType     string  `json:"customerRateType" jsonschema_description:"Must be one of the following values: 'Flat', 'Distance', 'CWT' (hundredweight), or 'Tons'. If the rate type is not explicitly stated, default to 'Flat'."`
		CustomerLineHaulRate float32 `json:"customerLineHaulRate" jsonschema_description:"Set this field to the specified rate if explicitly mentioned. If not, set it to null."`
	}
)
