package llm

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/models"
)

func TestStringsToTime(t *testing.T) {
	ctx := context.Background()
	t.Run("No Timezone", func(t *testing.T) {
		res := StringsToTime(ctx, "2023-08-10", "08:00", "")
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 10, 8, 0, 0, 0, time.UTC),
			Valid: true,
		}
		assert.Equal(t, expected, res)
	})

	t.Run("New York", func(t *testing.T) {
		res := StringsToTime(ctx, "2023-08-10", "08:00", "America/New_York")

		loc, err := time.LoadLocation("America/New_York")
		require.NoError(t, err)
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 10, 8, 0, 0, 0, loc),
			Valid: true,
		}
		assert.Equal(t, expected, res)
	})

	t.Run("New York", func(t *testing.T) {
		res := StringsToTime(ctx, "2023-08-10", "08:00", "America/Los_Angeles")

		loc, err := time.LoadLocation("America/Los_Angeles")
		require.NoError(t, err)
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 10, 8, 0, 0, 0, loc),
			Valid: true,
		}
		assert.Equal(t, expected, res)
	})
}

func TestExtractJSONFromText(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    string
		wantErr bool
	}{
		{
			name:    "empty string",
			input:   "",
			want:    "",
			wantErr: true,
		},
		{
			name:    "text with no JSON",
			input:   "Hello world! This is just plain text.",
			want:    "",
			wantErr: true,
		},
		{
			name:    "text with single JSON object",
			input:   "Some text before {\"name\": \"John\", \"age\": 30} and text after",
			want:    "{\"name\": \"John\", \"age\": 30}",
			wantErr: false,
		},
		{
			name: "text with JSON array",
			//nolint:lll
			input: `Begin text {"quote_requests": [{"pickup_location": {"city": "New Orleans","state": "LA","zip": "12345"},"pickup_date": "11/13/2024, 08:00AM","dropoff_location": {"city": "Decatur","state": "AL","zip": "12345"},"dropoff_date": "11/14/2024, 09:00AM","truck_type": "VAN"}]} end text`,
			//nolint:lll
			want:    `{"quote_requests": [{"pickup_location": {"city": "New Orleans","state": "LA","zip": "12345"},"pickup_date": "11/13/2024, 08:00AM","dropoff_location": {"city": "Decatur","state": "AL","zip": "12345"},"dropoff_date": "11/14/2024, 09:00AM","truck_type": "VAN"}]}`,
			wantErr: false,
		},
		{
			name:    "invalid JSON - unclosed brackets",
			input:   "Text {\"name\": \"John\", \"nested\": {\"key\": \"value\"",
			want:    "",
			wantErr: true,
		},
		{
			name:    "nested JSON objects",
			input:   "Start {\"user\": {\"name\": \"John\", \"address\": {\"city\": \"NYC\"}}} End",
			want:    "{\"user\": {\"name\": \"John\", \"address\": {\"city\": \"NYC\"}}}",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := extractor.ExtractJSONFromText(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("extractJSONFromText() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("extractJSONFromText() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildTurvoSpecifications(t *testing.T) {
	t.Run("valid service and transport types", func(t *testing.T) {
		input := models.SuggestedSpecifications{
			ServiceType:   "Standard",
			TransportType: "REEFER",
		}

		result := turvo.BuildTurvoSpecifications(input)

		assert.Equal(t, "Standard", result.ServiceType)
		assert.Equal(t, "REEFER", result.TransportType)
	})

	t.Run("invalid service and transport types default to Any and VAN", func(t *testing.T) {
		input := models.SuggestedSpecifications{
			ServiceType:   "Invalid",
			TransportType: "Invalid",
		}

		result := turvo.BuildTurvoSpecifications(input)

		assert.Equal(t, "Any", result.ServiceType)
		assert.Equal(t, "VAN", result.TransportType)
	})

	t.Run("zero net weight uses total weight", func(t *testing.T) {
		input := models.SuggestedSpecifications{
			NetWeight: models.ValueUnit{
				Val:  0,
				Unit: "invalid",
			},
			TotalWeight: models.ValueUnit{
				Val:  1000,
				Unit: "lbs",
			},
		}

		result := turvo.BuildTurvoSpecifications(input)

		assert.Equal(t, float32(1000), result.NetWeight.Val)
		assert.Equal(t, "lb", result.NetWeight.Unit)
	})

	t.Run("zero total weight uses net weight", func(t *testing.T) {
		input := models.SuggestedSpecifications{
			NetWeight: models.ValueUnit{
				Val:  2000,
				Unit: "LBS",
			},
			TotalWeight: models.ValueUnit{
				Val:  0,
				Unit: "invalid",
			},
		}

		result := turvo.BuildTurvoSpecifications(input)

		assert.Equal(t, float32(2000), result.TotalWeight.Val)
		assert.Equal(t, "lb", result.TotalWeight.Unit)
	})
}

func TestExtractSecondLevelDomain(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"<EMAIL>", "foo"},
		{"foo.com", "foo"},
		{"@foo.com", "foo"},
		{"<EMAIL>", "example"},
		{"example.co.uk", "example"},
		{"@example.co.uk", "example"},
		{"<EMAIL>", "domain"},
		{"sub.domain.com", "domain"},
		{"@sub.domain.com", "domain"},
		{"@sub.domain.co.uk", "domain"},
		{"<EMAIL>", "domain"},
		{"someone@localhost", ""},
		{"localhost", ""},
		{"@localhost", ""},
		{"invalid-email", ""},
		{"user@", ""},
		{"@", ""},
		{"", ""},
		// Unsupported case
		{"<EMAIL>", "somesite"},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := extractor.ExtractSecondLevelDomain(test.input)
			assert.Equal(t, test.expected, result,
				"extractSecondLevelDomain(%q) = %q, want %q", test.input, result, test.expected)
		})
	}
}

func TestValidateTransportType(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name     string
		input    models.TransportType
		expected models.TransportType
	}{
		{"Valid VAN", "VAN", models.VanTransportType},
		{"Valid FLATBED", "FLATBED", models.FlatbedTransportType},
		{"Valid REEFER", "REEFER", models.ReeferTransportType},
		{"Valid Transport Type - HOTSHOT mapping to HOTSHOT", "HOTSHOT", models.HotShotTransportType},
		{"Valid Transport Type - BOX TRUCK mapping to BOX TRUCK", "BOX TRUCK", models.BoxTruckTransportType},
		{"FLATBED HOTSHOT should map to HOTSHOT", "HOTSHOT", models.HotShotTransportType},
		{"Valid BOX should match BOX TRUCK", "BOX", models.BoxTruckTransportType},
		{"Handles extra spaces", "  FLATBED  ", models.FlatbedTransportType},
		{"Handles case insensitivity", "reefer", models.ReeferTransportType},
		{"Handles spaced input 'HOT  SHOT'", "HOT    SHOT", models.HotShotTransportType},
		{"Handles spaced input 'BOX  TRUCK'", "BOX    TRUCK", models.BoxTruckTransportType},
		{"Garbage Input Defaults to VAN", "RANDOMSTRING", models.VanTransportType},
		{"Handles missing input", "", models.VanTransportType},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateTransportType(ctx, models.Email{}, nil, tt.input, models.QuickQuoteConfig{})
			assert.Equal(t, tt.expected, result)
		})
	}

	// Adds valid transport types
	config := models.QuickQuoteConfig{
		OtherTransportTypes: []string{"SPRINTER"},
		SpecialEquipment:    []string{"LIFTGATE"},
	}

	testsWithConfig := []struct {
		name     string
		input    models.TransportType
		expected models.TransportType
	}{
		{"With config - Valid VAN", "VAN", models.VanTransportType},
		{"With config - Valid FLATBED", "FLATBED", models.FlatbedTransportType},
		{"With config - Valid REEFER", "REEFER", models.ReeferTransportType},
		{"With config - Valid Other Transport Type", "HOTSHOT", models.TransportType("HOTSHOT")},
		{"With config - Valid Other Transport Type - Box Truck", "BOX TRUCK", models.TransportType("BOX TRUCK")},
		{"With config - Valid FLATBED HOTSHOT should match HOTSHOT", "HOTSHOT", models.TransportType("HOTSHOT")},
		{"With config - Invalid FLATBED should not match HOTSHOT", "FLATBED", models.FlatbedTransportType},
		{"With config - Invalid BOX should match BOX TRUCK", "BOX", models.TransportType("BOX TRUCK")},
		{"With config - Handles extra spaces", "  FLATBED  ", models.FlatbedTransportType},
		{"With config - Handles case insensitivity", "reefer", models.ReeferTransportType},
		{"With config - Handles spaced input 'HOT  SHOT'", "HOT    SHOT", models.TransportType("HOTSHOT")},
		{"With config - Handles spaced input 'BOX  TRUCK'", "BOX    TRUCK", models.TransportType("BOX TRUCK")},
		{"With config - Garbage Input Defaults to VAN", "RANDOMSTRING", models.VanTransportType},
		{"With config - Handles missing input", "", models.VanTransportType},
	}

	// Run tests with config
	for _, tt := range testsWithConfig {
		t.Run(tt.name, func(t *testing.T) {
			result := validateTransportType(ctx, models.Email{}, nil, tt.input, config)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsHallucination(t *testing.T) {
	tests := []struct {
		name       string
		userPrompt string
		pickup     models.Address
		dropoff    models.Address
		expected   bool
	}{
		{
			name: "Valid - Both city and zip present",
			//nolint:lll
			userPrompt: "Subject: Pickup in Boston, Dropoff in New York\nEmail Body: Pickup zip 02108, dropoff zip 10001",
			pickup:     models.Address{City: "Boston", Zip: "02108"},
			dropoff:    models.Address{City: "New York", Zip: "10001"},
			expected:   false,
		},
		{
			name:       "Valid - Only zips in email but LLM supplements city",
			userPrompt: "Subject: Quote Request\nEmail Body:	 Pickup in 02108, Dropoff in 10001",
			pickup:     models.Address{City: "Boston", Zip: "02108"},
			dropoff:    models.Address{City: "New York", Zip: "10001"},
			expected:   false,
		},
		{
			name:       "Valid - Only city mentioned",
			userPrompt: "Subject: Quote Request\nEmail Body: Pickup in Boston, Dropoff in New York",
			pickup:     models.Address{City: "Boston"},
			dropoff:    models.Address{City: "New York"},
			expected:   false,
		},
		{
			name:       "Hallucination - Neither city nor zip in email",
			userPrompt: "Subject: Random email content\nEmail Body: This email does not mention any locations.",
			pickup:     models.Address{City: "Boston", Zip: "02108"},
			dropoff:    models.Address{City: "New York", Zip: "10001"},
			expected:   true,
		},
		{
			name:       "Valid - Pickup missing in email",
			userPrompt: "Subject: Dropoff in New York",
			pickup:     models.Address{City: "Boston", Zip: "02108"},
			dropoff:    models.Address{City: "New York", Zip: "10001"},
			expected:   false,
		},
		{
			name:       "Valid - Dropoff missing in email",
			userPrompt: "Subject: Pickup in Boston",
			pickup:     models.Address{City: "Boston"},
			dropoff:    models.Address{City: "New York"},
			expected:   false,
		},
		{
			name:       "Valid - LLM corrected pickup misspelling",
			userPrompt: "Subject: Pickup in Bostno, dropoff in new york",
			pickup:     models.Address{City: "Boston"},
			dropoff:    models.Address{City: "New York"},
			expected:   false,
		},
		// This case is not supported; if email mentions only zips, expected behavior is LLM returns either
		// a) just zips or b) city+zip, NOT just city
		{
			name:       "Invalid - Only zips in email but LLM REPLACES with city",
			userPrompt: "Subject: Pickup in 02108, Dropoff in 10001",
			pickup:     models.Address{City: "Boston"},
			dropoff:    models.Address{City: "New York"},
			expected:   true,
		},
		// Should be a low surface area case, which is why function returns true only if both pickup and dropoff
		// are missing from the email to leave room for NLP
		{
			name:       "Valid - LLM reformatted both pickup and dropoff",
			userPrompt: "Subject: Pickup in Bostno, dropoff in NY",
			pickup:     models.Address{City: "Boston"},
			dropoff:    models.Address{City: "New York"},
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isHallucination(tt.userPrompt, tt.pickup, tt.dropoff)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func wrapTestExpectedToOpenAI(expected string) openai.GetResponseOutput {
	return openai.GetResponseOutput{
		Content:         expected,
		ResponseID:      "",
		BraintrustLogID: "",
	}
}
