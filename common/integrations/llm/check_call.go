package llm

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
)

func ExtractCheckCallSuggestions(
	ctx context.Context,
	email models.Email,
	openAI openai.Service,
	rds RDSInterface,
) (res []models.SuggestedLoadChange, err error) {
	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractCheckCallSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	if len(email.Loads) == 0 {
		log.Info(ctx, "no freightTrackingID found for message, skipping sending to OpenAI",
			zap.String("threadID", email.ThreadID))
		return res, nil
	}

	//nolint:lll
	type CheckCall struct {
		FreightTrackingID string `json:"freight_tracking_id" jsonschema_description:"The ID of the load/trailer/truck. It's usually a sequence of numbers that is sometimes prepended with alphabetical characters. The ID is never just a string. It should always have numbers."`
		Status            string `json:"status" jsonschema_description:"The status of the load/trailer/truck. Expected values are 'at pickup', 'loaded', 'in-transit', 'at dropoff', 'unloaded'."`
		Date              string `json:"date" jsonschema_description:"The date of this check call update in format 'YYYY-MM-DD' (e.g., '2023-04-04'). If the year isn't provided, then infer based on today's date."`
		Time              string `json:"time" jsonschema_description:"The time of this check call update in format 'HH:mm' (e.g., '15:30')."`
		City              string `json:"city" jsonschema_description:"The city in which the load/trailer/truck currently is. If there's no city, then default to 'X'."`
		State             string `json:"state" jsonschema_description:"The state in which the load/trailer/truck currently is. If there's no state, then default to 'XX'."`
		Notes             string `json:"notes" jsonschema_description:"Any information of relevance to the status update."`
		Timezone          string `json:"timezone" jsonschema_description:"The IANA timezone, such as America/New_York, America/Phoenix. If the user does not specify the timezone, leave the field empty."`
	}

	type PromptResponse struct {
		CheckCalls []CheckCall `json:"check_call"`
	}

	// TODO: Check-in and check-out are separate check calls/fields
	var prompt = `You are Drumkit - a bleeding edge logistics scheduling assistant that interfaces via email.
    Make sure your final answers are definitive, complete and well formatted.

    You'll receive an e-mail body from the user. You should look for status updates on loads or trailers or trucks.
    The information we're most concerned with are status, timestamp, date, city, state, and notes.
    These can appear in the emails in any number of formats. They will usually appear all together in the same
    area of the email body for each load.

    Statuses we're most concerned with are "at pickup", "loaded", "in-transit", "at dropoff",
    "unloaded". Do your best to retrieve only these statuses and not other ones.

    1. "At Pickup": This status indicates that the driver has arrived at the pickup location (aka the shipper)
		and is in the process of loading the shipment onto the truck. The driver may be waiting for the cargo
		to be ready or is actively loading the goods.

    2. "Loaded": When a check call status is "Loaded," it means that the shipment has been successfully loaded
       onto the truck, and the driver is ready to depart from the pickup location to begin the transit journey.

    3. "In-Transit": The "In-Transit" status signifies that the driver has left the pickup location and is currently
       en route to the delivery destination. This status remains active until the driver arrives at the dropoff
       location.

    4. "At Dropoff": When the status is "At Dropoff," the driver has arrived at the delivery destination
	(aka consignee) and is preparing to unload the shipment from the truck. The driver may be waiting for
	the receiver to accept the delivery or is actively unloading the goods.

    5. "Unloaded": The "Unloaded" status indicates that the driver has successfully unloaded the shipment from the
       truck at the delivery destination/consignee. This is typically the final milestone in the check call process,
	   signifying that the delivery has been completed.

    One e-mail can contain multiple statuses for different loads.

    Your responses should be a JSON with this type structure:

       {
         "check_call": [
           {
             "freight_tracking_id": string,
             "date": string,
             "time": string,
             "status": string,
             "city": string,
             "state": string,
             "timezone": string,
             "notes": string
           }
         ]
       }

    Here's additional context of what values we expect for the response:

       {
         "check_call": [
           {
             "freight_tracking_id": "the tracking id",
             "date": "the date of this check call",
             "time": "the time of this check call",
             "timezone": "the timezone of the location specified by the user",
             "status": "status value",
             "city": "city name",
             "state": "state name",
             "notes": "relevant notes"
           }
         ]
       }

    "freight_tracking_id" should be the ID of the load/trailer/truck. It's usually a sequence of numbers that is
    sometimes prepended with alphabetical characters. The ID is never just a string. It should always have numbers.
    "status" should be the status of the load/trailer/truck.
    "date" should be the date of the check call update in this format "YYYY-MM-DD" (e.g., "2023-04-04").
    If the year isn't provided, then infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `).
    "time" should be the time of the check call update in this format "HH:mm" (e.g., "15:30").
    "city" should be in which city the load/trailer/truck currently. If there's no city, then default to "X".
    "state" should be in which state the load/trailer/truck currently. If there's no state, then default to "XX".
    "notes" should be anything of relevance to the status update.
    "timezone" should be the IANA timezone, such as America/New_York, America/Phoenix.
    If the user does not specify the timezone, leave the field empty.

	If the user provides check-in and check-out times, then you should generate separate check calls for that load.
	If the status is related to pickup, then check-in is equivalent to "at pickup" status, and check-out to "loaded".
	If the status is related to dropoff, then check-in is equivalent to "at dropoff" status,
	and check-out to "unloaded".
	If the user provides the current location beyond pickup/dropoff, then it's equivalent to "in-transit" status.

    Example:

    2120443
    Driver is loaded
    checkin : 8:30pm 3/29
    checkout : 02:00am 3/30
    Temp : -10
    Current location : Rolla ,MO

    21204-69341
    Shipment is delivered/unloaded. 30 min delay at dock due to mech issues.
    checkin @ 5pm CDT 3/29
    checkout : 8pm CDT 3/29
    Temp : -15

    2120490 : Driver is not answering , i will update you shortly

    Response:

    {
      "check_call": [
        {
			"freight_tracking_id": "2120443",
			"status": "at pickup",
			"date": "2024-03-29",
			"time": "20:30",
			"timezone": "",
			"city": "",
			"state": "",
			"notes": "Temp: -10"
        },
		{
			"freight_tracking_id": "2120443",
			"status": "loaded",
			"date": "2024-03-30",
			"time": "02:00",
			"timezone": "",
			"city": "",
			"state": "",
			"notes": "Temp -10"
		},
		{
			"freight_tracking_id": "2120443",
			"status": "in-transit",
			"date": "",
			"time": "",
			"timezone": "",
			"city": "Rolla",
			"state": "MO",
			"notes": ""
		},
        {
			"freight_tracking_id": "21204-69341",
			"status": "at dropoff",
			"date": "2024-03-29",
			"time": "17:00",
			"timezone": "America/Chicago",
			"city": "",
			"state": "",
			"notes": "30 min delay at dock due to mech issues. Temp: -15",
        },
		{
			"freight_tracking_id": "21204-69341",
			"status": "unloaded",
			"date": "2024-03-29",
			"time": "20:00",
			"timezone": "America/Chicago",
			"city": "",
			"state": "",
			"notes": "30 min delay at dock due to mech issues. Temp: -15",
		}
      ]
    }

    If no check call was found, return:

    {
      "check_call": []
    }

    The user e-mails are in English - US date format.`

	userPrompt := "Subject: " + email.Subject + "\n" + email.BodyWithoutSignature

	response, err := openAI.GetResponse(
		ctx,
		email,
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		openai.ResponseOptions{
			UserPrompt:      userPrompt,
			DeveloperPrompt: prompt,
			Schema:          extractor.GenerateSchema[PromptResponse](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("LLM check call extraction failed: %w", err)
	}

	log.Infof(ctx, "OpenAI response: %s", response.Content)

	resp, err := extractor.StructExtractor[PromptResponse](response.Content)
	if err != nil {
		log.ErrorNoSentry(ctx, "failed to extract JSON from OpenAI response", zap.Error(err))
		return res, nil
	}

	if len(resp.CheckCalls) < 1 {
		log.Info(ctx, "No check call found within email")
		return res, nil
	}

	for _, checkCall := range resp.CheckCalls {
		freightTrackingID := checkCall.FreightTrackingID
		load, err := rds.GetLoadByFreightID(ctx, email.ServiceID, freightTrackingID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Infof(ctx,
					"no load associated with freightTrackingID %s, not capturing in check call",
					freightTrackingID)
			}

			continue
		}

		var loadTZ string
		city := checkCall.City
		state := checkCall.State

		switch strings.ToLower(checkCall.Status) {
		case "at pickup", "loaded":
			loadTZ = load.Pickup.Timezone
			city = load.Pickup.City
			state = load.Pickup.State

		case "at dropoff", "unloaded":
			loadTZ = load.Consignee.Timezone
			city = load.Consignee.City
			state = load.Consignee.State
		}

		var loc string
		// Always keep timestamps for Aljex users TZ agnostic
		if load.TMS.Name != models.Aljex {
			loc = or(checkCall.Timezone, loadTZ)
		}

		var timestamp models.NullTime
		if checkCall.Date == "" || checkCall.Time == "" {
			timestamp = models.NullTime{Time: email.SentAt, Valid: true}
		} else {
			timestamp = StringsToTime(ctx, checkCall.Date, checkCall.Time, loc)
		}

		suggestedLoadChange := models.SuggestedLoadChange{
			Account:           email.Account,
			FreightTrackingID: freightTrackingID,
			ServiceID:         email.ServiceID,
			Suggested: models.SuggestedChanges{
				CheckCallChanges: &models.CheckCallChanges{
					Status:    checkCall.Status,
					Timestamp: timestamp,
					Timezone:  loc,
					City:      city,
					State:     state,
					Notes:     checkCall.Notes,
				},
			},
			Applied:  models.SuggestedChanges{},
			Pipeline: models.CheckCallPipeline,
			Category: models.CheckCallResponse,
			Status:   models.Pending,
			EmailID:  email.ID,
			ThreadID: email.ThreadID,
			Email:    email,
			LoadID:   &load.ID,
		}

		res = append(res, suggestedLoadChange)
	}

	log.Debug(ctx, "suggested load change", zap.Any("loadChange", res))
	return res, err
}
