package llm

import (
	"context"
	"os"
	"testing"
	"time"

	awstextract "github.com/aws/aws-sdk-go-v2/service/textract"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/models"
)

type DateParserTestSuite struct {
	suite.Suite
}

// Mock implementations for testing

type MockTextractClient struct {
	mock.Mock
}

func (m *MockTextractClient) AnalyzeDocument(
	_ context.Context,
	input *awstextract.AnalyzeDocumentInput,
	_ ...func(*awstextract.Options),
) (*awstextract.AnalyzeDocumentOutput, error) {
	args := m.Called(input)
	return args.Get(0).(*awstextract.AnalyzeDocumentOutput), args.Error(1)
}

// Test constants and variables
var mockCustomer = models.TMSCustomer{
	Model:           gorm.Model{ID: 10},
	CompanyCoreInfo: models.CompanyCoreInfo{Name: "XYZ Goods"}}
var mockTMS = models.Integration{
	Model: gorm.Model{ID: 1},
	Name:  models.McleodEnterprise}

// Sample customer info for LLM responses
var mockLLMCustomerOutput = CustomerInfo{
	ShipperName:         mockCustomer.Name,
	OriginalSenderEmail: "<EMAIL>",
}

// TestOpenAIBasedQuoteExtraction tests the OpenAI-based quote request extraction function.
func TestOpenAIBasedQuoteExtraction(t *testing.T) {
	// Skip if OpenAI credentials are not available
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	mockOpenaiService := new(MockOpenaiService)
	mockRDS := new(MockRDS)

	// Setup test email with required route info
	testEmail := models.Email{
		UserID:    2,
		ServiceID: 1,
		ThreadID:  "thread1",
		Body: `Email body must now be included in test so func doesn't skip hallucination.
			I need a pickup from Bayonne, NJ to East Windsor, CT.`,
		Subject: "Test Subject",
	}

	// Mock timezone lookup
	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	// Mock RDS dependencies
	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).
		Return([]models.Integration{mockTMS}, nil)
	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, testEmail.ThreadID, testEmail.UserID).
		Return(1, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	// Set up expectations for the OpenAI client
	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	// Call function under test
	result, err := promptQuoteRequestLLM(
		context.Background(),
		testEmail,
		nil,
		"",
		models.Attachment{},
		mockOpenaiService,
		false,
		mockRDS,
	)

	// Verify results
	require.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
	}

	// Verify all mocks were called correctly
	mockRDS.AssertExpectations(t)
}

// TestPDFAttachmentQuoteExtraction tests extracting quote requests from a PDF attachment.
func TestPDFAttachmentQuoteExtraction(t *testing.T) {
	// Skip if OpenAI credentials are not available
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	// Setup test email with PDF attachment
	testEmail := models.Email{
		UserID:    2,
		ServiceID: 1,
		ThreadID:  "thread1",
		Subject:   "Mock quote request",
		Body:      "This is a test email body. See attached.",
		Attachments: []models.Attachment{
			{
				OriginalFileName: "test.pdf",
				S3URL:            "s3://test.pdf",
				MimeType:         "application/pdf",
			},
		},
		HasPDFs: true,
	}

	mockOpenaiService := new(MockOpenaiService)
	mockTextract := new(MockTextractClient)
	mockRDS := new(MockRDS)

	// Mock s3PDFToMarkdownFunc and IsQuoteRequestEmailFunc
	getZeroxMarkdownFunc = func(
		_ context.Context,
		_ models.Attachment,
		_ ...extractor.MarkdownOptions,
	) (string, error) {
		return "mock markdown content containing Bayonne, NJ and East Windsor, CT", nil
	}

	// Mock timezone lookup
	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	// Mock RDS dependencies
	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).Return([]models.Integration{mockTMS}, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	// Set up expectations for the OpenAI client
	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	// Call function under test
	//nolint:lll
	result, err := ExtractQuoteRequestSuggestions(context.Background(), testEmail, mockOpenaiService, mockTextract, mockRDS)

	// Verify results
	require.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
		assert.Equal(t, testEmail.Attachments[0], result[0].Attachment)
	}

	// Verify all mocks were called correctly
	assertQRExpectations(t, mockRDS, mockTextract, mockOpenaiService)
}

// TestDetailedQuoteExtraction tests quote extraction from a detailed email body.
func TestDetailedQuoteExtraction(t *testing.T) {
	// Skip if OpenAI credentials are not available
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	mockOpenaiService := new(MockOpenaiService)
	// Set up expectations for the OpenAI client
	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	mockTextract := new(MockTextractClient)
	mockRDS := new(MockRDS)

	// Setup test email with detailed freight info
	detailedEmail := models.Email{
		Model:      gorm.Model{ID: 1},
		Account:    "<EMAIL>",
		UserID:     2,
		ServiceID:  1,
		ExternalID: "email1",
		ThreadID:   "thread1",
		Subject:    "Mock quote request",
		Body: `
			NJ-CT dry load Pick up: 8/12 or 8/14~8/15 9AM to 3PM (except Sunday and Tuesday)
			EASTERN FRESH NJ LLC 99 New Hook Rd, Bayonne, NJ 07002

			PHONE: ************<tel:(201)%20775-9999> / 201-862- 8888<tel:(201)%20862-8888>
			FAX: ************<tel:(201)%20339-1222>

			Deliver to: Deliver at same day or after day 12PM to 4PM (except Sunday and Thursday) Tam's
			Corporation 10 Thompson Rd, East Windsor, CT 06088
			Cell: ************<tel:(718)%20753-7981>

			Dry load, 42,000LB, mixed foods

			Please send me quote, my target is $600, thx.
		`,
	}

	// Mock timezone lookup
	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	// Mock RDS dependencies
	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, detailedEmail.ServiceID).
		Return([]models.Integration{mockTMS}, nil).Once()
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	// Call function under test
	result, err := ExtractQuoteRequestSuggestions(
		context.Background(),
		detailedEmail,
		mockOpenaiService,
		mockTextract,
		mockRDS,
	)

	// Verify results
	assert.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty quote request list")

	if len(result) > 0 {
		assert.Equal(t, detailedEmail.UserID, result[0].UserID)
		assert.Equal(t, detailedEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
	}

	// Verify all mocks were called correctly
	assertQRExpectations(t, mockRDS, mockTextract, mockOpenaiService)
}

//
// Live tests
// 1. You will need to comment out the skip in the test to run it.
// 2. You will also need to modify openai.NewService to this:
/**
func NewService(ctx context.Context) (Service, error) {

	clientOnce.Do(func() {

		apiKey := "[INSERT_API_KEY_HERE]"
		if apiKey == "" {
			clientErr = errors.New("OpenAI API key is not set")
			return
		}

		openaiClient = openaiSDK.NewClient(
			openaiOption.WithAPIKey(apiKey),
		)
	})

	return &service{client: openaiClient}, clientErr
}
**/

// TestLiveEndToEndQuoteExtraction tests the full extraction pipeline with real clients.
// This test is skipped by default and only runs when explicitly enabled.
func TestLiveEndToEndQuoteExtraction(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live test: run with LIVE_TEST=true to enable")
		return
	}

	// Skip if OpenAI credentials are not available
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	// Get real clients
	openaiService, err := openai.NewService(context.Background())
	if err != nil {
		t.Skipf("Failed to get OpenAI client: %v", err)
	}
	textractClient := textract.GetTextractClient(context.TODO())
	mockRDS := new(MockRDS)

	// Setup test email with detailed freight info
	testEmail := models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body: `
		NJ-CT dry load Pick up: 8/12 or 8/14~8/15 9AM to 3PM (except Sunday and Tuesday)

		EASTERN FRESH NJ LLC 99 New Hook Rd, Bayonne, NJ 07002

		PHONE: ************<tel:(201)%20775-9999> / 201-862- 8888<tel:(201)%20862-8888>
		FAX: ************<tel:(201)%20339-1222>

		Deliver to: Deliver at same day or after day 12PM to 4PM (except Sunday and Thursday) Tam's
		Corporation 10 Thompson Rd, East Windsor, CT 06088
		Cell: ************<tel:(718)%20753-7981>

		Dry load, 42,000LB, mixed foods

		Please send me quote, my target is $600, thx.
		`,
	}

	// Mock RDS dependencies
	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).
		Return([]models.Integration{{Name: models.McleodEnterprise}}, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	// Call function under test
	result, err := ExtractQuoteRequestSuggestions(
		context.Background(),
		testEmail,
		openaiService,
		textractClient,
		mockRDS,
	)

	// Verify results
	require.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
	}

	// Only verify RDS mock expectations since we're using real OpenAI and Textract clients
	mockRDS.AssertExpectations(t)
}

// TestLiveIsQuoteRequestEmail tests detecting a single quote request email
func TestLiveIsQuoteRequestEmail(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM isQuoteRequestEmail test: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	testEmail := models.Email{
		Subject: "Rate for Dallas to Houston",
		Body: `We need a rate for a shipment from Dallas to Houston.
			Details:
			- Pickup date: 09/15/2023
			- Delivery date: 09/16/2023
			- Commodity: Electronics
			- Weight: 10,000 lbs
			- Equipment: Dry van
			- Special requirements: None

			Please provide your best rate as soon as possible.

			Thanks,
			John Smith
			ABC Electronics`,
	}

	// Get real OpenAI client
	openaiService, err := openai.NewService(ctx)
	require.NoError(t, err, "Failed to create OpenAI service")

	// Test isQuoteRequestEmail
	isQuote := isQuoteRequestEmail(
		ctx,
		testEmail.Body,
		"text",
		testEmail,
		openaiService,
		false,
	)
	assert.True(t, isQuote, "Email should be identified as a quote request")

}

// TestLivePromptLLMForCustomer tests the promptLLMForCustomer function
func TestLivePromptLLMForCustomer(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM promptLLMForCustomer test: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	testEmail := models.Email{
		Subject: "Rate for Dallas to Houston",
		Body: `We need a rate for a shipment from Dallas to Houston.
			Details:
			- Pickup date: 09/15/2023
			- Delivery date: 09/16/2023
			- Commodity: Electronics
			- Weight: 10,000 lbs
			- Equipment: Dry van
			- Special requirements: None

			Please provide your best rate as soon as possible.

			Thanks,
			John Smith
			XYZ Goods`,
	}

	// Get real OpenAI client
	openaiService, err := openai.NewService(ctx)
	require.NoError(t, err, "Failed to create OpenAI service")

	// Mock RDS for promptLLMForCustomer test
	mockRDS := new(MockRDS)
	mockRDS.On("GetCustomerByName", mock.Anything, mock.Anything, mock.Anything).Return(mockCustomer, nil)

	// Test promptLLMForCustomer
	customer, _, err := promptLLMForCustomer(
		ctx,
		mockTMS.ID,
		testEmail,
		nil,
		"",
		openaiService,
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRGetCustomer, false),
		mockRDS,
	)
	require.NoError(t, err, "promptLLMForCustomer should not return error")
	assert.NotNil(t, customer, "Should extract customer information")

	mockRDS.AssertExpectations(t)

}

// TestLiveMultiQuoteRequestEmail tests detecting a multi quote request email
func TestLiveMultiQuoteRequestEmail(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM isMultiQuoteRequestEmail test: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	testEmail := models.Email{
		Subject: "Multiple Lane Quote Request",
		Body: `We need rates for the following lanes:

			LANE 1:
			Origin: Chicago, IL
			Destination: St. Louis, MO
			Product: Auto parts
			Weight: 18,000 lbs
			Ready date: 08/25/2023

			LANE 2:
			Origin: Detroit, MI
			Destination: Indianapolis, IN
			Product: Machinery
			Weight: 22,000 lbs
			Ready date: 08/28/2023

			LANE 3:
			Origin: Columbus, OH
			Destination: Cincinnati, OH
			Product: Consumer goods
			Weight: 15,000 lbs
			Ready date: 08/26/2023

			Please quote each lane separately. We need these rates by EOD tomorrow.

			Thanks,
			Robert Chen
			Midwest Distribution`,
	}

	// Test IsMultiQuoteRequestEmail
	isMulti, err := IsMultiQuoteRequestEmail(ctx, testEmail)
	require.NoError(t, err, "IsMultiQuoteRequestEmail should not return error")
	assert.True(t, isMulti, "Email should be identified as a multi quote request")

}

// Date parsing test suite
func TestDateParserSuite(t *testing.T) {
	suite.Run(t, new(DateParserTestSuite))
}

func (suite *DateParserTestSuite) TestParseDate() {
	tests := []struct {
		name          string
		input         string
		city          string
		state         string
		expected      time.Time
		expectedError bool
	}{
		{
			name:          "Empty string",
			input:         "",
			expected:      time.Time{},
			expectedError: false,
		},
		{
			name:          "Valid date and time",
			input:         "05/15/2023, 2:30PM",
			city:          "New York",
			state:         "NY",
			expected:      time.Date(2023, time.May, 15, 14, 30, 0, 0, time.FixedZone("America/New_York", -14400)),
			expectedError: false,
		},
		{
			name:          "Valid date only",
			input:         "05/15/2023",
			expected:      time.Date(2023, 5, 15, 16, 0, 0, 0, time.UTC),
			expectedError: false,
		},
		{
			name:          "Invalid format",
			input:         "2023-05-15",
			expected:      time.Date(2023, 5, 15, 16, 0, 0, 0, time.UTC),
			expectedError: false,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			result, err := parseDate(context.Background(), tt.input, Address{City: tt.city, State: tt.state})

			if tt.expectedError {
				assert.Error(suite.T(), err)
			} else {
				if tt.name == "Valid date and time" {
					if !tt.expected.Equal(result) {
						assert.Errorf(suite.T(), err, "Expected %s, got %s", tt.expected, result)
					}
				} else {
					assert.NoError(suite.T(), err)
					assert.Equal(suite.T(), tt.expected, result)
				}
			}
		})
	}
}

func assertQRExpectations(
	t *testing.T,
	mockRDS *MockRDS,
	mockTextract *MockTextractClient,
	mockOpenaiService *MockOpenaiService,
) {

	mockRDS.AssertExpectations(t)
	mockTextract.AssertExpectations(t)
	mockOpenaiService.AssertExpectations(t)
}
