// Package mock provides an in-memory implementation of Outlook client interface
package mock

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/models"
)

// Service is an in-memory implementation of the msclient.Client interface
type Client struct {
	Subscriptions map[string]msclient.Subscription
	Messages      map[string]msclient.Message
	Calls         []string // List of service calls that tests can verify
}

func (m *Client) addCall(call string) {
	m.Calls = append(m.Calls, call)
}

func (m *Client) WatchInbox(_ context.Context, webhookURL, clientState string) (sub msclient.Subscription, err error) {
	m.addCall(fmt.Sprintf("WatchInbox(%s, %s)", webhookURL, clientState))

	subscription := msclient.Subscription{
		ID:              "MockSubscriptionID",
		NotificationURL: "www.test.com",
		ClientState:     clientState,
	}
	m.Subscriptions[subscription.ID] = subscription

	return
}

func (m *Client) RewatchInbox(
	_ context.Context,
	_ string,
	user models.UserAccessor,
) (sub msclient.Subscription, err error) {

	m.addCall(fmt.Sprintf("RewatchInbox(%d)", user.GetID()))
	return
}

func (m *Client) GetMessageByID(
	_ context.Context,
	id string,
	_ ...msclient.Option,
) (msg msclient.Message, err error) {

	m.addCall(fmt.Sprintf("GetMessageByID(%s)", id))

	msg, ok := m.Messages[id]
	if !ok {
		return msg, errtypes.EntityNotFoundError(models.Integration{}, id, "")
	}

	return msg, nil
}

// placeholder for mock attachment retrieval (until I actually write tests)
func (m *Client) GetMessageAttachmentsByID(_ context.Context, _ string) ([]msclient.Attachment, error) {
	return []msclient.Attachment{}, nil
}

func (m *Client) SendMessage(context.Context, *msclient.Message) error {
	return nil
}

func (m *Client) ReplyAll(context.Context, string, string) (*msclient.Message, error) {
	return &msclient.Message{}, nil
}

func (m *Client) DraftReplyAll(context.Context, string, string) (*msclient.Message, error) {
	return &msclient.Message{}, nil
}

func (m *Client) StopWatchingInbox(_ context.Context, subID string) (err error) {
	m.addCall(fmt.Sprintf("StopWatchingInbox(%s)", subID))
	if _, ok := m.Subscriptions[subID]; !ok {
		return errtypes.EntityNotFoundError(models.Integration{}, subID, "")
	}

	return

}

func (m *Client) ListMessagesAfterDate(_ context.Context, date time.Time) (msgs []msclient.Message, err error) {
	m.addCall(fmt.Sprintf("ListMessagesAfterDate(%s)", date.Format(time.DateOnly)))
	for _, m := range m.Messages {
		msgs = append(msgs, m)
	}
	return
}

func (m *Client) DraftMessage(context.Context, *msclient.Message) (err error) {
	return
}

func (m *Client) GetAuthenticatedUser() models.UserAccessor {
	return &models.User{Model: gorm.Model{ID: 1}, EmailAddress: "<EMAIL>"}
}

// AddAttachment mocks the addition of an attachment to a message
func (m *Client) AddAttachment(_ context.Context, messageID string, _ *msclient.Attachment) error {
	m.addCall(fmt.Sprintf("AddAttachment(%s)", messageID))
	return nil
}

func (m *Client) GetDistributionLists(context.Context) ([]msclient.GroupValue, error) {
	return []msclient.GroupValue{}, nil
}

func (m *Client) GetDistributionListMembers(context.Context, string) ([]msclient.MemberValue, error) {
	return []msclient.MemberValue{}, nil
}

var _ msclient.Client = &Client{}
