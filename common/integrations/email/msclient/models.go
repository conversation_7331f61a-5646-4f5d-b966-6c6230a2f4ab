package msclient

import (
	"context"
	"strings"
	"time"

	"github.com/k3a/html2text"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// MicrosoftAuthCodeRequest is the payload we receive after a successful ms auth
type (
	MicrosoftAuthCodeRequest struct {
		Account MicrosoftAccount `json:"account"`

		// On-Behalf-Of (OBO) flow
		AccessToken string `json:"accessToken"`
	}

	MicrosoftAccount struct {
		Name           string `json:"name"`
		Username       string `json:"username" validate:"required"`
		LocalAccountID string `json:"localAccountId" validate:"required"`
		TenantID       string `json:"tenantId" validate:"required"`
	}
)

type Subscription struct {
	ID                        string    `json:"id"`
	Resource                  string    `json:"resource"`
	ApplicationID             string    `json:"applicationId"`
	ChangeType                string    `json:"changeType"`
	ClientState               string    `json:"clientState"`
	NotificationURL           string    `json:"notificationUrl"`
	ExpirationDateTime        time.Time `json:"expirationDateTime"`
	CreatorID                 string    `json:"creatorId"`
	LatestSupportedTLSVersion string    `json:"latestSupportedTlsVersion"`
	NotificationContentType   string    `json:"notificationContentType"`
}

// On-Behalf-Of Token
// https://learn.microsoft.com/en-us/graph/auth-v2-user?tabs=http
type OBOToken struct {
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
	ExpiresIn    int    `json:"expires_in"`
	ExtExpiresIn int    `json:"ext_expires_in"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type (
	// Outlook throws an error if a field is initialized with a zero value so use omitempty tags
	Message struct {
		OdataContext               string    `json:"@odata.context,omitempty"`
		OdataEtag                  string    `json:"@odata.etag,omitempty"`
		ID                         string    `json:"id"`
		CreatedDateTime            time.Time `json:"createdDateTime,omitempty"`
		LastModifiedDateTime       time.Time `json:"lastModifiedDateTime,omitempty"`
		ChangeKey                  string    `json:"changeKey,omitempty"`
		Categories                 []string  `json:"categories,omitempty"`
		ReceivedDateTime           time.Time `json:"receivedDateTime,omitempty"`
		SentDateTime               time.Time `json:"sentDateTime,omitempty"`
		HasAttachments             bool      `json:"hasAttachments,omitempty"`
		InternetMessageID          string    `json:"internetMessageId,omitempty"`
		Subject                    string    `json:"subject,omitempty"`
		BodyPreview                string    `json:"bodyPreview,omitempty"`
		Importance                 string    `json:"importance,omitempty"`
		ParentFolderID             string    `json:"parentFolderId,omitempty"`
		ConversationID             string    `json:"conversationId,omitempty"`
		IsDeliveryReceiptRequested bool      `json:"isDeliveryReceiptRequested,omitempty"`
		IsReadReceiptRequested     bool      `json:"isReadReceiptRequested,omitempty"`
		IsRead                     bool      `json:"isRead,omitempty"`
		IsDraft                    bool      `json:"isDraft,omitempty"`
		WebLink                    string    `json:"webLink,omitempty"`
		InferenceClassification    string    `json:"inferenceClassification,omitempty"`
		// TODO: Verify references for nil pointer dereferences
		Body          *Body                 `json:"body,omitempty"`
		Sender        Sender                `json:"sender,omitempty"`
		From          From                  `json:"from,omitempty"`
		ToRecipients  []RecipientCollection `json:"toRecipients,omitempty"`
		CcRecipients  []RecipientCollection `json:"ccRecipients,omitempty"`
		BccRecipients []RecipientCollection `json:"bccRecipients,omitempty"`
		ReplyTo       []RecipientCollection `json:"replyTo,omitempty"`
		Flag          Flag                  `json:"flag,omitempty"`
	}

	// NOTE: In order to preserve the inline thread in the reply like the client app does,
	// we must specify "Comment" field in the request body and not Message.Body. Outlook API throws an error
	// if both are specified. Message object should be used to specify Sender, Recipients, Attachments, etc.
	// but NOT reply body. See https://stackoverflow.com/a/78750255/10715467 and
	// https://learn.microsoft.com/en-us/graph/api/message-replyall?view=graph-rest-1.0&tabs=http for more info.
	ReplyMessage struct {
		Message Message `json:"message"`
		Comment string  `json:"comment"`
	}

	Body struct {
		ContentType string `json:"contentType,omitempty"` // html or text
		Content     string `json:"content,omitempty"`
	}
	EmailAddress struct {
		Name    string `json:"name"`
		Address string `json:"address"`
	}
	Sender struct {
		EmailAddress EmailAddress `json:"emailAddress"`
	}
	From struct {
		EmailAddress EmailAddress `json:"emailAddress"`
	}
	RecipientCollection struct {
		EmailAddress EmailAddress `json:"emailAddress"`
	}
	Flag struct {
		FlagStatus string `json:"flagStatus,omitempty"`
	}
)

type ListResponse struct {
	DataContext  string `json:"@odata.context"`
	DataNextLink string `json:"@odata.nextLink"`
	DataCount    int    `json:"@odata.count"`
}

type MessageListResponse struct {
	ListResponse
	Value []Message `json:"value"`
}

type Header struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func (msg Message) ToEmailModel(ctx context.Context, user *models.User) (res models.Email) {
	var recipients []string
	for _, r := range msg.ToRecipients {
		recipients = append(recipients, r.EmailAddress.Address)
	}

	var cc []string
	for _, r := range msg.CcRecipients {
		cc = append(cc, r.EmailAddress.Address)
	}

	var body string
	if msg.Body.ContentType == "html" {
		log.Info(ctx, "msclient.Message.ToEmailModel - content type was html, using HTML2Text")
		body = html2text.HTML2Text(msg.Body.Content)
	} else {
		body = msg.Body.Content
	}

	return models.Email{
		Account:              user.EmailAddress,
		ServiceID:            user.ServiceID,
		UserID:               user.ID,
		RFCMessageID:         msg.InternetMessageID,
		ExternalID:           msg.ID,
		ThreadID:             msg.ConversationID,
		Body:                 body,
		CC:                   strings.Join(cc, ","),
		ClassificationMethod: "",
		SentAt:               msg.SentDateTime,
		Sender:               msg.Sender.EmailAddress.Address,
		Labels:               "",
		Subject:              msg.Subject,
		S3URL:                "",
		Recipients:           strings.Join(recipients, ","),
		ThreadReferences:     "",
		InReplyTo:            "",
		WebLink:              msg.WebLink,
		Loads:                []models.Load{},
	}
}

// ToGeneratedEmailModel converts a Message to models.GeneratedEmail
// Users can send from delegated inboxes, so `fromUser` is the account used to send the msg (e.g. <EMAIL>)
// and `triggeredByUser` is the Drumkit user that triggered sending the msg from that account (e.g. <EMAIL>).
func (msg Message) ToGeneratedEmailModel(
	sender *models.User,
	triggeredByUser *models.User) (res models.GeneratedEmail) {
	var recipients []string
	for _, r := range msg.ToRecipients {
		recipients = append(recipients, r.EmailAddress.Address)
	}

	var cc []string
	for _, r := range msg.CcRecipients {
		cc = append(cc, r.EmailAddress.Address)
	}

	var triggeredByUserID uint
	if triggeredByUser != nil {
		triggeredByUserID = triggeredByUser.ID
	}

	return models.GeneratedEmail{
		UserID:            sender.ID,
		ServiceID:         sender.ServiceID,
		RFCMessageID:      msg.InternetMessageID,
		ExternalID:        msg.ID,
		ThreadID:          msg.ConversationID,
		FreightTrackingID: "",
		Body:              msg.Body.Content,
		Recipients:        recipients,
		CC:                cc,
		SentAt:            models.NullTime{Time: msg.SentDateTime, Valid: !msg.SentDateTime.IsZero()},
		Sender:            msg.Sender.EmailAddress.Address,
		Subject:           msg.Subject,
		ThreadReferences:  "",
		WebLink:           msg.WebLink,
		TriggeredByUserID: triggeredByUserID,
	}
}
