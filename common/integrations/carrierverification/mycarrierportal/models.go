package mycarrierportal

type AuthResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
	UserName    string `json:"userName"`
}

type EmailSearchResponse struct {
	ID                      string               `json:"$id"`
	Success                 bool                 `json:"Success"`
	Message                 string               `json:"Message"`
	AssociatedCarriersCount int                  `json:"AssociatedCarriersCount"`
	AssociatedCarriers      []EmailSearchCarrier `json:"AssociatedCarriers"`
}

type EmailSearchCarrier struct {
	ID           string  `json:"$id"`
	DOTNumber    int     `json:"DOTNumber"`
	DocketNumber string  `json:"DocketNumber"`
	CompanyName  string  `json:"CompanyName"`
	DBAName      *string `json:"DBAName"`
	Street       string  `json:"Street"`
	City         string  `json:"City"`
	State        string  `json:"State"`
	ZipCode      string  `json:"ZipCode"`
	Country      string  `json:"Country"`
	Phone        string  `json:"Phone"`
}

type Carrier struct {
	ID                    string                `json:"$id"`
	CarrierID             int                   `json:"CarrierID"`
	DotNumber             int                   `json:"DotNumber"`
	DocketNumber          string                `json:"DocketNumber"`
	CompanyName           string                `json:"CompanyName"`
	DBAName               string                `json:"DBAName"`
	Street                string                `json:"Street"`
	City                  string                `json:"City"`
	State                 string                `json:"State"`
	ZipCode               string                `json:"ZipCode"`
	Country               string                `json:"Country"`
	Phone                 string                `json:"Phone"`
	Status                string                `json:"Status"`
	PossibleFraud         string                `json:"PossibleFraud"`
	DoubleBrokering       string                `json:"DoubleBrokering"`
	IncidentReports       IncidentReports       `json:"IncidentReports"`
	FraudCallNumber       string                `json:"FraudCallNumber"`
	HasSaferWatchKey      bool                  `json:"HasSaferWatchKey"`
	WatchdogReports       string                `json:"WatchdogReports"`
	CarrierRating         CarrierRating         `json:"CarrierRating"`
	RiskAssessment        RiskAssessment        `json:"RiskAssessment"`
	RiskAssessmentDetails RiskAssessmentDetails `json:"RiskAssessmentDetails"`
	CertData              CertData              `json:"CertData"`
	Emails                []Email               `json:"Emails"`
	Source                int                   `json:"Source"`
	IsIntrastateCarrier   bool                  `json:"IsIntrastateCarrier"`
	IsMonitored           bool                  `json:"IsMonitored"`
	IsBlocked             bool                  `json:"IsBlocked"`
}

type IncidentReports struct {
	ID                            string `json:"$id"`
	TotalIncidentReports          int    `json:"TotalIncidentReports"`
	TotalIncidentReportsWithFraud int    `json:"TotalIncidentReportsWithFraud"`
}

type CarrierRating struct {
	ID                               string  `json:"$id"`
	CarrierID                        int     `json:"CarrierID"`
	CustomerID                       int     `json:"CustomerID"`
	CustomerRating                   int     `json:"CustomerRating"`
	RatingSum                        int     `json:"RatingSum"`
	TotalRatings                     int     `json:"TotalRatings"`
	LowRatings                       int     `json:"LowRatings"`
	TotalRatingPercent               int     `json:"TotalRatingPercent"`
	CustomerRatingPercent            int     `json:"CustomerRatingPercent"`
	RatingValue                      float64 `json:"RatingValue"`
	RatingValueText                  string  `json:"RatingValueText"`
	AvgRatingText                    string  `json:"AvgRatingText"`
	AvgRatingBasisText               string  `json:"AvgRatingBasisText"`
	AvgRatingTextPlusRatingBasisText string  `json:"AvgRatingTextPlusRatingBasisText"`
	CustomerRatingText               string  `json:"CustomerRatingText"`
	HasCompletedPacket               bool    `json:"HasCompletedPacket"`
}

type RiskAssessment struct {
	ID        string `json:"$id"`
	Overall   string `json:"Overall"`
	Authority string `json:"Authority"`
	Insurance string `json:"Insurance"`
	Safety    string `json:"Safety"`
	Operation string `json:"Operation"`
	Other     string `json:"Other"`
}

type RiskAssessmentDetails struct {
	ID                  string        `json:"$id"`
	IsIntrastateCarrier bool          `json:"IsIntrastateCarrier"`
	TotalPoints         int           `json:"TotalPoints"`
	OverallRating       string        `json:"OverallRating"`
	Authority           RatingDetails `json:"Authority"`
	Insurance           RatingDetails `json:"Insurance"`
	Safety              RatingDetails `json:"Safety"`
	Operation           RatingDetails `json:"Operation"`
	Other               RatingDetails `json:"Other"`
}

type RatingDetails struct {
	ID            string `json:"$id"`
	TotalPoints   int    `json:"TotalPoints"`
	OverallRating string `json:"OverallRating"`
}

type Infraction struct {
	ID        string `json:"$id"`
	Points    int    `json:"Points"`
	RiskLevel string `json:"RiskLevel"`
	RuleText  string `json:"RuleText"`
}

type CertData struct {
	ID      string `json:"$id"`
	Status  string `json:"Status"`
	Noncoop bool   `json:"Noncoop"`
}

type Email struct {
	ID          string `json:"$id"`
	EmailType   int    `json:"EmailType"`
	Description string `json:"Description"`
	Email       string `json:"Email"`
}
