package truckstop

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type Truckstop struct {
	integration models.Integration
}

const (
	devRedirectURI     = "http://localhost:5173/integrations/truckstop"
	stagingRedirectURI = "https://app-staging.drumkit.ai/integrations/truckstop"
	prodRedirectURI    = "https://app.drumkit.ai/integrations/truckstop"
)

func New(ctx context.Context, integration models.Integration) (Truckstop, error) {
	log.With(ctx, zap.Uint("drumkitIntegrationID", integration.ID), zap.String("integration", "truckstop"))

	if integration.AccessToken != "" && integration.NeedsRefresh() {
		client := NewClient(integration, env.Vars.AppEnv)
		tokenResp, err := client.RefreshToken(ctx)
		if err != nil {
			return Truckstop{}, fmt.Errorf("failed to refresh token: %w", err)
		}

		integration.AccessToken = tokenResp.AccessToken
		integration.AccessTokenExpirationDate = models.NullTime{
			Time:  time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second),
			Valid: true,
		}
		integration.RefreshToken = tokenResp.RefreshToken

		if integration.ID != 0 {
			if err = integrationDB.Update(ctx, &integration); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update truckstop info on integration db",
					zap.Any("integration", integration),
				)
				return Truckstop{}, fmt.Errorf("integration db update failed: %w", err)
			}
		}
	}

	return Truckstop{integration: integration}, nil
}

// InitialOnboard handles the initial authentication with Truckstop
func (t Truckstop) InitialOnboard(
	ctx context.Context,
	service models.Service,
	request models.OnboardCarrierVerificationRequest,
) (models.OnboardCarrierVerificationResponse, error) {
	var redirectURI string
	switch env.Vars.AppEnv {
	case "staging":
		redirectURI = stagingRedirectURI
	case "dev":
		redirectURI = devRedirectURI
	default:
		redirectURI = prodRedirectURI
	}
	t.integration.Service = service

	tokenResp, err := NewClient(t.integration, env.Vars.AppEnv).Authenticate(
		ctx,
		request.AuthorizationCode,
		redirectURI,
	)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, err
	}

	tokenExpTime := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return models.OnboardCarrierVerificationResponse{
		AccessToken:               tokenResp.AccessToken,
		AccessTokenExpirationDate: tokenExpTime,
		RefreshToken:              tokenResp.RefreshToken,
	}, nil
}

// GetCarrier retrieves carrier information from Truckstop
func (t Truckstop) GetCarrier(
	ctx context.Context,
	email string,
) (carrier models.CarrierVerificationResponse, err error) {

	client := NewClient(t.integration, env.Vars.AppEnv)
	report, err := client.GetCarrier(ctx, email)
	if err != nil {
		return carrier, err
	}
	// Map DOT number and MC number from identifiers
	dotNum, err := strconv.Atoi(report.Identifiers.DOTNumber)
	if err != nil {
		return carrier, fmt.Errorf("invalid DOT number: %w", err)
	}
	carrier.DOTNumber = dotNum

	if len(report.Identifiers.MCNumbers) > 0 {
		carrier.DocketNumber = report.Identifiers.MCNumbers[0]
	}

	carrier.RiskRating = report.ReportSummary
	carrier.IntegrationName = string(models.Truckstop)

	return carrier, nil
}

// InviteCarrier sends an invitation to a carrier through Truckstop
func (t Truckstop) InviteCarrier(ctx context.Context, dotnumber, email string) error {
	return NewClient(t.integration, env.Vars.AppEnv).InviteCarrier(ctx, dotnumber, email)
}
