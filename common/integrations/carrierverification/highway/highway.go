package highway

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util"
)

const (
	stagingHost        = "https://staging.highway.com"
	prodHost           = "https://highway.com"
	devRedirectURI     = "http://localhost:5173/integrations/highway"
	stagingRedirectURI = "https://app-staging.drumkit.ai/integrations/highway"
	prodRedirectURI    = "https://app.drumkit.ai/integrations/highway"
)

var baseURL string

type Highway struct {
	integration models.Integration
}

func New(ctx context.Context, integration models.Integration) (Highway, error) {
	log.With(ctx, zap.Uint("drumkitIntegrationID", integration.ID), zap.String("integration", "highway"))

	if env.Vars.AppEnv == "prod" {
		baseURL = prodHost
	} else {
		baseURL = stagingHost
	}

	if integration.AccessToken != "" && integration.NeedsRefresh() {
		client := NewClient(integration)
		refreshTokenResp, err := client.RefreshToken(
			ctx,
			integration.Service.HighwayClientID,
			integration.Service.HighwayClientSecret,
			integration.RefreshToken,
		)

		if err != nil {
			return Highway{}, err
		}
		integration.AccessToken = refreshTokenResp.AccessToken
		integration.RefreshToken = refreshTokenResp.RefreshToken
		integration.AccessTokenExpirationDate = models.NullTime{
			Time:  time.Now().Add(time.Duration(refreshTokenResp.ExpiresIn) * time.Second),
			Valid: true,
		}

		if integration.ID != 0 {
			if err = integrationDB.Update(ctx, &integration); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update highway info on integration db",
					zap.Any("integration", integration),
				)
				return Highway{}, fmt.Errorf("integration db update failed: %w", err)
			}
		}
	}

	return Highway{integration: integration}, nil
}

// InitialOnboard docs: https://docs.highway.com/reference/create-access-token
func (h Highway) InitialOnboard(
	ctx context.Context,
	service models.Service,
	request models.OnboardCarrierVerificationRequest,
) (models.OnboardCarrierVerificationResponse, error) {
	// Determine redirect URI based on environment
	var redirectURI string
	switch env.Vars.AppEnv {
	case "staging":
		redirectURI = stagingRedirectURI
	case "dev":
		redirectURI = devRedirectURI
	default:
		redirectURI = prodRedirectURI
	}

	tokenResp, err := NewClient(h.integration).Authenticate(ctx, service.HighwayClientID,
		service.HighwayClientSecret, redirectURI, request.AuthorizationCode)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, err
	}

	// calculating token expiration time
	tokenUpdatedTime, err := util.ParseDatetime(tokenResp.UpdatedAt)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, err
	}
	tokenExpTime := tokenUpdatedTime.Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return models.OnboardCarrierVerificationResponse{
		AccessToken:               tokenResp.AccessToken,
		RefreshToken:              tokenResp.RefreshToken,
		AccessTokenExpirationDate: tokenExpTime,
	}, nil
}

// GetCarrier docs: https://docs.highway.com/reference/email-search
func (h Highway) GetCarrier(ctx context.Context, email string) (carrier models.CarrierVerificationResponse, err error) {
	client := NewClient(h.integration)
	emailData, err := client.EmailSearch(ctx, email)
	if err != nil {
		return carrier, err
	}
	var emailCarrier Carrier

	if len(emailData.Carriers) > 0 {
		emailCarrier = emailData.Carriers[0]
	}

	carrierData, err := client.GetCarrierByID(ctx, emailCarrier.ID)
	if err != nil {
		return carrier, err
	}

	carrier.CarrierName = carrierData.LegalName
	if carrierData.DbaName != nil {
		carrier.DBA = *carrierData.DbaName
	}
	carrier.DOTNumber = carrierData.DotNumber
	carrier.DocketNumber = strconv.Itoa(carrierData.McNumber)
	carrier.RiskRating = carrierData.AuthorityAssessment.Rating
	carrier.TotalIncidentReports = carrierData.Inspections.TotalInspections
	carrier.IntegrationName = string(models.Highway)

	return carrier, nil
}

// InviteCarrier docs : https://docs.highway.com/reference/create-2
func (h Highway) InviteCarrier(ctx context.Context, dotnumber, email string) (err error) {
	return NewClient(h.integration).InviteCarrier(ctx, dotnumber, email)
}
