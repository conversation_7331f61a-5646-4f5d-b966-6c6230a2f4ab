package hubspot

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

type (
	Field struct {
		ObjectTypeID string `json:"objectTypeId"`
		Name         string `json:"name"`
		Value        string `json:"value"`
	}

	FormSubmission struct {
		Fields []Field `json:"fields"`
	}

	ErrorDetail struct {
		Message   string `json:"message"`
		ErrorType string `json:"errorType"`
	}

	FormSubmissionResponse struct {
		RedirectURI   string        `json:"redirectUri,omitempty"`
		InlineMessage string        `json:"inlineMessage,omitempty"`
		Errors        []ErrorDetail `json:"errors,omitempty"`
	}
)

// https://legacydocs.hubspot.com/docs/methods/forms/submit_form_v3_authentication#:~:text=As%20this%20API%20is%20authenticated%2C
// This endpoint has the following rate limits:
//
//	Free/Starter accounts: 100 requests/10 seconds
//	Professional/Enterprise accounts: 150 requests/10 seconds
//	Accounts with the API Add On: 200 requests/10 seconds
//
//nolint:lll
func (h *HubSpot) SubmitForm(
	ctx context.Context,
	formID, portalID string,
	reqBody *FormSubmission,
) (*FormSubmissionResponse, error) {

	path := fmt.Sprintf("submissions/v3/integration/secure/submit/%s/%s", portalID, formID)

	addr := url.URL{
		Scheme: "https",
		Host:   "api.hsforms.com",
		Path:   path,
	}
	url := addr.String()

	auth := "Bearer " + h.crm.AccessToken
	headerMap := make(map[string]string)
	respBody, _, err := httputil.PostBytesWithToken(ctx, h.crm, addr, reqBody, headerMap, &auth, s3backup.TypeTokens)

	log.Debug(ctx, "received Hubspot response", zap.ByteString("body", respBody))

	if err != nil {
		return nil, err
	}

	var res FormSubmissionResponse
	if err = json.Unmarshal(respBody, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal %s json response body: %w", url, err)
	}

	return &res, nil
}
