package velostics

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type requestOption func(*http.Request)

func withJSON(body any) requestOption {
	return func(req *http.Request) {
		if body == nil {
			return
		}

		//nolint:errcheck
		jsonBytes, _ := json.Marshal(body)
		req.Body = io.NopCloser(bytes.NewReader(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
	}
}

func (v *Velostics) getToken(ctx context.Context) (string, error) {
	password, err := crypto.EncryptAESGCM(ctx, string(v.Source.EncryptedPassword), nil)
	if err != nil {
		return "", fmt.Errorf("encrypting password: %w", err)
	}

	formData := url.Values{
		"grant_type":    {"password"},
		"client_id":     {v.Source.Service.VelosticsClientID},
		"client_secret": {v.Source.Service.VelosticsClientSecret},
		"audience":      {"API_IDENTIFIER"}, // TODO: confirm value
		"username":      {v.Source.Username},
		"password":      {password},
		"scope":         {"SCOPE"}, // TODO: confirm value
	}

	var tokenResp TokenResp
	if err := v.post(ctx, "/oauth/token", nil, formData, &tokenResp); err != nil {
		return "", fmt.Errorf("getting token: %w", err)
	}

	return tokenResp.AccessToken, nil
}

func (v *Velostics) get(ctx context.Context, path string, query url.Values, out any) error {
	return v.do(ctx, http.MethodGet, path, query, out)
}

func (v *Velostics) post(ctx context.Context, path string, query url.Values, body, out any) error {
	return v.do(ctx, http.MethodPost, path, query, out, withJSON(body))
}

func (v *Velostics) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	out any,
	opts ...requestOption,
) error {

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     baseHost,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, nil)
	if err != nil {
		return fmt.Errorf("building request %s %s: %w", method, reqURL, err)
	}

	req.Header.Set("Authorization", "Bearer "+v.Source.AccessToken)

	for _, opt := range opts {
		opt(req)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, v.Source, err)
		return fmt.Errorf("sending request %s %s: %w", method, reqURL, err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, v.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("reading response %s: %w", reqURL, err)
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(v.Source, req, resp, respBody)
	}

	if !strings.Contains(path, "login") {
		log.Debug(
			ctx,
			"velostics response",
			zap.String("method", method),
			zap.String("url", reqURL),
			zap.ByteString("body", respBody),
		)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"unmarshal failed",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("unmarshal %s %s: %w", method, reqURL, err)
		}
	}

	return nil
}
