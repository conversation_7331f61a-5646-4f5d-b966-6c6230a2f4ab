package turvo

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	cancelApptPathFormat = "/v1/calendar/appointments/%s"
)

func (t *Turvo) CancelAppointment(ctx context.Context, id, _ string) (models.Appointment, error) {
	if id == "" {
		return models.Appointment{}, fmt.Errorf("appointment ID is required")
	}

	appt, err := t.GetAppointment(ctx, id)
	if err != nil {
		return models.Appointment{}, fmt.<PERSON><PERSON><PERSON>("failed to get appointment: %w", err)
	}

	if appt.ConfirmationNo == "" {
		return models.Appointment{}, fmt.Errorf("appointment has no confirmation number")
	}

	path := fmt.Sprintf(cancelApptPathFormat, appt.ConfirmationNo)
	return models.Appointment{}, t.delete(ctx, path, nil, nil, nil)
}
