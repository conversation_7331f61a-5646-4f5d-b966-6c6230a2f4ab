package turvo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type requestOption func(*http.Request)

func withJSON(body any) requestOption {
	return func(req *http.Request) {
		if body == nil {
			return
		}

		//nolint:errcheck
		jsonBytes, _ := json.Marshal(body)
		req.Body = io.NopCloser(bytes.NewReader(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
	}
}

func (t *Turvo) get(ctx context.Context, path string, query url.Values, out any) error {
	return t.do(ctx, http.MethodGet, path, query, out)
}

func (t *Turvo) post(ctx context.Context, path string, query url.Values, body, out any) error {
	return t.do(ctx, http.MethodPost, path, query, out, withJSON(body))
}

func (t *Turvo) delete(ctx context.Context, path string, query url.Values, body, out any) error {
	return t.do(ctx, http.MethodDelete, path, query, out, withJSON(body))
}

func (t *Turvo) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	out any,
	opts ...requestOption,
) error {

	var host = baseProdHost
	if t.Source.AppID == "staging" {
		host = baseStagingHost
	}

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     host,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, nil)
	if err != nil {
		return fmt.Errorf("building request %s %s: %w", method, reqURL, err)
	}

	if path == "oauth/token" {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("x-api-key", t.Source.APIKey)
	} else {
		req.Header.Set("Authorization", "Bearer "+t.Source.AccessToken)
	}

	for _, opt := range opts {
		opt(req)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, t.Source, err)
		return fmt.Errorf("sending request %s %s: %w", method, reqURL, err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, t.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("reading response %s: %w", reqURL, err)
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(t.Source, req, resp, respBody)
	}

	if !strings.Contains(path, "oauth/token") {
		log.Debug(
			ctx,
			"turvo response",
			zap.String("method", method),
			zap.String("url", reqURL),
			zap.ByteString("body", respBody),
		)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"unmarshal failed",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("unmarshal %s %s: %w", method, reqURL, err)
		}
	}

	return nil
}
