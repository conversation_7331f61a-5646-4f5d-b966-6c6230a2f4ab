package turvo

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

const (
	appointmentEndpoint = "/v1/calendar/appointments"
	apptResourceType    = "LOCATION"
	apptResourceIDType  = "turvoLocation"
	defaultDayOffset    = 1 // Number of days to add for end date
)

func (t *Turvo) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	// TODO: need to double check the request
	if err := validateAppointmentRequest(req); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid appointment request: %w", err)
	}

	apptReq := MakeAppointmentReq{
		Resource: Resource{
			ResourceID: req.WarehouseID,
			Type:       apptResourceType,
			IDType:     apptResourceIDType,
		},
		StartDate: TimeSlot{
			Date:     req.StartTime.Format(time.RFC3339),
			Timezone: req.WarehouseTimezone,
		},
		EndDate: TimeSlot{
			Date:     req.StartTime.AddDate(0, 0, defaultDayOffset).Format(time.RFC3339),
			Timezone: req.WarehouseTimezone,
		},
	}

	var resp MakeAppointmentResp
	if err := t.post(ctx, appointmentEndpoint, nil, apptReq, &resp); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create appointment: %w", err)
	}

	appointment, err := parseAppointmentResponse(ctx, resp)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to parse appointment response: %w", err)
	}

	return appointment, nil
}

func validateAppointmentRequest(req models.MakeAppointmentRequest) error {
	if req.WarehouseID == "" {
		return fmt.Errorf("warehouse ID is required")
	}

	if req.WarehouseTimezone == "" {
		return fmt.Errorf("warehouse timezone is required")
	}

	if req.StartTime.IsZero() {
		return fmt.Errorf("start time is required")
	}

	return nil
}

func parseAppointmentResponse(ctx context.Context, resp MakeAppointmentResp) (models.Appointment, error) {
	startDate, err := util.ParseDatetime(resp.Details.StartDate.Date)
	if err != nil {
		log.Error(ctx, "failed to parse start date time string", zap.Error(err))
		return models.Appointment{}, fmt.Errorf("invalid start date format: %w", err)
	}

	return models.Appointment{
		ConfirmationNo: resp.Details.ConfirmationNumber,
		Date:           startDate.Format(time.DateOnly),
		StartTime:      startDate,
		Notes:          resp.Details.Attributes.Notes,
	}, nil
}

func (t *Turvo) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.Turvo, "MakeAppointmentWithLoad")
}
