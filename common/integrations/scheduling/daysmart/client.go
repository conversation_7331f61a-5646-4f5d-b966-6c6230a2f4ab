package daysmart

import (
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	baseHost   = "ws.appointment-plus.com"
	baseURL    = "https://" + baseHost
	dateFormat = "20060102"
)

type (
	DaySmart struct {
		Source models.Integration
	}
)

func validateAppointmentResponse(resp AppointmentResp) error {
	if resp.Data.ApptID == "" {
		return fmt.Errorf("missing appointment ID in response")
	}

	if resp.Data.Date == "" {
		return fmt.Errorf("missing date in response")
	}

	return nil
}

func minutesSinceMidnight(dateStr string, minutesOffset int) (time.Time, error) {
	baseDate, err := time.Parse(dateFormat, dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid date format '%s': %w", dateStr, err)
	}

	if minutesOffset < 0 {
		return time.Time{}, fmt.Errorf("minutes offset cannot be negative, got: %d", minutesOffset)
	}

	return baseDate.Add(time.Duration(minutesOffset) * time.Minute), nil
}
