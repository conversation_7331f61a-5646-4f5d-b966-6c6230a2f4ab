package daysmart

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	getApptEndpoint = "/Appointments/GetAppointments"
	apptIDParam     = "appt_id"
)

func (d *DaySmart) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	if err := validateAppointmentID(id); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid appointment ID: %w", err)
	}

	result, err := d.fetchAppointment(ctx, id)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to fetch appointment: %w", err)
	}

	appointment, err := d.createAppointmentModel(result.Data)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create appointment model: %w", err)
	}

	return appointment, nil
}

func validateAppointmentID(appointmentID string) error {
	if appointmentID == "" {
		return fmt.Errorf("appointment ID is required")
	}

	return nil
}

func (d *DaySmart) fetchAppointment(ctx context.Context, appointmentID string) (AppointmentResp, error) {
	queryParams := url.Values{
		apptIDParam: {appointmentID},
	}

	var result AppointmentResp
	if err := d.post(ctx, getApptEndpoint, queryParams, nil, &result); err != nil {
		return AppointmentResp{}, fmt.Errorf("API request failed: %w", err)
	}

	if err := validateAppointmentResponse(result); err != nil {
		return AppointmentResp{}, fmt.Errorf("invalid response: %w", err)
	}

	return result, nil
}

func (d *DaySmart) createAppointmentModel(data AppointmentData) (models.Appointment, error) {
	startTime, err := minutesSinceMidnight(data.Date, data.StartTime)
	if err != nil {
		return models.Appointment{}, err
	}

	return models.Appointment{
		ExternalID:  data.ApptID,
		WarehouseID: data.LocationIDCID,
		Date:        startTime.Format(time.DateOnly),
		StartTime:   startTime,
		Notes:       data.CustomerNotes,
	}, nil
}
