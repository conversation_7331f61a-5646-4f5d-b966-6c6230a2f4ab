package daysmart

import (
	"context"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	cancelAppointmentEndpoint = "/Appointments/CancelAppointments"
)

func (d *DaySmart) CancelAppointment(ctx context.Context, id, reason string) (models.Appointment, error) {
	if err := validateCancelParams(id, reason); err != nil {
		return models.Appointment{}, err
	}

	queryParams := makeQueryParams(id, reason)

	var response AppointmentResp
	if err := d.post(ctx, cancelAppointmentEndpoint, queryParams, nil, &response); err != nil {
		log.Error(
			ctx,
			"failed to cancel appointment",
			zap.String("appointmentID", id),
			zap.Error(err),
		)

		return models.Appointment{}, fmt.Errorf("failed to cancel appointment: %w", err)
	}

	log.Info(
		ctx,
		"appointment cancelled successfully",
		zap.String("appointmentID", id),
		zap.String("reason", reason),
	)

	return models.Appointment{}, nil
}

func validateCancelParams(appointmentID, reason string) error {
	if appointmentID == "" {
		return fmt.Errorf("appointment ID is required")
	}

	if reason == "" {
		return fmt.Errorf("cancellation reason is required")
	}

	return nil
}

func makeQueryParams(appointmentID, reason string) url.Values {
	return url.Values{
		"appt_id": {appointmentID},
		"reason":  {reason},
	}
}
