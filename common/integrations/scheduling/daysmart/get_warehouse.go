package daysmart

import (
	"context"
	"fmt"
	"net/url"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	warehouseEndpoint = "/Locations/GetLocations"
	cidParam          = "c_id"
)

func (d *DaySmart) GetWarehouse(ctx context.Context, id string) (models.Warehouse, error) {
	if id == "" {
		return models.Warehouse{}, fmt.Errorf("warehouse ID is required")
	}

	result, err := d.fetchWarehouseData(ctx, id)
	if err != nil {
		return models.Warehouse{}, err
	}

	if len(result.Data) == 0 {
		return models.Warehouse{}, fmt.Errorf("warehouse not found: %s", id)
	}

	warehouse, err := d.createWarehouseModel(ctx, result.Data[0])
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("failed to create warehouse model: %w", err)
	}

	return warehouse, nil
}

func (d *DaySmart) fetchWarehouseData(ctx context.Context, warehouseID string) (GetAllWarehousesResp, error) {
	queryParams := url.Values{
		cidParam: {warehouseID},
	}

	var result GetAllWarehousesResp
	if err := d.post(ctx, warehouseEndpoint, queryParams, nil, &result); err != nil {
		return GetAllWarehousesResp{}, fmt.Errorf("failed to fetch warehouse data: %w", err)
	}

	return result, nil
}
