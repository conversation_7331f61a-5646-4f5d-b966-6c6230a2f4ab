package daysmart

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/timezone"
)

const (
	warehousesEndpoint = "/Locations/GetLocations"
	addressSeparator   = " "
)

func (d *DaySmart) GetAllWarehouses(ctx context.Context) ([]models.Warehouse, error) {
	warehouses, err := d.fetchWarehouses(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch warehouses: %w", err)
	}

	return d.convertToWarehouses(ctx, warehouses), nil
}

func (d *DaySmart) fetchWarehouses(ctx context.Context) (GetAllWarehousesResp, error) {
	var result GetAllWarehousesResp
	if err := d.post(ctx, warehousesEndpoint, nil, nil, &result); err != nil {
		return GetAllWarehousesResp{}, fmt.Errorf("API request failed: %w", err)
	}

	return result, nil
}

func (d *DaySmart) convertToWarehouses(ctx context.Context, result GetAllWarehousesResp) []models.Warehouse {
	var warehouses []models.Warehouse

	for _, wh := range result.Data {
		warehouse, err := d.createWarehouseModel(ctx, wh)
		if err != nil {
			log.Warn(
				ctx,
				"failed to create warehouse model",
				zap.Int("locationID", wh.LocationID),
				zap.String("name", wh.Name),
				zap.Error(err),
			)
			continue
		}

		warehouses = append(warehouses, warehouse)
	}

	return warehouses
}

func (d *DaySmart) createWarehouseModel(ctx context.Context, wh LocationData) (models.Warehouse, error) {
	tz, err := timezone.GetTimezone(ctx, wh.City, wh.State, "")
	if err != nil {
		log.Warn(
			ctx,
			"failed to get timezone",
			zap.String("city", wh.City),
			zap.String("state", wh.State),
			zap.Error(err),
		)

		return models.Warehouse{}, err
	}

	return models.Warehouse{
		WarehouseID:             strconv.Itoa(wh.LocationID),
		WarehouseName:           wh.Name,
		WarehouseAddressLine1:   wh.Address1,
		WarehouseAddressLine2:   wh.Address2,
		WarehouseFullAddress:    formatAddress(wh.Address1, wh.Address2),
		WarehouseFullIdentifier: formatIdentifier(wh.Name, wh.Address1, wh.Address2),
		WarehouseTimezone:       tz,
		DefaultSubscribedEmail:  wh.Email,
		Source:                  models.DaySmartSource,
	}, nil
}

func formatAddress(address1, address2 string) string {
	return strings.TrimSpace(strings.Join([]string{address1, address2}, addressSeparator))
}

func formatIdentifier(name, address1, address2 string) string {
	return strings.TrimSpace(strings.Join([]string{name, address1, address2}, addressSeparator))
}
