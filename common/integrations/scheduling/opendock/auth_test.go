package opendock

import (
	"fmt"
	"testing"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const secretKey = "my-secret-key"

func TestIsExpiredNeutronJWT(t *testing.T) {
	t.Run("Not expired", func(t *testing.T) {
		token, err := generateToken(false)
		require.NoError(t, err)

		isExpired, err := isExpiredNeutronJWT(token)
		require.NoError(t, err)
		assert.False(t, isExpired)
	})

	t.Run("Expired", func(t *testing.T) {
		token, err := generateToken(true)
		require.NoError(t, err)

		isExpired, err := isExpiredNeutronJWT(token)
		require.NoError(t, err)
		assert.True(t, isExpired)
	})

	t.Run("Invalid token", func(t *testing.T) {
		isExpired, err := isExpiredNeutronJWT("invalid-jwt-token")
		assert.Error(t, err)
		assert.False(t, isExpired)
	})
}

func generateToken(isExpired bool) (string, error) {
	claims := jwt.StandardClaims{
		ExpiresAt: time.Now().Add(time.Hour).Unix(),
	}

	if isExpired {
		claims.ExpiresAt = time.Now().Add(2 * -time.Hour).Unix()
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secretKey))
}

// Parses and checks if the Neutron token is expired
func isExpiredNeutronJWT(tokenString string) (bool, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &jwt.StandardClaims{})
	if err != nil {
		return false, err
	}

	// Check if the token is valid
	if claims, ok := token.Claims.(*jwt.StandardClaims); ok {
		// Check if the token is expired
		expirationTime := time.Unix(claims.ExpiresAt, 0)

		return time.Now().After(expirationTime), nil
	}

	return false, fmt.Errorf("invalid token")
}
