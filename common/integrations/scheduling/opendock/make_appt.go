package opendock

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	AppointmentResponse struct {
		Data models.ScheduledAppointment `json:"data"`
	}
)

// NOTE: MakeAppointment schedules an appointment at the given warehouse (this can take 5-10 seconds).
// The warehouse can then confirm or deny the appointment request.
//
// https://neutron.opendock.com/docs#/Appointment/createOneBaseAppointmentControllerAppointment
func (o *Opendock) MakeAppointmentWithLoad(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ models.Load,
) (appt models.Appointment, err error) {

	if o.userID == "" {
		_, err = o.GetUser(ctx)
		if err != nil {
			return appt, fmt.Errorf("error getting userID: %w", err)
		}
	}

	req.UserID = o.userID

	var result AppointmentResponse
	err = o.post(ctx, "appointment", nil, req, &result)

	var status string
	if len(result.Data.Status) > 0 {
		if statusBytes, err := json.Marshal(result.Data.Status); err == nil {
			status = string(statusBytes)
		}
	}

	appt = models.Appointment{
		ConfirmationNo: result.Data.ConfirmationNumber,
		Date:           req.StartTime.Format(time.DateOnly),
		DockID:         req.DockID,
		ExternalID:     result.Data.ID,
		RefNumber:      result.Data.Request.RefNumber,
		StartTime:      req.StartTime,
		Status:         status,
		WarehouseID:    req.WarehouseID,
	}

	return appt, err
}

func (o *Opendock) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (appt models.Appointment, err error) {

	if o.userID == "" {
		_, err = o.GetUser(ctx)
		if err != nil {
			return appt, fmt.Errorf("error getting userID: %w", err)
		}
	}

	req.UserID = o.userID

	var result AppointmentResponse
	err = o.post(ctx, "appointment", nil, req, &result)

	var status string
	if len(result.Data.Status) > 0 {
		if statusBytes, err := json.Marshal(result.Data.Status); err == nil {
			status = string(statusBytes)
		}
	}

	appt = models.Appointment{
		ConfirmationNo: result.Data.ConfirmationNumber,
		Date:           req.StartTime.Format(time.DateOnly),
		DockID:         req.DockID,
		ExternalID:     result.Data.ID,
		RefNumber:      result.Data.Request.RefNumber,
		StartTime:      req.StartTime,
		Status:         status,
		WarehouseID:    req.WarehouseID,
	}

	return appt, err
}
