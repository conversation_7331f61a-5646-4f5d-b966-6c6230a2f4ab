package opendock

import (
	"context"
	"fmt"
	"net/url"
	"slices"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	// GetWarehousesResponse wraps the response for all warehouse retrievals.
	GetWarehousesResponse struct {
		Data []models.WarehouseDetails `json:"data"`
	}
)

var blacklistedWarehouses = []string{
	"10eeb1fe-40b0-4e66-8323-065af8767b3f", // OM Byram - OM | Carol Stream IL
}

func (o *Opendock) GetAllWarehouses(ctx context.Context) (warehouses []models.Warehouse, err error) {
	var result GetWarehousesResponse

	query := make(url.Values)
	query.Add(
		"fields",
		"id,timezone,name,street,city,state,zip,email,phone,timezone,settings,customApptFieldsTemplate",
	)

	path := "warehouse/"
	if err = o.get(ctx, path, query, &result); err != nil {
		return nil, fmt.Errorf("error getting all warehouses: %w", err)
	}

	var allWarehouses []models.Warehouse
	for _, wh := range result.Data {
		if slices.Contains(blacklistedWarehouses, wh.ID) {
			continue
		}

		whMainAddressLine := strings.Split(wh.Street, ",")[0]
		whSecondaryAddressLine := strings.Join([]string{wh.City, ", ", wh.State, " ", wh.Zip}, "")

		allWarehouses = append(allWarehouses, models.Warehouse{
			CustomApptFieldsTemplate: wh.CustomApptFieldsTemplate,
			DefaultSubscribedEmail:   wh.DefaultSubscribedEmail,
			Settings:                 wh.Settings,
			Source:                   models.OpendockSource,
			WarehouseAddressLine1:    whMainAddressLine,
			WarehouseAddressLine2:    whSecondaryAddressLine,
			WarehouseFullAddress: strings.Join(
				[]string{
					whMainAddressLine,
					whSecondaryAddressLine,
				},
				" ",
			),
			WarehouseFullIdentifier: strings.Join(
				[]string{
					wh.Name,
					whMainAddressLine,
					whSecondaryAddressLine,
				},
				" ",
			),
			WarehouseID:       wh.ID,
			WarehouseName:     wh.Name,
			WarehouseTimezone: wh.Timezone,
		})
	}

	return allWarehouses, nil
}
