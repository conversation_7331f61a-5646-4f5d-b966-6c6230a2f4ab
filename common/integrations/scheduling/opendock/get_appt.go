package opendock

import (
	"context"
	"encoding/json"
	"net/url"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

// https://neutron.opendock.com/docs#/Appointment/getOneBaseAppointmentControllerAppointment
func (o *Opendock) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	path := "appointment/" + url.PathEscape(id)

	var result AppointmentResponse
	if err := o.get(ctx, path, nil, &result); err != nil {
		return models.Appointment{}, err
	}

	var status string
	if len(result.Data.Status) > 0 {
		if statusBytes, err := json.Marshal(result.Data.Status); err == nil {
			status = string(statusBytes)
		}
	}

	record := models.Appointment{
		ExternalID:     result.Data.ID,
		ConfirmationNo: result.Data.ConfirmationNumber,
		WarehouseID:    result.Data.Request.WarehouseID,
		DockID:         result.Data.Request.DockID,
		RefNumber:      result.Data.Request.RefNumber,
		Date:           result.Data.Request.StartTime.Format(time.DateOnly),
		StartTime:      result.Data.Request.StartTime,
		Status:         status,
		Notes:          result.Data.Request.Notes,
	}

	return record, nil
}
