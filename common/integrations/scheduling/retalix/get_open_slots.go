package retalix

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func (r *Retalix) GetOpenSlots(
	ctx context.Context,
	_ string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	// Step 1: Initial setup and validation
	err := r.getApptFormViewState(ctx)
	if err != nil {
		return nil, err
	}

	err = r.postApptFormForValidation(ctx, req.PONumbers, *req.Warehouse)
	if err != nil {
		return nil, err
	}

	err = r.beginForceApptFormValidation(ctx, *req.Warehouse)
	if err != nil {
		return nil, err
	}

	// Step 2: Validate POs
	_, err = r.getValidatedApptForm(ctx, *req.Warehouse)
	if err != nil {
		return nil, err
	}

	// Step 3: Get schedule view
	doc, err := r.getScheduleView(ctx, *req.Warehouse)
	if err != nil {
		return nil, err
	}

	// Extract viewstate for subsequent requests
	vs := extractViewState(doc)
	if vs.State == "" || vs.Validation == "" || vs.Generator == "" {
		return nil, &Error{
			Type:    ValidationError,
			Message: "schedule view missing required form data",
		}
	}

	// Convert ViewState struct to map for getTimesForDate
	viewState := map[string]string{
		"viewState":          vs.State,
		"viewStateGenerator": vs.Generator,
		"eventValidation":    vs.Validation,
	}

	// Step 4: Get available dates and their times
	dates := r.getAvailableDates(doc)
	if len(dates) == 0 {
		return nil, &Error{
			Type:    ValidationError,
			Message: "no available appointment dates found",
		}
	}

	// Step 5: Build slots from dates and times
	var slots []models.Slot
	for _, date := range dates {
		times, err := r.getTimesForDate(ctx, date, *req.Warehouse, viewState)
		if err != nil {
			continue
		}

		if len(times) > 0 {
			slot := models.Slot{
				Warehouse:  *req.Warehouse,
				StartTimes: times,
			}

			slots = append(slots, slot)
		}
	}

	// Verify we found some slots
	if len(slots) == 0 {
		return nil, &Error{
			Type:    ValidationError,
			Message: "no appointment slots available",
		}
	}

	return slots, nil
}
