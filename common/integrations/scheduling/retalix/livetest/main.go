package main

import (
	"context"
	"flag"
	"fmt"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/integrations/scheduling/retalix"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

// Command line flags
var (
	username = flag.String("username", "", "Retalix username")
	password = flag.String("password", "", "Retalix password")
	// Example: 814
	warehouseID = flag.String("warehouse-id", "", "Warehouse ID (e.g., 814)")
	// Example: "Hy-Vee Food Stores, Inc. - Cumming Distribution Center (Cumming, IA )",
	warehouseName = flag.String("warehouse-name", "", "Full warehouse name")
	// Example: 215628
	poNumber = flag.String("po", "", "PO number to test with")
)

func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	client, err := buildClient(ctx)
	if err != nil {
		log.Fatal(ctx, "buildClient failed", zap.Error(err))
	}

	if err := validateAppointment(ctx, client); err != nil {
		log.Fatal(ctx, "validateAppointment failed", zap.Error(err))
	}

	slots, err := getOpenSlots(ctx, client)
	if err != nil {
		log.Fatal(ctx, "getOpenSlots failed", zap.Error(err))
	}

	if len(slots) < 1 {
		log.Fatal(
			ctx,
			"getOpenSlots failed",
			zap.Int("number of slots", len(slots)),
			zap.Error(err),
		)
	}

	if err := makeAppointment(ctx, client, slots[0]); err != nil {
		log.Fatal(ctx, "makeAppointment failed", zap.Error(err))
	}

	log.Info(ctx, "All tests passed!")
}

func buildClient(ctx context.Context) (*retalix.Retalix, error) {
	flag.Parse()

	// Validate required flags
	if *username == "" || *password == "" || *warehouseID == "" || *poNumber == "" {
		return nil, fmt.Errorf("missing required flags: username, password, warehouse-id, and po are required")
	}

	// Add warehouse name validation
	if *warehouseName == "" {
		log.Warn(ctx, "warehouse name not provided, some operations may fail")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, *password, nil)
	if err != nil {
		log.Error(ctx, "failed to encrypt retalix password", zap.Error(err))
	}

	integration := models.Integration{
		Name:              models.Retalix,
		Type:              models.Scheduling,
		Username:          *username,
		EncryptedPassword: []byte(encryptedPassword),
		ServiceID:         1,
	}

	ctx = log.With(ctx,
		zap.String("warehouseID", *warehouseID),
		zap.String("poNumber", *poNumber),
	)

	return retalix.New(ctx, integration)
}

func getOpenSlots(ctx context.Context, client *retalix.Retalix) ([]models.Slot, error) {
	warehouse := models.Warehouse{
		WarehouseID:   *warehouseID,
		WarehouseName: *warehouseName,
	}

	req := models.GetOpenSlotsRequest{
		PONumbers: []string{*poNumber},
		Warehouse: &warehouse,
	}

	log.Info(
		ctx,
		"Getting open slots",
		zap.String("warehouseID", warehouse.WarehouseID),
		zap.String("warehouseName", warehouse.WarehouseName),
		zap.Strings("poNumbers", req.PONumbers),
	)

	slots, err := client.GetOpenSlots(ctx, "", req)
	if err != nil {
		return []models.Slot{}, fmt.Errorf("GetOpenSlots failed: %w", err)
	}

	log.Info(ctx, "Retrieved slots", zap.Int("totalSlots", len(slots)))

	for _, slot := range slots {
		date := slot.StartTimes[0].Format("Monday, January 2, 2006")

		log.Debug(
			ctx,
			"Slot details",
			zap.String("date", date),
			zap.Int("timesAvailable", len(slot.StartTimes)),
			zap.Time("firstTime", slot.StartTimes[0]),
			zap.Time("lastTime", slot.StartTimes[len(slot.StartTimes)-1]),
		)
	}

	return slots, nil
}

func validateAppointment(ctx context.Context, client *retalix.Retalix) error {
	poNumbers := []string{*poNumber}
	warehouse := models.Warehouse{
		WarehouseID: *warehouseID,
	}

	_, err := client.ValidateAppointment(ctx, poNumbers, warehouse)

	return err
}

//nolint:unused
func submitAppointment(ctx context.Context, client *retalix.Retalix) error {
	poNumbers := []string{*poNumber}
	warehouse := models.Warehouse{
		WarehouseID: *warehouseID,
	}

	return client.SubmitAppointment(ctx, poNumbers, warehouse, false, "")
}

func makeAppointment(ctx context.Context, client *retalix.Retalix, slot models.Slot) error {
	warehouse := models.Warehouse{
		WarehouseID:   *warehouseID,
		WarehouseName: *warehouseName,
	}

	req := models.MakeAppointmentRequest{
		StartTime:  slot.StartTimes[0],
		DockID:     "",
		LoadTypeID: "",
		PONums:     *poNumber,
	}

	_, err := client.MakeAppointment(
		ctx,
		req,
		models.WithWarehouse(warehouse),
	)

	return err
}
