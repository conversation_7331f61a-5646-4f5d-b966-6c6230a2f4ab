package retalix

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/util/httplog"
)

func (r *Retalix) get(
	ctx context.Context,
	url string,
	_ io.Reader,
) ([]byte, []*http.Cookie, error) {

	return r.getWithHeaders(ctx, url, nil, map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	})
}

func (r *Retalix) getWithHeaders(
	ctx context.Context,
	url string,
	body io.Reader,
	headers map[string]string,
) ([]byte, []*http.Cookie, error) {

	return r.makeHTTPRequest(ctx, http.MethodGet, url, body, headers)
}

func (r *Retalix) postForm(
	ctx context.Context,
	url string,
	body io.Reader,
) ([]byte, []*http.Cookie, error) {

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	return r.postWithHeaders(ctx, url, body, headers)
}

func (r *Retalix) postWithHeaders(
	ctx context.Context,
	url string,
	body io.Reader,
	headers map[string]string,
) ([]byte, []*http.<PERSON>ie, error) {

	return r.makeHTTPRequest(ctx, http.MethodPost, url, body, headers)
}

func (r *Retalix) makeHTTPRequest(
	ctx context.Context,
	method string,
	url string,
	body io.Reader,
	headers map[string]string,
	_ ...func(htmlBody string) int,
) ([]byte, []*http.Cookie, error) {

	var req *http.Request

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create %s request for Retalix: %w", method, err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	resp, err := r.httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.scheduler, err)
		return nil, nil, fmt.Errorf("could not send %s request for Retalix: %w", method, err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, r.scheduler, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("error reading response body: %w", err)
	}

	return respBody, resp.Cookies(), err
}
