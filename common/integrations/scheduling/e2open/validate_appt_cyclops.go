package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	CyclopsValidateAppointmentRequest struct {
		models.CyclopsBaseRequest
		ProID      string `json:"proId"`
		FilterType string `json:"filterType,omitempty"`
	}

	ValidatedAppointment struct {
		AppointmentID string            `json:"appointmentId"`
		Reference     string            `json:"reference"`
		ScheduledTime string            `json:"scheduledTime"`
		Duration      int               `json:"duration"`
		Location      string            `json:"location"`
		Status        string            `json:"status"`
		Notes         string            `json:"notes"`
		Warehouse     Warehouse         `json:"warehouse"`
		Extended      ValidatedExtended `json:"extended"`
	}

	Warehouse struct {
		Name     string `json:"name"`
		City     string `json:"city"`
		State    string `json:"state"`
		ZipCode  string `json:"zipCode"`
		Country  string `json:"country"`
		Website  string `json:"website"`
		StopType string `json:"stopType"`
	}

	ValidatedExtended struct {
		IsFCFS bool `json:"isFcfs"`
	}

	ValidateAppointmentResponse struct {
		Success      bool                   `json:"success"`
		Message      string                 `json:"message"`
		Errors       []string               `json:"errors"`
		Appointments []ValidatedAppointment `json:"appointments"`
	}
)

func (e *E2open) ValidateAppointmentWithCyclops(
	ctx context.Context,
	poNumbers []string,
	_ models.Warehouse,
	opts *models.SchedulingOptions,
) ([]models.ValidatedPONumber, error) {

	cyclopsURL, err := util.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	validatedPOs := make([]models.ValidatedPONumber, 0, len(poNumbers))

	for _, poNumber := range poNumbers {
		cyclopsReq := CyclopsValidateAppointmentRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    E2openPlatform,
				Action:      models.ActionValidateAppointment,
				UserID:      e.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: e.creds.Username,
					Password: e.creds.Password,
				},
			},
			ProID:      poNumber,
			FilterType: "inbound",
		}

		reqBody, err := json.Marshal(cyclopsReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}

		httpReq, err := http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			cyclopsURL,
			bytes.NewBuffer(reqBody),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("Accept", "application/json")

		resp, err := otel.TracingHTTPClient().Do(httpReq)
		if err != nil {
			return nil, fmt.Errorf("failed to execute request: %w", err)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response: %w", err)
		}
		resp.Body.Close()

		var validationResp ValidateAppointmentResponse
		if err = json.Unmarshal(body, &validationResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		if !validationResp.Success {
			validatedPOs = append(validatedPOs, models.ValidatedPONumber{
				PONumber: poNumber,
				IsValid:  false,
				Error:    fmt.Sprintf("Validation failed: %s", validationResp.Message),
			})
			continue
		}

		validatedPO := models.ValidatedPONumber{
			PONumber: poNumber,
			IsValid:  true,
		}

		if len(validationResp.Appointments) == 0 {
			validatedPO.IsValid = false
			validatedPO.Error = "No appointments found for validation"
			validatedPOs = append(validatedPOs, validatedPO)
			continue
		}

		var relevantAppt *ValidatedAppointment
		switch opts.RequestType {
		case models.RequestTypePickup:
			for _, appt := range validationResp.Appointments {
				if appt.Warehouse.StopType == "pickup" {
					relevantAppt = &appt
					break
				}
			}
		case models.RequestTypeDropoff:
			for i := len(validationResp.Appointments) - 1; i >= 0; i-- {
				if validationResp.Appointments[i].Warehouse.StopType == "dropoff" {
					relevantAppt = &validationResp.Appointments[i]
					break
				}
			}
		}

		if relevantAppt == nil {
			validatedPO.IsValid = false
			validatedPO.Error = fmt.Sprintf("No %s appointment found", opts.RequestType)
			validatedPOs = append(validatedPOs, validatedPO)
			continue
		}

		if relevantAppt.Extended.IsFCFS {
			validatedPO.IsValid = false
			validatedPO.Error = fmt.Sprintf(
				"%s appointment requires FCFS scheduling",
				opts.RequestType,
			)
		}

		validatedPOs = append(validatedPOs, validatedPO)
	}

	return validatedPOs, nil
}
