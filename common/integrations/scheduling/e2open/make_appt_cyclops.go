package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		AppointmentData
		RequestType models.RequestType `json:"requestType"`
	}
)

func (e *E2open) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    E2openPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      e.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: e.creds.Username,
				Password: e.creds.Password,
			},
		},
		AppointmentData: AppointmentData{
			ProID:           req.LoadTypeID,
			AppointmentTime: req.StartTime.Format("2006-01-02T15:04:05Z07:00"),
		},
		RequestType: reqType,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := util.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var apptResp models.CyclopsMakeAppointmentResponse
	if err = json.Unmarshal(body, &apptResp); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !apptResp.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: apptResp.Message,
			Errors:  apptResp.Errors,
		}
	}

	return convertToAppointment(apptResp)
}
