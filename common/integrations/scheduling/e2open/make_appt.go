package e2open

import (
	"context"
	"encoding/json"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (e *E2open) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	return e.MakeAppointmentWithCyclops(ctx, req)
}

func (e *E2open) MakeAppointmentWithAPI(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	getAvailableApptReq := GetAvailableApptReq{}
	getAvailableApptReq.Identifiers.PrimaryReferenceNumber = req.RefNumber
	getAvailableApptReq.Identifiers.PoNumber = req.PONums

	var availableAppt GetAvailableApptResp
	queryParams := url.Values{}

	err := e.post(
		ctx,
		"/appointments/v1/fetch-available-appointments",
		queryParams,
		getAvailableApptReq,
		&availableAppt,
	)
	if err != nil {
		return models.Appointment{}, err
	}

	id := ""
	// considering that there is only one appointment available for the requested Primary Number and PoNumber
	if len(availableAppt.Appointments) > 0 {
		id = availableAppt.Appointments[0].ID
	}

	apptReq := MakeAppointmentReq{}
	apptReq.PreferredAppointment.ID = id

	var resp GetAppointmentResp
	err = e.post(ctx, "/appointments/v1/schedule-appointment", nil, apptReq, &resp)
	if err != nil {
		return models.Appointment{}, err
	}

	var status string
	if len(resp.Status) > 0 {
		if statusBytes, err := json.Marshal(resp.Status); err == nil {
			status = string(statusBytes)
		}
	}

	layout := "2006-01-02T15:04:05.000-07:00"
	startTime, err := time.Parse(layout, resp.Appointment.ArrivalWindow.StartDateTime)
	if err != nil {
		log.Error(ctx, "could not parse start time", zap.Error(err))
	}

	appt := models.Appointment{
		ExternalID:     resp.Appointment.ID,
		ConfirmationNo: resp.AppointmentConfirmationNumber,
		Date:           startTime.Format(time.DateOnly),
		StartTime:      startTime,
		Status:         status,
	}

	return appt, nil
}

func (e *E2open) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.E2open, "MakeAppointmentWithLoad")
}
