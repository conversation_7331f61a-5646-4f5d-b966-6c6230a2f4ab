package e2open

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

func (e *E2open) SubmitAppointment(
	ctx context.Context,
	poNumbers []string,
	warehouse models.Warehouse,
	_ bool,
	_ string,
	opts ...models.SchedulingOption,
) error {

	options := &models.SchedulingOptions{
		RequestType: models.RequestTypePickup,
	}
	options.Apply(opts...)

	if options.RequestedDate.IsZero() {
		return fmt.Errorf("requested date is required")
	}

	return e.SubmitAppointmentWithCyclops(ctx, poNumbers, warehouse, options)
}
