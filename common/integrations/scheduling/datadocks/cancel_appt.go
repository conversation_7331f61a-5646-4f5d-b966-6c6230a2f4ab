package datadocks

import (
	"context"
	"fmt"
	"net/url"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	apptPathFormat = "/api/v1/appointments/%s"
	canceledState  = "cancelled"
)

type CancelRequest struct {
	Appointment struct {
		State string       `json:"state"`
		Notes []CancelNote `json:"notes"`
	} `json:"appointment"`
}

type CancelNote struct {
	Body string `json:"body"`
}

func (d *DataDocks) CancelAppointment(ctx context.Context, id, reason string) (models.Appointment, error) {
	if err := validateCancelParams(id, reason); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid cancel parameters: %w", err)
	}

	req := buildCancelRequest(reason)

	var response AppointmentResp
	path := fmt.Sprintf(apptPathFormat, url.PathEscape(id))

	err := d.put(ctx, path, nil, req, &response)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to cancel appointment: %w", err)
	}

	return models.Appointment{}, err
}

func validateCancelParams(appointmentID, reason string) error {
	if appointmentID == "" {
		return fmt.Errorf("appointment ID is required")
	}

	if reason == "" {
		return fmt.Errorf("cancelation reason is required")
	}

	return nil
}

func buildCancelRequest(reason string) CancelRequest {
	var req CancelRequest
	req.Appointment.State = canceledState
	req.Appointment.Notes = []CancelNote{{Body: reason}}
	return req
}
