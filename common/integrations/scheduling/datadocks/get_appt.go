package datadocks

import (
	"context"
	"fmt"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	appointmentsEndpoint = "/api/v1/appointments"
	noteSeparator        = ","
)

func (d *DataDocks) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	if id == "" {
		return models.Appointment{}, fmt.<PERSON><PERSON><PERSON>("appointment ID is required")
	}

	appointments, err := d.fetchAppointments(ctx)
	if err != nil {
		return models.Appointment{}, fmt.<PERSON><PERSON><PERSON>("failed to fetch appointments: %w", err)
	}

	appointment, found := findAppointment(appointments, id)
	if !found {
		return models.Appointment{}, fmt.<PERSON><PERSON><PERSON>("appointment not found: %s", id)
	}

	return d.convertToAppointment(ctx, appointment, id)
}

func (d *DataDocks) fetchAppointments(ctx context.Context) ([]AppointmentResp, error) {
	var appointments []AppointmentResp
	if err := d.get(ctx, appointmentsEndpoint, nil, &appointments); err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("API request failed: %w", err)
	}

	return appointments, nil
}

func findAppointment(appointments []AppointmentResp, appointmentID string) (AppointmentResp, bool) {
	for _, appointment := range appointments {
		if appointmentID == strconv.Itoa(appointment.ID) {
			return appointment, true
		}
	}

	return AppointmentResp{}, false
}
