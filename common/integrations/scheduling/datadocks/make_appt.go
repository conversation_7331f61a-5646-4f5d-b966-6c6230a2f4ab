package datadocks

import (
	"context"
	"fmt"
	"strconv"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	createAppointmentEndpoint = "/api/v1/appointments"
	StateNeedsBooking         = "needs_booking"
	StatePending              = "pending"
	StateBooked               = "booked"
	StateArrived              = "arrived"
	StateStarted              = "started"
	StateCompleted            = "completed"
	StateLeft                 = "left"
	StateCancelled            = "cancelled"
)

type AppointmentRequest struct {
	Appointment struct {
		State       string            `json:"state"`
		Duration    float64           `json:"duration"`
		Outbound    bool              `json:"outbound"`
		DropTrailer bool              `json:"drop_trailer"`
		Queued      bool              `json:"queued"`
		Notes       []AppointmentNote `json:"notes"`
	} `json:"appointment"`
}

type AppointmentNote struct {
	Body string `json:"body"`
}

func (d *DataDocks) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	if err := validateAppointmentRequest(req); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid appointment request: %w", err)
	}

	resp, err := d.createAppointment(ctx, req)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create appointment: %w", err)
	}

	appointmentID := strconv.Itoa(resp.ID)

	return d.convertToAppointment(ctx, resp, appointmentID)
}

func validateAppointmentRequest(req models.MakeAppointmentRequest) error {
	if req.StartTime.IsZero() {
		return fmt.Errorf("start time is required")
	}

	if req.EndTime.IsZero() {
		return fmt.Errorf("end time is required")
	}

	if req.EndTime.Before(req.StartTime) {
		return fmt.Errorf("end time cannot be before start time")
	}

	return nil
}

func (d *DataDocks) createAppointment(ctx context.Context, req models.MakeAppointmentRequest) (AppointmentResp, error) {
	apptReq := buildAppointmentRequest(req)

	var resp AppointmentResp
	if err := d.post(ctx, createAppointmentEndpoint, nil, apptReq, &resp); err != nil {
		return AppointmentResp{}, fmt.Errorf("API request failed: %w", err)
	}

	return resp, nil
}

func buildAppointmentRequest(req models.MakeAppointmentRequest) AppointmentRequest {
	duration := req.EndTime.Sub(req.StartTime)

	var apptReq AppointmentRequest
	apptReq.Appointment.State = StateBooked
	apptReq.Appointment.Duration = duration.Minutes()
	apptReq.Appointment.Outbound = false
	apptReq.Appointment.DropTrailer = false
	apptReq.Appointment.Queued = false
	apptReq.Appointment.Notes = []AppointmentNote{{Body: req.Notes}}

	return apptReq
}

func (d *DataDocks) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.DataDocks, "MakeAppointmentWithLoad")
}
