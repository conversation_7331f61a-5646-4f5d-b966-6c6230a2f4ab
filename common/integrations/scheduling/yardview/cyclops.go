package yardview

import (
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	YardViewPlatform = "YardView"
)

type (
	AppointmentData struct {
		AppointmentID   string `json:"appointmentId"`
		ProID           string `json:"proId"`
		ContainerID     string `json:"containerId"`
		TrailerID       int    `json:"trailerId"`
		TrailerLicense  string `json:"trailerLicense"`
		VehicleNumber   string `json:"vehicleNumber"`
		VehicleLicense  string `json:"vehicleLicense"`
		AppointmentTime string `json:"appointmentTime"`
		Duration        int    `json:"duration"`
		Status          string `json:"status"`
		Notes           string `json:"notes"`
	}
)

func convertToAppointment(appt models.CyclopsAppointment) (models.Appointment, error) {
	scheduledTime, err := time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("invalid scheduled time format: %w", err)
	}

	return models.Appointment{
		ExternalID: appt.AppointmentID,
		StartTime:  scheduledTime,
		Status:     appt.Status,
	}, nil
}
