package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	MakeAppointmentRequestData struct {
		AppointmentPID  int                      `json:"appointmentPID" validate:"required"`
		AppointmentID   int                      `json:"appointmentID"`
		AppointmentKey  string                   `json:"appointmentKey"`
		ProID           string                   `json:"proId"`
		CarrierSCAC     string                   `json:"carrierSCAC"`
		StartTime       string                   `json:"startTime" validate:"required"`
		EndTime         string                   `json:"endTime" validate:"required"`
		Status          models.AppointmentStatus `json:"status" validate:"required"`
		AppointmentType models.RequestType       `json:"appointmentType" validate:"required"`
		Notes           string                   `json:"notes"`
		UpdatedAt       string                   `json:"updatedAt" validate:"required"`
		UpdatedBy       string                   `json:"updatedBy"`
		TrailerID       string                   `json:"trailerID"`
	}

	MakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointment MakeAppointmentRequestData `json:"appointment"`
	}
)

func (y *YardView) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.YardView, "MakeAppointmentWithLoad")
}

func (y *YardView) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	return y.MakeAppointmentWithCyclops(ctx, req)
}

func (y *YardView) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	warehouseID, err := strconv.Atoi(req.WarehouseID)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("valid warehouse ID required for make appointment: %w", err)
	}

	cyclopsReq := MakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    YardViewPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      y.scheduler.Username,
			Mode:        models.CyclopsModeAPI,
			Credentials: models.CyclopsCredentials{
				Username: y.creds.Username,
				Password: y.creds.Password,
			},
		},
		Appointment: MakeAppointmentRequestData{
			AppointmentPID:  warehouseID,
			ProID:           req.LoadTypeID,
			AppointmentType: reqType,
			StartTime:       req.StartTime.Format("2006-01-02T15:04:05Z07:00"),
			EndTime:         req.StartTime.Format("2006-01-02T15:04:05Z07:00"),
			Status:          "",
			Notes:           req.Notes,
			UpdatedAt:       time.Now().String(),
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := util.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsMakeAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return convertToAppointment(res.Appointment)
}
