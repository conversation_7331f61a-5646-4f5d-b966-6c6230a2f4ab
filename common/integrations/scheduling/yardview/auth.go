package yardview

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

func New(ctx context.Context, scheduler models.Integration) (*YardView, error) {
	if scheduler.Username == "" {
		return nil, fmt.Errorf("missing required username")

	}

	password, err := crypto.DecryptAESGCM(ctx, string(scheduler.EncryptedPassword), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt password")
	}

	creds := &Credentials{
		Username: scheduler.Username,
		Password: password,
	}

	return &YardView{scheduler: scheduler, creds: creds}, nil
}

func (y *YardView) OnboardScheduler(_ context.Context) (models.OnboardSchedulerResponse, error) {
	return models.OnboardSchedulerResponse{}, errtypes.NotImplemented(models.YardView, "OnboardScheduler")
}
