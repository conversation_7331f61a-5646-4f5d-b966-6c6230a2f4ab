package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	FilterType string

	GetSlotsRequest struct {
		models.CyclopsGetSlotsRequest
		FilterType FilterType `json:"filterType,omitempty"`
	}
)

const (
	ScheduleTypeInbound              = "Inbound"
	ScheduleTypeTargetAirFreightOnly = "TargetAirFreightOnly"
	ScheduleTypeTaxAirFreight        = "TaxAirFreight"
	ScheduleTypeElwood               = "Elwood"
)

func (s FilterType) IsValid() bool {
	switch s {
	case ScheduleTypeInbound, ScheduleTypeTargetAirFreightOnly, ScheduleTypeTaxAirFreight, ScheduleTypeElwood:
		return true
	default:
		return false
	}
}

func (y *YardView) GetOpenSlots(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	return y.GetOpenSlotsWithCyclops(ctx, loadTypeID, req)
}

func (y *YardView) GetOpenSlotsWithCyclops(
	ctx context.Context,
	_ string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	if req.RequestType == "" {
		req.RequestType = models.RequestTypePickup
	}

	if !req.RequestType.IsValid() {
		return nil, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	filter := FilterType(req.FilterType)
	if req.FilterType != "" && !filter.IsValid() {
		return nil, fmt.Errorf("invalid schedule filter: %s", filter)
	}

	cyclopsReq := GetSlotsRequest{
		CyclopsGetSlotsRequest: models.CyclopsGetSlotsRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionGetOpenSlots,
				UserID:      y.scheduler.Username,
				Mode:        models.CyclopsModeAPI,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
		},
		FilterType: filter,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := util.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetSlotsResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return convertToSlots(ctx, res.Appointments), nil
}

func convertToSlots(
	ctx context.Context,
	appointments []models.CyclopsAppointmentData,
) []models.Slot {

	ctx = log.With(ctx, zap.String("source", string(models.YardViewSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Status != "AVAILABLE" {
			continue
		}

		var times []time.Time

		t, err := time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
		if err != nil {
			log.Infof(
				ctx,
				"Invalid time format for YardView warehouse, ScheduledTime %s: %v",
				appt.ScheduledTime,
				zap.Error(err),
			)

			continue
		}
		times = append(times, t)

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.AppointmentID,
				},
				StartTimes: times,
			}
			slots = append(slots, slot)
		}
	}

	return slots
}
