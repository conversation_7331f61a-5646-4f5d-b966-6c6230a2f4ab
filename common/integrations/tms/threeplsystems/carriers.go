package threeplsystems

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (t ThreePLSystems) GetCarriers(ctx context.Context) (carriers []models.TMSCarrier, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCarriersThreePLSystems", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	var carriersResp []CarrierResp
	err = t.get(ctx, "", nil, &carriersResp, s3backup.TypeCarriers)
	if err != nil {
		return carriers, err
	}
	for _, data := range carriersResp {
		carrier := models.TMSCarrier{
			TMSIntegration:   t.tms,
			TMSIntegrationID: t.tms.ID,
			ServiceID:        t.tms.ServiceID,
		}
		if data.Dot != nil {
			carrier.DOTNumber = *data.Dot
		}
		carrier.Name = data.CarrierName
		carrier.Email = data.CarrierContactEmail
		carrier.Phone = *data.CarrierPhone
		carrier.AddressLine1 = data.CarrierAddress1
		if data.CarrierAddress2 != nil {
			carrier.AddressLine2 = *data.CarrierAddress2
		}
		carrier.City = data.CarrierCity
		carrier.State = data.CarrierState
		carrier.Country = data.CarrierCountry
		carrier.Zipcode = data.CarrierZip

		carriers = append(carriers, carrier)
	}
	return carriers, nil
}
