package threeplsystems

import (
	"context"
	"net/url"

	"github.com/aws/smithy-go/time"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	timezone2 "github.com/drumkitai/drumkit/common/util/timezone"
)

func (t ThreePLSystems) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (calls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(t.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryThreePLSystems", spanAttrs)
	defer func() { metaSpan.End(err) }()

	load, err := loadDB.GetLoadByID(ctx, loadID)
	if err != nil {
		return nil, err
	}

	queryParams := url.Values{}
	queryParams.Add("loadid", load.ExternalTMSID)

	var callResp []GetCheckCallResp
	err = t.get(ctx, "/api/v1/tracking", nil, &callResp, s3backup.TypeCheckCalls)
	if err != nil {
		log.Error(ctx, "Error getting check call", zap.Error(err))
		return calls, err
	}
	for _, data := range callResp {
		call := models.CheckCall{}

		timezone, err := timezone2.GetTimezone(ctx, data.ShipperCity, data.ShipperState, data.ShipperCountry)
		if err != nil {
			log.Warn(ctx, "Error getting time zone", zap.String("city", data.ShipperCity),
				zap.String("state", data.ShipperState), zap.String("country", data.ShipperCountry))
		}

		locationDateTime, timeErr := time.ParseDateTime(data.LastUpdateDate)
		if timeErr != nil {
			log.Error(ctx, "Error parsing time", zap.Error(err))
			return calls, err
		}

		call.LoadID = loadID
		call.FreightTrackingID = freightTrackingID
		call.Status = data.Status
		call.DateTime = models.NullTime{
			Time:  locationDateTime,
			Valid: true,
		}
		call.Timezone = timezone
		call.City = data.ShipperCity
		call.State = data.ShipperState

		calls = append(calls, call)
	}

	return calls, err
}
