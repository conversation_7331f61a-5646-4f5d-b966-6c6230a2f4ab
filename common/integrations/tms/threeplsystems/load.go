package threeplsystems

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/common/util/timezone"
)

func (t ThreePLSystems) GetLoadIDs(ctx context.Context, _ models.SearchLoadsQuery) (ids []string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadThreePLSystems", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	endPoint := "/api/clientv1/GetLoads"
	var loadResp []LoadResp
	err = t.get(ctx, endPoint, nil, &loadResp, s3backup.TypeLoads)
	if err != nil {
		return ids, err
	}

	for _, resp := range loadResp {
		ids = append(ids, strconv.Itoa(resp.LoadID))
	}

	return ids, nil
}

func (t ThreePLSystems) GetLoadsByIDType(
	ctx context.Context,
	id string,
	_ string,
) ([]models.Load, models.LoadAttributes, error) {
	load, attrs, err := t.GetLoad(ctx, id)
	return []models.Load{load}, attrs, err
}

func (t ThreePLSystems) GetLoad(ctx context.Context, shipmentID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.String("freight_tracking_id", shipmentID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadThreePLSystems", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := "/api/clientv1/GetLoads"
	queryParams := url.Values{}
	queryParams.Set("loadId", shipmentID)
	// todo : this request also need "StartDate" , "EndDate" and "Status" as query params.
	//   StartDate (DateTime): The start date of the date range for which to retrieve loads.
	//	 EndDate (DateTime): The end date of the date range for which to retrieve loads.
	//	 Status (string): The status filter for the loads. It can have specific values that alter the query behavior,
	//	 such as "dispatchnotcovered", "dispatch", "pending", "intransit", or "delivered".Other specific status values
	//	 are also supported as defined in the ShipmentStatuses enumeration.

	var loadResp LoadResp
	err = t.get(ctx, endPoint, queryParams, &loadResp, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}
	loadData := t.ThreePLSystemsShipmentToLoad(ctx, loadResp)

	return loadData, DefaultLoadAttributes, nil
}

func (t ThreePLSystems) CreateLoad(
	ctx context.Context,
	load models.Load,
	_ *models.TMSUser,
) (result models.Load, err error) {

	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadThreePLSystems", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody := t.LoadToThreePLSystemsShipmentReq(load)
	queryParams := url.Values{}
	// todo : need to confirm response type
	var response LoadResp
	err = t.post(ctx, "/api/v1/createshipment", queryParams, reqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return result, err
	}
	result = t.ThreePLSystemsShipmentToLoad(ctx, response)
	return result, nil
}

func (t ThreePLSystems) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	reqLoad *models.Load,
) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(*reqLoad)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadThreePLSystems", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody := t.LoadToThreePLSystemsShipmentReq(*reqLoad)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}
	type reqBodyWithID struct {
		ID int `json:"LoadId"`
		CreateUpdateLoadReq
	}
	shipmentIDInteger, err := strconv.Atoi(reqLoad.ExternalTMSID)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("error converting customer ID to int")
	}
	queryParams := url.Values{}

	var response UpdateLoadResp
	err = t.put(ctx, "/api/v1/UpdateShipment", queryParams,
		reqBodyWithID{shipmentIDInteger, reqBody}, &response, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("updating Load failed: %w", err)
	}
	reqLoad.ExternalTMSID = strconv.Itoa(response.LoadID)

	return *reqLoad, DefaultLoadAttributes, nil
}

func (t ThreePLSystems) ThreePLSystemsShipmentToLoad(ctx context.Context, resp LoadResp) (load models.Load) {
	load.ExternalTMSID = strconv.Itoa(resp.LoadID)
	load.Status = resp.ShipmentStatus
	load.ServiceID = t.tms.ServiceID
	load.Service = t.tms.Service
	if resp.ShipmentMode != nil {
		loadMode := models.StringToLoadMode(*resp.ShipmentMode)
		if loadMode == "" {
			log.Warn(
				ctx,
				"Unknown ThreePLSystems load mode",
				zap.String("mode", *resp.ShipmentMode),
				zap.String("loadID", load.ExternalTMSID),
			)
		}
		load.Mode = loadMode
	}

	// carrier
	if len(resp.Carriers) > 0 {
		pickUpStart, err := util.ParseDatetime(resp.PickupOpenTime)
		if err != nil {
			log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
		}
		pickUpEnd, err := util.ParseDatetime(resp.PickupCloseTime)
		if err != nil {
			log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
		}
		expectedPickup, err := util.ParseDatetime(resp.PickupDate)
		if err != nil {
			log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
		}
		expectedDelivery, err := util.ParseDatetime(resp.EstimatedDelivery)
		if err != nil {
			log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
		}
		var deliveryStart, deliveryEnd time.Time
		if resp.EstimatedDeliveryOpenTime != nil {
			deliveryStart, err = util.ParseDatetime(*resp.EstimatedDeliveryOpenTime)
			if err != nil {
				log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
			}
		}
		if resp.EstimatedDeliveryCloseTime != nil {
			deliveryEnd, err = util.ParseDatetime(*resp.EstimatedDeliveryCloseTime)
			if err != nil {
				log.Warn(ctx, "could not parse date time", zap.String("time string", resp.PickupOpenTime))
			}
		}

		load.Carrier.Name = resp.Carriers[0].CarrierName
		load.Carrier.MCNumber = resp.Carriers[0].CarrierMCNumber
		load.Carrier.SCAC = resp.Carriers[0].CarrierScac
		if resp.Carriers[0].CarrierContactEmail != nil {
			load.Carrier.Email = *resp.Carriers[0].CarrierContactEmail
		}
		if resp.Carriers[0].CarrierContactPhone != nil {
			load.Carrier.Phone = *resp.Carriers[0].CarrierContactPhone
		}
		load.Carrier.PickupStart = models.NullTime{
			Time:  pickUpStart,
			Valid: true,
		}
		load.Carrier.PickupEnd = models.NullTime{
			Time:  pickUpEnd,
			Valid: true,
		}
		load.Carrier.ExpectedPickupTime = models.NullTime{
			Time:  expectedPickup,
			Valid: true,
		}
		load.Carrier.ExpectedDeliveryTime = models.NullTime{
			Time:  expectedDelivery,
			Valid: true,
		}
		load.Carrier.DeliveryStart = models.NullTime{
			Time:  deliveryStart,
			Valid: true,
		}
		load.Carrier.DeliveryEnd = models.NullTime{
			Time:  deliveryEnd,
			Valid: true,
		}
	}

	// pickup
	for _, stop := range resp.Stops {
		switch stop.StopType {
		case "Pickup":
			load.Pickup.AddressLine1 = stop.Address1
			if stop.Address2 != nil {
				load.Pickup.AddressLine2 = *stop.Address2
			}
			load.Pickup.City = stop.City
			load.Pickup.State = stop.State
			load.Pickup.Country = stop.Country
			load.Pickup.Zipcode = stop.Zip
			timeZone, err := timezone.GetTimezone(ctx, stop.City, stop.State, stop.Country)
			if err != nil {
				log.Warn(ctx, "could not get timezone", zap.String("City", stop.City),
					zap.String("state", stop.State), zap.String("country", stop.Country))
			}
			load.Pickup.Timezone = timeZone
		case "Stop":
			if stop.Address2 != nil {
				load.Consignee.AddressLine2 = *stop.Address2
			}
			load.Consignee.AddressLine1 = stop.Address1
			load.Consignee.City = stop.City
			load.Consignee.State = stop.State
			load.Consignee.Country = stop.Country
			load.Consignee.Zipcode = stop.Zip
			timeZone, err := timezone.GetTimezone(ctx, stop.City, stop.State, stop.Country)
			if err != nil {
				log.Warn(ctx, "could not get timezone", zap.String("City", stop.City),
					zap.String("state", stop.State), zap.String("country", stop.Country))
			}
			load.Consignee.Timezone = timeZone
		}
	}

	// commodities
	load.Commodities = make([]models.Commodity, len(resp.Items))
	for i, item := range resp.Items {
		load.Commodities[i].Length = item.Length
		load.Commodities[i].Width = item.Width
		load.Commodities[i].Height = item.Height
		load.Commodities[i].WeightTotal = item.Weight
		load.Commodities[i].Quantity = item.Pieces
		load.Commodities[i].FreightClass = item.Class
		load.Commodities[i].HazardousMaterial = item.IsHazardous
		load.Commodities[i].NMFC = item.Nmfc
		if item.Packaging != nil {
			load.Commodities[i].PackagingType = *item.Packaging
		}
		if item.ProductDescription != nil {
			load.Commodities[i].Description = *item.ProductDescription
		}
	}

	return
}

func (t ThreePLSystems) LoadToThreePLSystemsShipmentReq(load models.Load) (req CreateUpdateLoadReq) {
	req.ShipmentStatus = load.Status
	req.PoReference = load.PONums

	// carrier details
	req.Carrier.CarrierScac = load.Carrier.SCAC

	// pickup details
	req.ShipperName = load.Pickup.Name
	req.ShipperPhone = load.Pickup.Phone
	req.ShipperEmail = load.Pickup.Email
	req.ShipperContact = load.Pickup.Contact
	req.ShipperAddress = load.Pickup.AddressLine1
	req.ShipperAddress2 = load.Pickup.AddressLine2
	req.ShipperCountry = load.Pickup.Country
	req.ShipperZip = load.Pickup.Zipcode

	req.PickupDate = load.Carrier.ExpectedPickupTime.Time.Format(time.RFC3339)
	req.PickupOpenTime = load.Carrier.PickupStart.Time.Format(time.RFC3339)
	req.PickupCloseTime = load.Carrier.PickupEnd.Time.Format(time.RFC3339)

	req.EstimatedDelivery = load.Carrier.ExpectedDeliveryTime.Time.Format(time.RFC3339)
	req.EstimatedDeliveryOpenTime = load.Carrier.DeliveryStart.Time.Format(time.RFC3339)
	req.EstimatedDeliveryCloseTime = load.Carrier.DeliveryEnd.Time.Format(time.RFC3339)

	// consignee details
	req.ConsigneeName = load.Consignee.Name
	req.ConsigneePhone = load.Consignee.Phone
	req.ConsigneeEmail = load.Consignee.Email
	req.ConsigneeContact = load.Consignee.Contact
	req.ConsigneeAddress = load.Consignee.AddressLine1
	req.ConsigneeAddress2 = load.Consignee.AddressLine2
	req.ConsigneeCountry = load.Consignee.Country
	req.ConsigneeZip = load.Consignee.Zipcode

	req.Items = make([]Item, len(load.Commodities))
	for i, com := range load.Commodities {
		req.Items[i].Length = com.Length
		req.Items[i].Width = com.Width
		req.Items[i].Height = com.Height
		req.Items[i].Weight = com.WeightTotal
		req.Items[i].Pieces = com.Quantity
		req.Items[i].Class = com.FreightClass
		req.Items[i].IsHazardous = com.HazardousMaterial
		req.Items[i].ProductDescription = com.Description
		req.Items[i].Packaging = com.PackagingType
	}

	return
}
