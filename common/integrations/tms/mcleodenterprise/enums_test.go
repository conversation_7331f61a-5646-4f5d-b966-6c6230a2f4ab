package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestToTMSCommodity(t *testing.T) {
	// Test trident tenant
	t.Run("trident fallback", func(t *testing.T) {
		tenant := "trident"
		llmCommodity := "Unknown Commodity"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "DRYVAN", commodity)
	})

	t.Run("trident exact match", func(t *testing.T) {
		tenant := "trident"
		llmCommodity := "tea/sugar"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "TEA", commodity)
	})

	t.Run("trident fuzzy match", func(t *testing.T) {
		tenant := "trident"
		llmCommodity := "steel container"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "CONTAINER", commodity)
	})

	t.Run("trident case insensitive match", func(t *testing.T) {
		tenant := "trident"
		llmCommodity := "WIRE MESH"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "WIREMESH", commodity)
	})

	// Test fetch tenant
	t.Run("fetch fallback", func(t *testing.T) {
		tenant := "fcfm"
		llmCommodity := "Unknown Commodity"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "VTRAILER", commodity)
	})

	t.Run("fetch exact match", func(t *testing.T) {
		tenant := "fcfm"
		llmCommodity := "53' Van Trailer"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "VTRAILER", commodity)
	})

	t.Run("fetch fuzzy match", func(t *testing.T) {
		tenant := "fcfm"
		llmCommodity := "van trailer"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "VTRAILER", commodity)
	})

	t.Run("fetch partial match", func(t *testing.T) {
		tenant := "fcfm"
		llmCommodity := "paper products"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "PAPER", commodity)
	})

	// Test unknown tenant
	t.Run("unknown tenant", func(t *testing.T) {
		tenant := "unknown"
		llmCommodity := "Some Commodity"
		commodity := ToTMSCommodity(tenant, llmCommodity)
		assert.Equal(t, "Some Commodity", commodity)
	})
}

// BenchmarkFuzzyMatchCommodity runs performance tests for the fuzzyMatchCommodity function
//
// Results:
// BenchmarkFuzzyMatchCommodity/exact_match-10             97019811                12.94 ns/op            0 B/op          0 allocs/op
// BenchmarkFuzzyMatchCommodity/fuzzy_match-10              4185802               286.0 ns/op            56 B/op          6 allocs/op
// BenchmarkFuzzyMatchCommodity/large_options_list-10         40242             29816 ns/op            7544 B/op        508 allocs/op
// BenchmarkFuzzyMatchCommodity/no_match-10                 4701547               254.3 ns/op            56 B/op          6 allocs/op
//
//nolint:lll
func BenchmarkFuzzyMatchCommodity(b *testing.B) {
	// Define benchmark test cases
	testCases := []struct {
		name     string
		input    string
		options  []TMSCommodity
		fallback string
	}{
		{
			name:  "exact match",
			input: "53' Van Trailer",
			options: []TMSCommodity{
				{Code: "VTRAILER", Description: "53' Van Trailer", Hazmat: "N", UN: ""},
				{Code: "STEEL", Description: "Steel", Hazmat: "N", UN: ""},
				{Code: "CARPET", Description: "Carpet", Hazmat: "N", UN: ""},
			},
			fallback: "UNKNOWN",
		},
		{
			name:  "fuzzy match",
			input: "van trailer",
			options: []TMSCommodity{
				{Code: "VTRAILER", Description: "53' Van Trailer", Hazmat: "N", UN: ""},
				{Code: "STEEL", Description: "Steel", Hazmat: "N", UN: ""},
				{Code: "CARPET", Description: "Carpet", Hazmat: "N", UN: ""},
			},
			fallback: "UNKNOWN",
		},
		{
			name:  "large options list",
			input: "steel container",
			options: append(
				fetchCommodityOptions,
				TMSCommodity{Code: "CUSTOM", Description: "Custom Commodity", Hazmat: "N", UN: ""},
			),
			fallback: "UNKNOWN",
		},
		{
			name:  "no match",
			input: "nonexistent commodity",
			options: []TMSCommodity{
				{Code: "VTRAILER", Description: "53' Van Trailer", Hazmat: "N", UN: ""},
				{Code: "STEEL", Description: "Steel", Hazmat: "N", UN: ""},
				{Code: "CARPET", Description: "Carpet", Hazmat: "N", UN: ""},
			},
			fallback: "UNKNOWN",
		},
	}

	// Run benchmarks for each test case
	for _, tc := range testCases {
		b.Run(tc.name, func(b *testing.B) {
			// Reset the timer to exclude setup time
			b.ResetTimer()

			// Run the benchmark
			for i := 0; i < b.N; i++ {
				fuzzyMatchCommodity(tc.input, tc.options, tc.fallback)
			}
		})
	}
}
