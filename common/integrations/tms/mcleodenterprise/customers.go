package mcleodenterprise

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (m *McleodEnterprise) GetCustomers(ctx context.Context) (customers []models.TMSCustomer, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	queryParams.Set("is_active", "Y")
	queryParams.Set("recordLength", fmt.Sprint(recordLength))
	recordOffset := 0

	// First, check if we have a saved job state in redis
	updatedAt, cursor, err := redis.GetIntegrationState(ctx, m.tms.ID, redis.CustomerJob)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
	}
	if updatedAt != "" {
		queryParams.Set("changedAfterDate", updatedAt)
	}
	if cursor != "" {
		if n, err := strconv.Atoi(cursor); err == nil {
			recordOffset = n
		} else {
			log.Warn(
				ctx,
				"invalid cursor format, using default offset",
				zap.Error(err),
				zap.Uint("integration_id", m.tms.ID),
				zap.String("cursor", cursor),
			)
		}
	}

	// if we haven't set the changedAfterDate query param by now, check the DB for the last update time
	if queryParams.Get("changedAfterDate") == "" {
		latestUpdatedAt, err := integrationDB.GetColumn(ctx, m.tms.ID, integrationDB.LastCustomerUpdatedAt)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(
					ctx,
					"record not found, fetching all customers for integration",
					zap.Uint("integration_id", m.tms.ID),
				)
			} else {
				log.WarnNoSentry(
					ctx,
					"failed to get integration state, fetching all customers for integration",
					zap.Error(err),
				)
			}
		} else {
			queryParams.Set("changedAfterDate", latestUpdatedAt.Format(time.RFC3339))
		}
	}

	for {
		queryParams.Set("recordOffset", fmt.Sprint(recordOffset))

		var customersResp []CustomerResp
		err = m.get(ctx, "/customers/search", queryParams, &customersResp, s3backup.TypeCustomers)
		if err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				m.tms.ID,
				redis.CustomerJob,
				queryParams.Get("changedAfterDate"),
				queryParams.Get("recordOffset"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		// var to store the fresh batch of retreived customers
		customersToRefresh := &[]models.TMSCustomer{}
		*customersToRefresh = make([]models.TMSCustomer, 0, len(customersResp))

		for _, data := range customersResp {
			var customer models.TMSCustomer
			customer.TMSIntegration = m.tms
			customer.TMSIntegrationID = m.tms.ID

			customer.Name = data.Name
			customer.ExternalTMSID = data.ID
			customer.AddressLine1 = data.Address1
			customer.AddressLine2 = data.Address2
			customer.City = data.City
			customer.State = data.StateID
			customer.Zipcode = data.ZipCode

			*customersToRefresh = append(*customersToRefresh, customer)
		}

		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, customersToRefresh); err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				m.tms.ID,
				redis.CustomerJob,
				queryParams.Get("changedAfterDate"),
				queryParams.Get("recordOffset"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		// after successfully upserting, add the customers to the function's return slice
		customers = append(customers, *customersToRefresh...)

		recordOffset += recordLength
		if len(customersResp) < recordLength {
			// delete the redis key once we've successfully reached the end of the job
			redis.RDB.Del(ctx, fmt.Sprintf("integration-id-%d-%s", m.tms.ID, redis.CustomerJob))
			break
		}
	}

	return customers, err
}
