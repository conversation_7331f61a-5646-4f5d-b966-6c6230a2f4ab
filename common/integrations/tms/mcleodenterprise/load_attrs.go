package mcleodenterprise

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

var DefaultLoadAttributes = models.LoadAttributes{
	LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
		Status:           models.FieldAttributes{},
		Mode:             models.FieldAttributes{},
		MoreThanTwoStops: models.FieldAttributes{},
		PONums:           models.FieldAttributes{}, // Same as Consignee.RefNumber
		Operator:         models.FieldAttributes{},
		Customer: models.CustomerAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				// Fields are read-only because user must select whole object, which then autofills related fields
				ExternalTMSID: models.FieldAttributes{},
				Name:          models.FieldAttributes{},
				AddressLine1:  models.FieldAttributes{IsReadOnly: true},
				AddressLine2:  models.FieldAttributes{IsReadOnly: true},
				City:          models.FieldAttributes{IsReadOnly: true},
				State:         models.FieldAttributes{IsReadOnly: true},
				Zipcode:       models.FieldAttributes{IsReadOnly: true},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{IsNotSupported: true},
				Email:         models.FieldAttributes{IsNotSupported: true},
			},
			RefNumber: models.FieldAttributes{},
		},
		BillTo: models.InitUnsupportedBillTo,
		Specifications: models.SpecificationsAttributes{
			OrderType:     models.FieldAttributes{IsReadOnly: true},
			Commodities:   models.FieldAttributes{},
			TotalPieces:   models.FieldAttributes{},
			TotalWeight:   models.FieldAttributes{},
			TotalDistance: models.FieldAttributes{IsReadOnly: true},

			BillableWeight:      models.FieldAttributes{IsNotSupported: true},
			NumCommodities:      models.FieldAttributes{IsNotSupported: true},
			TotalInPalletCount:  models.FieldAttributes{IsNotSupported: true},
			TotalOutPalletCount: models.FieldAttributes{IsNotSupported: true},
			MinTempFahrenheit:   models.FieldAttributes{IsNotSupported: true},
			MaxTempFahrenheit:   models.FieldAttributes{IsNotSupported: true},
			IsRefrigerated:      models.FieldAttributes{IsNotSupported: true},
			LiftgatePickup:      models.FieldAttributes{IsNotSupported: true},
			LiftgateDelivery:    models.FieldAttributes{IsNotSupported: true},
			InsidePickup:        models.FieldAttributes{IsNotSupported: true},
			InsideDelivery:      models.FieldAttributes{IsNotSupported: true},
			Tarps:               models.FieldAttributes{IsNotSupported: true},
			Oversized:           models.FieldAttributes{IsNotSupported: true},
			Hazmat:              models.FieldAttributes{IsNotSupported: true},
			Straps:              models.FieldAttributes{IsNotSupported: true},
			Permits:             models.FieldAttributes{IsNotSupported: true},
			Escorts:             models.FieldAttributes{IsNotSupported: true},
			Seal:                models.FieldAttributes{IsNotSupported: true},
			CustomBonded:        models.FieldAttributes{IsNotSupported: true},
			Labor:               models.FieldAttributes{IsNotSupported: true},
		},
		RateData: models.RateDataAttributes{
			CollectionMethod:       models.FieldAttributes{},                 // orderResp.CollectionMethodDescr
			RevenueCode:            models.FieldAttributes{},                 // orderResp.RevenueCodeID
			CustomerRateType:       models.FieldAttributes{},                 // orderResp.RateTypeDescr
			CustomerRateNumUnits:   models.FieldAttributes{IsReadOnly: true}, // orderResp.RateUnits
			CustomerLineHaulUnit:   models.FieldAttributes{},                 // toRateUnit(orderResp.RateTypeDescr)
			CustomerLineHaulRate:   models.FieldAttributes{},                 // orderResp.Rate
			CustomerLineHaulCharge: models.FieldAttributes{IsReadOnly: true},

			CarrierRateType:       models.FieldAttributes{},                 // movResp.OverrideTypeDescr
			CarrierRateNumUnits:   models.FieldAttributes{IsReadOnly: true}, // float32(movResp.OverrideUnits)
			CarrierLineHaulUnit:   models.FieldAttributes{IsReadOnly: true}, // toRateUnit(movResp.OverrideTypeDescr)
			CarrierLineHaulRate:   models.FieldAttributes{},                 // float32(movResp.OverridePayRate)
			CarrierLineHaulCharge: models.FieldAttributes{IsReadOnly: true},

			CustomerTotalCharge: models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			FSCPercent:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			FSCPerMile:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			CarrierCost:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			CarrierCostCurrency: models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			CarrierMaxRate:      models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			NetProfitUSD:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
			ProfitPercent:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: false},
		},

		Pickup: models.PickupAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{},
				Name:          models.FieldAttributes{},
				// Fields are read-only because user must select whole object, which then autofills related fields
				AddressLine1: models.FieldAttributes{IsReadOnly: true},
				AddressLine2: models.FieldAttributes{IsReadOnly: true},
				City:         models.FieldAttributes{IsReadOnly: true},
				State:        models.FieldAttributes{IsReadOnly: true},
				Zipcode:      models.FieldAttributes{IsReadOnly: true},
				Country:      models.FieldAttributes{IsNotSupported: true},
				// NOTE: GET order endpoint returns values, but GET locations does not. As a result,
				// when the user selects a location, the contact and phone fields are empty upon submission
				// but Mcleod may populate them in the returned order. This leads to false positive
				// "partial update" errors. So we just don't support these fields for now.
				Contact: models.FieldAttributes{IsNotSupported: true},
				Phone:   models.FieldAttributes{IsNotSupported: true},
				Email:   models.FieldAttributes{IsNotSupported: true},
			},
			ExternalTMSStopID: models.FieldAttributes{},
			BusinessHours:     models.FieldAttributes{IsNotSupported: true},
			// NOTE: Pickup and dropoff ref #s are read-only because updating them leads to exponential duplication
			// As in, passing in the existing array doesn't PUT but rather appends to the existing array
			// So far, only customer ref (=blnum) and PO # (=consignee_refno) are relevant for Trident
			RefNumber:     models.FieldAttributes{IsReadOnly: true},
			ReadyTime:     models.FieldAttributes{IsNotSupported: true},
			ApptRequired:  models.FieldAttributes{IsReadOnly: true},
			ApptType:      models.FieldAttributes{IsNotSupported: true},
			ApptStartTime: models.FieldAttributes{},
			ApptEndTime:   models.FieldAttributes{},
			ApptNote:      models.FieldAttributes{IsReadOnly: true},
			Timezone:      models.FieldAttributes{},
		},
		Consignee: models.ConsigneeAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{},
				Name:          models.FieldAttributes{},
				// Fields are read-only because user must select whole object, which then autofills related fields
				AddressLine1: models.FieldAttributes{IsReadOnly: true},
				AddressLine2: models.FieldAttributes{IsReadOnly: true},
				City:         models.FieldAttributes{IsReadOnly: true},
				State:        models.FieldAttributes{IsReadOnly: true},
				Zipcode:      models.FieldAttributes{IsReadOnly: true},
				Country:      models.FieldAttributes{IsNotSupported: true},
				// See note above in Pickup
				Contact: models.FieldAttributes{IsNotSupported: true},
				Phone:   models.FieldAttributes{IsNotSupported: true},
				Email:   models.FieldAttributes{IsNotSupported: true},
			},
			ExternalTMSStopID: models.FieldAttributes{},
			BusinessHours:     models.FieldAttributes{IsNotSupported: true},
			RefNumber:         models.FieldAttributes{}, // aka consignee_refno in API, PO # in UI
			MustDeliver:       models.FieldAttributes{IsNotSupported: true},
			ApptRequired:      models.FieldAttributes{IsReadOnly: true},
			ApptType:          models.FieldAttributes{IsNotSupported: true},
			ApptStartTime:     models.FieldAttributes{},
			ApptEndTime:       models.FieldAttributes{},
			ApptNote:          models.FieldAttributes{IsReadOnly: true},
			Timezone:          models.FieldAttributes{},
		},
		Carrier: models.CarrierAttributes{
			ExternalTMSID:        models.FieldAttributes{},
			Name:                 models.FieldAttributes{},
			DOTNumber:            models.FieldAttributes{IsReadOnly: true},
			Phone:                models.FieldAttributes{IsReadOnly: true},
			Email:                models.FieldAttributes{IsReadOnly: true},
			Dispatcher:           models.FieldAttributes{IsReadOnly: true},
			FirstDriverName:      models.FieldAttributes{},
			FirstDriverPhone:     models.FieldAttributes{},
			ExternalTMSTruckID:   models.FieldAttributes{},
			ExternalTMSTrailerID: models.FieldAttributes{},

			PickupStart:   models.FieldAttributes{IsReadOnly: true},
			PickupEnd:     models.FieldAttributes{IsReadOnly: true},
			DeliveryStart: models.FieldAttributes{IsReadOnly: true},
			DeliveryEnd:   models.FieldAttributes{IsReadOnly: true},

			MCNumber:                 models.FieldAttributes{IsNotSupported: true},
			SealNumber:               models.FieldAttributes{IsNotSupported: true},
			Notes:                    models.FieldAttributes{IsNotSupported: true},
			SCAC:                     models.FieldAttributes{IsNotSupported: true},
			SecondDriverName:         models.FieldAttributes{IsNotSupported: true},
			SecondDriverPhone:        models.FieldAttributes{IsNotSupported: true},
			DispatchCity:             models.FieldAttributes{IsNotSupported: true},
			DispatchState:            models.FieldAttributes{IsNotSupported: true},
			DispatchSource:           models.FieldAttributes{IsNotSupported: true},
			RateConfirmationSent:     models.FieldAttributes{IsNotSupported: true},
			ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true},
			ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true},
			DispatchedTime:           models.FieldAttributes{IsNotSupported: true},
			ExpectedPickupTime:       models.FieldAttributes{IsNotSupported: true},
			ExpectedDeliveryTime:     models.FieldAttributes{IsNotSupported: true},
			SignedBy:                 models.FieldAttributes{IsNotSupported: true},
		},
	},
}

func (m *McleodEnterprise) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&m.tms, &attrs)

	return attrs
}
