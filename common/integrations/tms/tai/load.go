package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (t Tai) GetLoadIDs(context.Context, models.SearchLoadsQuery) (
	[]string,
	error,
) {
	return nil, util.NotImplemented(models.Tai, "GetLoads")
}

func (t Tai) GetLoadsByIDType(ctx context.Context, id string, _ string) ([]models.Load, models.LoadAttributes, error) {
	load, attrs, err := t.GetLoad(ctx, id)
	return []models.Load{load}, attrs, err
}

func (t Tai) GetLoad(ctx context.Context, shipmentID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.String("freight_tracking_id", shipmentID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := fmt.Sprintf("/PublicApi/Shipping/v2/Shipments/%s", shipmentID)
	queryParams := url.Values{}
	var shipmentData ShipmentResp
	err = t.get(ctx, endPoint, queryParams, &shipmentData, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}

	loadData := t.TaiShipmentToLoad(shipmentData)
	loadData.FreightTrackingID = shipmentID

	return loadData, DefaultLoadAttributes, nil
}

func (t Tai) CreateLoad(ctx context.Context, load models.Load, _ *models.TMSUser) (result models.Load, err error) {
	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := t.LoadToTaiShipmentReq(load)
	if err != nil {
		return result, err
	}
	queryParams := url.Values{}
	var response LoadResponse
	err = t.post(ctx, "/PublicApi/Shipping/v2/Shipments", queryParams, reqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return result, err
	}
	result.FreightTrackingID = strconv.Itoa(response.ShipmentID)
	result.Status = response.Status

	return result, nil
}

func (t Tai) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	reqLoad *models.Load,
) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(*reqLoad)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := t.LoadToTaiShipmentReq(*reqLoad)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}
	type reqBodyWithID struct {
		ID int `json:"shipmentId"`
		LoadCreateOrUpdateRequest
	}
	shipmentIDInteger, err := strconv.Atoi(reqLoad.FreightTrackingID)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("error converting freight tracking ID to int")
	}
	queryParams := url.Values{}

	// TODO: need to change response type while testing
	// FIXME: Use different endpoints for different updates, similar to Relay
	var response ShipmentResp
	path := fmt.Sprintf("/PublicApi/Shipping/v2/ShipmentReferenceNumbers/%s", reqLoad.FreightTrackingID)
	err = t.put(ctx, path, queryParams, reqBodyWithID{shipmentIDInteger, reqBody}, &response, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("updating Load failed: %w", err)
	}
	result := t.TaiShipmentToLoad(response)
	result.FreightTrackingID = strconv.Itoa(response.ShipmentID)

	return result, DefaultLoadAttributes, nil
}

func (t Tai) TaiShipmentToLoad(shipmentData ShipmentResp) models.Load {
	var result models.Load

	result.FreightTrackingID = strconv.Itoa(shipmentData.ShipmentID)
	result.ExternalTMSID = strconv.Itoa(shipmentData.ShipmentID)
	result.Status = shipmentData.Status
	result.ServiceID = t.tms.ServiceID
	result.TMSID = t.tms.ID

	// Customer
	result.Customer.ExternalTMSID = strconv.Itoa(shipmentData.Customer.BillToOrganizationID)
	result.Customer.Name = shipmentData.Customer.Name
	result.Customer.RefNumber = shipmentData.Customer.ReferenceNumber
	result.Customer.Contact = shipmentData.Customer.SalesRepNames

	// BillTo
	result.BillTo.ExternalTMSID = strconv.Itoa(shipmentData.Customer.BillToOrganizationID)
	result.BillTo.Name = shipmentData.Customer.OfficeName

	// RateData
	netProfit := float32(shipmentData.TotalSell - shipmentData.TotalBuy)
	result.RateData.NetProfitUSD = netProfit
	carrierCost := float32(shipmentData.TotalBuy)
	result.RateData.CarrierCost = &carrierCost
	result.RateData.CarrierCostCurrency = "USD"
	// result.RateData.CustomerLHRateUSD = float32(shipmentData.TotalSell)
	// result.RateData.CarrierLHRateUSD = float32(shipmentData.TotalBuy)

	if shipmentData.TotalBuy > 0 {
		result.RateData.ProfitPercent = (netProfit / float32(shipmentData.TotalBuy)) * 100
	}

	// Carrier
	if len(shipmentData.CarrierList) > 0 {
		carrier := shipmentData.CarrierList[0]
		result.Carrier.ExternalTMSID = strconv.Itoa(carrier.CarrierMasterID)
		result.Carrier.Name = carrier.Name
		result.Carrier.DOTNumber = carrier.DotNumber
		result.Carrier.MCNumber = carrier.McNumber
		result.Carrier.SCAC = carrier.Scac
		result.Carrier.Phone = carrier.PhoneNumber
		result.Carrier.DispatchCity = carrier.City
		result.Carrier.DispatchState = carrier.State
		result.Carrier.FirstDriverPhone = shipmentData.DriverCellPhoneNumber
	}

	// Commodities
	var totalWeight float64
	for _, comm := range shipmentData.Commodities {
		var packagingType string
		switch v := comm.PackagingType.(type) {
		case string:
			packagingType = v
		case float64:
			packagingType = strconv.FormatFloat(v, 'f', -1, 64)
		case int:
			packagingType = strconv.Itoa(v)
		default:
			packagingType = ""
		}

		commodity := models.Commodity{
			HandlingQuantity:  comm.HandlingQuantity,
			PackagingType:     packagingType,
			WeightTotal:       comm.WeightTotal,
			HazardousMaterial: comm.HazardousMaterial,
			FreightClass:      comm.FreightClass,
			Description:       comm.Description,
		}
		result.Commodities = append(result.Commodities, commodity)
		totalWeight += comm.WeightTotal
	}

	// Update NumCommodities and TotalWeight
	result.Specifications.NumCommodities = len(result.Commodities)
	result.Specifications.TotalWeight = models.ValueUnit{Val: float32(totalWeight), Unit: shipmentData.WeightUnits}
	// QN: DimensinoUnits enums are  ['in', 'cm', 'ft', 'm'], unclear if they're used for mileage
	result.Specifications.TotalDistance = models.ValueUnit{Val: float32(shipmentData.Mileage), Unit: "mi"}

	// Stops
	for _, stop := range shipmentData.Stops {
		if stop.StopType == "First Pickup" {
			result.Pickup = mapStopToPickup(stop)
			result.Pickup.ReadyTime = stop.EstimatedReadyDateTime
			result.Carrier.ExpectedPickupTime = stop.AppointmentReadyDateTime
			result.Carrier.PickupStart = stop.ActualArrivalDateTime
			result.Carrier.PickupEnd = stop.ActualDepartureDateTime
		}
		if stop.StopType == "Last Drop" {
			result.Consignee = mapStopToConsignee(stop)
			result.Carrier.ExpectedDeliveryTime = stop.AppointmentReadyDateTime
			result.Carrier.DeliveryStart = stop.ActualArrivalDateTime
			result.Carrier.DeliveryEnd = stop.ActualDepartureDateTime
		}
	}

	// Additional fields
	result.DeclaredValueUSD = float32(shipmentData.TotalSell) // Assuming declared value is the total sell amount
	result.Specifications.TransportType = shipmentData.TrailerType
	result.Specifications.TransportSize = shipmentData.TrailerSize

	// Specifications
	if len(shipmentData.Commodities) > 0 {
		result.Specifications.Hazmat = shipmentData.Commodities[0].HazardousMaterial
	}

	return result
}

func mapStopToPickup(stop Stop) models.Pickup {
	return models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         stop.CompanyName,
			AddressLine1: stop.StreetAddress,
			AddressLine2: stop.StreetAddressTwo,
			City:         stop.City,
			State:        stop.State,
			Zipcode:      stop.ZipCode,
			Country:      stop.Country,
			Contact:      stop.ContactName,
			Phone:        stop.Phone,
			Email:        stop.Email,
		},
		ApptNote:      stop.Notes,
		RefNumber:     stop.ReferenceNumber,
		ReadyTime:     stop.EstimatedReadyDateTime,
		ApptStartTime: stop.AppointmentReadyDateTime,
		ApptEndTime:   stop.AppointmentCloseDateTime,
	}
}

func mapStopToConsignee(stop Stop) models.Consignee {
	return models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         stop.CompanyName,
			AddressLine1: stop.StreetAddress,
			AddressLine2: stop.StreetAddressTwo,
			City:         stop.City,
			State:        stop.State,
			Zipcode:      stop.ZipCode,
			Country:      stop.Country,
			Contact:      stop.ContactName,
			Phone:        stop.Phone,
			Email:        stop.Email,
		},
		ApptNote:      stop.Notes,
		RefNumber:     stop.ReferenceNumber,
		MustDeliver:   stop.EstimatedCloseDateTime,
		ApptStartTime: stop.AppointmentReadyDateTime,
		ApptEndTime:   stop.AppointmentCloseDateTime,
	}
}

func (t Tai) LoadToTaiShipmentReq(load models.Load) (LoadCreateOrUpdateRequest, error) {
	commodties := make([]CommodityReqForCreateOrUpdate, 0)

	for i := range load.Commodities {
		commodties = append(commodties, CommodityReqForCreateOrUpdate{
			HandlingQuantity:             float64(load.Commodities[i].HandlingQuantity),
			PackagingType:                load.Commodities[i].PackagingType,
			Length:                       load.Commodities[i].Length,
			Width:                        load.Commodities[i].Width,
			Height:                       load.Commodities[i].Height,
			WeightTotal:                  load.Commodities[i].WeightTotal,
			HazardousMaterial:            load.Commodities[i].HazardousMaterial,
			PiecesTotal:                  0,
			FreightClass:                 load.Commodities[i].FreightClass,
			NMFC:                         load.Commodities[i].NMFC,
			Description:                  load.Commodities[i].Description,
			AdditionalMarkings:           load.Commodities[i].AdditionalMarkings,
			UNNumber:                     load.Commodities[i].UNNumber,
			PackingGroup:                 load.Commodities[i].PackingGroup,
			ReferenceNumber:              load.Commodities[i].ReferenceNumber,
			HazmatCustomClassDescription: load.Commodities[i].HazmatCustomClassDescription,
			HazmatPieceDescription:       load.Commodities[i].HazmatPieceDescription,
			HarmonizedCode:               load.Commodities[i].HarmonizedCode,
			HazardClasses:                nil, // we have this but need to ask if the []string will be float here
		})
	}

	result := LoadCreateOrUpdateRequest{
		CustomerReferenceNumber:       load.Customer.RefNumber,
		TariffDescription:             "",
		AllowNewShipmentNotifications: true,
		IsCommitted:                   false,
		RateShipment:                  false,
		CarrierSCAC:                   load.Carrier.SCAC,
		Amount:                        0,
		ShipmentType:                  TruckloadShipmentType,
		Stackable:                     false,
		TrailerType:                   load.Specifications.TransportType,
		TrailerSize:                   load.Specifications.TransportSize,
		WeightUnits:                   "lbs",
		DimensionUnits:                "in",
		ServiceLevel:                  "Normal",
		ImportExport:                  "Import", // we need this in load req?
		ShipmentReferenceNumbers: []ReferenceNumber{
			{
				ReferenceType: "",
				Value:         load.Customer.RefNumber,
			},
		},
		Stops: []StopReqForCreateOrUpdate{
			{
				CompanyName:                  load.Pickup.Name,
				StreetAddress:                load.Pickup.AddressLine1,
				StreetAddressTwo:             load.Pickup.AddressLine2,
				City:                         load.Pickup.City,
				State:                        load.Pickup.State,
				ZipCode:                      load.Pickup.Zipcode,
				Country:                      load.Pickup.Country,
				ContactName:                  load.Pickup.Contact,
				Phone:                        load.Pickup.Phone,
				Fax:                          "",
				Email:                        load.Pickup.Email,
				Instructions:                 "",
				Notes:                        "",
				ReferenceNumber:              load.Pickup.RefNumber,
				AirportOrTerminalCode:        "",
				AppointmentReadyDateTime:     load.Pickup.ApptStartTime.Time,
				StopType:                     "",
				ShipmentStopReferenceNumbers: nil,
				EstimatedReadyDateTime:       load.Pickup.ReadyTime.Time,
			},
			{
				CompanyName:                  load.Consignee.Name,
				StreetAddress:                load.Consignee.AddressLine1,
				StreetAddressTwo:             load.Consignee.AddressLine2,
				City:                         load.Consignee.City,
				State:                        load.Consignee.State,
				ZipCode:                      load.Consignee.Zipcode,
				Country:                      load.Consignee.Country,
				ContactName:                  load.Consignee.Contact,
				Phone:                        load.Consignee.Phone,
				Fax:                          "",
				Email:                        load.Consignee.Email,
				Instructions:                 "",
				Notes:                        "",
				ReferenceNumber:              load.Pickup.RefNumber,
				AirportOrTerminalCode:        "",
				AppointmentReadyDateTime:     load.Pickup.ApptStartTime.Time,
				StopType:                     "",
				ShipmentStopReferenceNumbers: nil,
			},
		},
		Commodities:                  commodties,
		AccessorialCodes:             nil,
		ShipmentAlerts:               nil,
		DriverCellPhoneNumber:        load.Carrier.FirstDriverPhone,
		HazmatEmergencyContactNumber: "",
	}

	if load.Customer.ExternalTMSID != "" {
		conv, err := strconv.Atoi(load.Customer.ExternalTMSID)
		if err != nil {
			return LoadCreateOrUpdateRequest{}, fmt.Errorf("error converting customer ID: %w", err)
		}
		result.CustomerID = conv
	}

	return result, nil
}

func (t Tai) LoadToTaiShipmentReqSimple(load models.Load) (LoadCreateOrUpdateRequestSimple, error) {
	commodties := make([]CommodityReqForCreateOrUpdateSimple, 0)

	for i := range load.Commodities {
		commodties = append(commodties, CommodityReqForCreateOrUpdateSimple{
			WeightTotal:  load.Commodities[i].WeightTotal,
			Description:  load.Commodities[i].Description,
			PiecesTotal:  1,
			FreightClass: "No Class",
		})
	}

	customerID, err := strconv.Atoi(load.Customer.ExternalTMSID)
	if err != nil {
		return LoadCreateOrUpdateRequestSimple{}, fmt.Errorf("error converting customer ID: %w", err)
	}

	return LoadCreateOrUpdateRequestSimple{
		IsCommitted:  false,
		RateShipment: false,
		CustomerID:   customerID,
		ShipmentType: TruckloadShipmentType,
		WeightUnits:  "lbs",
		Stops: []StopReqForCreateOrUpdateSimple{
			{
				ZipCode:  load.Pickup.Zipcode,
				StopType: "First Pickup",
			},
			{
				ZipCode:  load.Consignee.Zipcode,
				StopType: "Last Drop",
			},
		},
		Commodities: commodties,
	}, nil
}
