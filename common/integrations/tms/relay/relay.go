package relay

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

// For unit testing
var (
	wsScheme = "wss"
)

type Relay struct {
	baseURL      string
	tms          models.Integration
	wsConn       *websocket.Conn
	wsConnMutex  sync.Mutex
	responseChan chan []byte
	errorChan    chan error
	doneChan     chan struct{}
}

type SDK interface {
	getLoadViaWebSocket(ctx context.Context, freightTrackingID string) (res []byte, err error)
}

type wsPayload struct {
	// Drumkit internal descriptor for debugging and readability
	OperationName string
	// Messages should be lists or structs
	Message []any
	// If you need to parse information in the nth response, specify how many responses reader should wait for.
	// Otherwise, set to 0 or 1 to continue once at least 1 response is received
	NumExpectedResponses int
}

func New(_ context.Context, tms models.Integration) *Relay {
	// To test Relay Staging, set tenant to training.relaytms.com
	// To test Relay prod, set tenant to relaytms.com

	return &Relay{tms: tms, baseURL: "https://" + tms.Tenant}
}

func (r *Relay) GetTestLoads() map[string]bool {
	isStaging := strings.Contains(r.tms.Tenant, "training")

	if isStaging {
		return map[string]bool{
			"*": true, // Wildcard key that will match any load ID in staging
		}
	}

	return nil
}

func (r *Relay) CreateLoad(context.Context, models.Load, *models.TMSUser) (models.Load, error) {
	return models.Load{}, errtypes.NotImplemented(r.tms.Name, "CreateLoad")
}

func (r *Relay) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, util.NotImplemented(models.Relay, "GetLocations")
}

func (r *Relay) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.Relay, "GetCarriers")
}

func (r *Relay) InitialOnboard(
	context.Context,
	models.Service,
	models.OnboardTMSRequest) (models.OnboardTMSResponse, error) {
	return models.OnboardTMSResponse{}, errtypes.NotImplemented(r.tms.Name, "InitialOnboard")
}

func (r *Relay) GetCustomers(_ context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, errtypes.NotImplemented(r.tms.Name, "GetCustomers")
}

func (r *Relay) CreateQuote(
	_ context.Context,
	_ models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, errtypes.NotImplemented(r.tms.Name, "CreateQuote")
}

func (r *Relay) getLoadViaWebSocket(ctx context.Context, freightTrackingID string) (res []byte, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getLoadViaWebSocketRelay", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	query := url.Values{}
	query.Set("token", "undefined")
	query.Set("vsn", "2.0.0")

	u := url.URL{Scheme: wsScheme, Host: r.tms.Tenant, Path: "socket/websocket", RawQuery: query.Encode()}

	header := http.Header{}
	header.Add("cookie", "_relay_web_key="+r.tms.APIKey)

	c, resp, err := websocket.DefaultDialer.Dial(u.String(), header)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return nil, err
	}
	defer c.Close()
	defer resp.Body.Close()

	messageChan := make(chan []byte, 2)

	go func() {
		defer close(messageChan)

		for {
			_, message, readErr := c.ReadMessage()
			if readErr != nil {
				// If expected close message, ignore error
				var closeErr *websocket.CloseError
				if errors.As(readErr, &closeErr) && closeErr.Code == websocket.CloseNormalClosure {
					log.Debugf(ctx, "websocket closed normally: ", readErr.Error())
					return
				}
				httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusInternalServerError)
				//nolint:revive // We want outer function to capture this error
				err = fmt.Errorf("error reading message: %w", readErr)
				return
			}
			log.Debugf(ctx, "recvd: ", string(message))

			messageChan <- message
		}

	}()

	connectMsg := `[
		"3",
		"3",
		"load_board_v2:all",
		"phx_join",
		{
			"apiVersion": null,
			"target_load_board_location_filter": "all",
			"ready_date_filter": "all",
			"pickup_date_filter": "all",
			"assignment_filter": "all",
			"filter_text": "",
			"sort_desc": true,
			"sort_by": "load_number"
		}
	]`

	log.Debug(ctx, "connecting to "+u.String())
	err = c.WriteMessage(websocket.TextMessage, []byte(connectMsg))
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return nil, fmt.Errorf("error writing first msg to ws: %w", err)
	}

	getLoadMsg := fmt.Sprintf(`
	[
		"3",
		"37",
		"load_board_v2:all",
		"load_board:filter_text_changed",
		{
			"filter_text": "%s"
		}
	]`, freightTrackingID)

	err = c.WriteMessage(websocket.TextMessage, []byte(getLoadMsg))
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return nil, fmt.Errorf("error writing getLoad msg to ws: %w", err)
	}

	select {
	// Read response to first message (connectMsg)
	case firstResp := <-messageChan:
		// Represent WS socket in HTTP request for capturing errors and metrics
		tempU := u
		tempU.RawQuery += "&messages[0]"
		req := &http.Request{URL: &tempU}

		if strings.Contains(string(firstResp), `"status":"error"`) || strings.Contains(string(firstResp), "phx_error") {
			httpResp := &http.Response{StatusCode: http.StatusUnauthorized}
			httplog.LogHTTPResponseCode(ctx, r.tms, httpResp.StatusCode)

			return res, errtypes.NewHTTPResponseError(r.tms, req, httpResp, firstResp)
		}
		if strings.Contains(string(firstResp), `"reason":"stale"`) {
			httpResp := &http.Response{StatusCode: http.StatusUnauthorized}
			err401 := errtypes.NewHTTPResponseError(r.tms, req, httpResp, firstResp)

			// Send separate Sentry notif so API key can be updated
			httplog.LogHTTPResponseCode(ctx, r.tms, httpResp.StatusCode)
			log.Error(ctx, "urgent: relay API key expired, update DB", zap.Error(err401))

			return res, err401
		}
		httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusOK)

		// Read response to second message (getLoadMsg) into result output
		// Caller performs additional checks on response body to verify and log status code as 200s may embed errors
		var moreMsgs bool
		res, moreMsgs = <-messageChan

		log.Debug(ctx, fmt.Sprint("[getLoad] more messages in channel (true expected): ", moreMsgs))

	case <-time.After(15 * time.Second):
		err = errors.New("getting websocket messages timed out")
	}

	// Cleanly close the connection by sending a close message and then
	// waiting (with timeout) for the server to close the connection.
	errClose := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	if errClose != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, errClose)
		return res, fmt.Errorf("error closing websocket: %w", errClose)
	}
	select {
	case _, moreMsgs := <-messageChan:
		log.Debug(ctx, fmt.Sprint("[getLoad] websocket closed. more messages (false expected): ", moreMsgs))

	case <-time.After(5 * time.Second):
		log.Info(ctx, "closing websocket timed out")
	}

	return res, err
}

func (r *Relay) initWebSocketConnection(ctx context.Context, u url.URL) error {
	r.wsConnMutex.Lock()
	defer r.wsConnMutex.Unlock()

	if r.wsConn != nil {
		return nil // Connection already exists
	}

	header := http.Header{}
	header.Add("cookie", "_relay_web_key="+r.tms.APIKey)

	c, resp, err := websocket.DefaultDialer.Dial(u.String(), header)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return fmt.Errorf("error dialing websocket: %w", err)
	}
	defer resp.Body.Close()

	r.wsConn = c
	r.doneChan = make(chan struct{})
	r.responseChan = make(chan []byte, 10)
	r.errorChan = make(chan error, 1)

	go r.readPump(ctx) // Start reading messages in a separate goroutine

	return nil
}

func (r *Relay) closeWebSocketConnection(ctx context.Context) error {
	r.wsConnMutex.Lock()
	defer r.wsConnMutex.Unlock()

	if r.wsConn == nil {
		return nil
	}

	err := r.wsConn.WriteMessage(websocket.CloseMessage,
		websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))

	if err != nil && websocket.IsUnexpectedCloseError(err, websocket.CloseNormalClosure) {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		log.WarnNoSentry(ctx, "error closing websocket", zap.Error(err))

		return err
	}

	select {
	case _, ok := <-r.doneChan:
		log.Debug(ctx, fmt.Sprint("[closeWebSocketConnection] websocket closed. more messages (false expected): ", ok))
		if ok {
			log.WarnNoSentry(ctx, "[closeWebSocketConnection] doneChan not closed")
		}

	case <-time.After(5 * time.Second):
		log.WarnNoSentry(ctx, "[closeWebSocketConnection] closing websocket timed out")
	}

	err = r.wsConn.Close()
	r.wsConn = nil

	return err
}

// readPump pumps messages from the websocket connection to the Relay instance.
func (r *Relay) readPump(ctx context.Context) {
	defer close(r.doneChan)
	defer close(r.responseChan)
	defer close(r.errorChan)

	for {
		_, message, readErr := r.wsConn.ReadMessage()
		if readErr != nil {
			if websocket.IsUnexpectedCloseError(readErr, websocket.CloseNormalClosure) {
				httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusInternalServerError)
				log.ErrorNoSentry(ctx, "error reading message", zap.Error(readErr))
				r.errorChan <- readErr

				return
			}
			log.Debug(ctx, "websocket closed normally: "+readErr.Error())
			return
		}
		r.responseChan <- message
	}
}

func (r *Relay) sendWebSocketMessages(
	ctx context.Context,
	closeConnection bool,
	path string,
	queryParams url.Values,
	isPostPatch bool,
	messages ...wsPayload,
) (responses []byte, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "sendWebSocketMessages", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	ctx = log.With(ctx, zap.String("wsPath", path))
	u := url.URL{Scheme: wsScheme, Host: r.tms.Tenant, Path: path, RawQuery: queryParams.Encode()}

	err = r.initWebSocketConnection(ctx, u)
	if err != nil {
		return nil, err
	}

	if closeConnection {
		defer func() {
			errClose := r.closeWebSocketConnection(ctx)
			if errClose != nil {
				log.Error(ctx, "error closing websocket", zap.Error(err))
			}
		}()
	}

	responses = make([]byte, 0, len(messages))
	for i, msg := range messages {
		ctx = log.With(ctx, zap.String("operationName", msg.OperationName), zap.Any("msgOrder", i))

		msgBodyBefore, err := json.Marshal(msg.Message)
		if err != nil {
			return nil, fmt.Errorf("error marshaling message %d: %w", i, err)
		}
		msgBody := []byte(strings.ReplaceAll(string(msgBodyBefore), "\\u0026", "&"))

		method := util.Ternary(isPostPatch, "POST", "GET")
		// NOTE: log.With() in api/routes/middleware already logs userEmail attr as long as context is persisted
		log.Infof(
			ctx,
			"[AUDIT] %s [Op: %s] - Relay WS message body",
			method,
			msg.OperationName,
			zap.String("wsMsgBody", string(msgBody)))

		err = r.wsConn.WriteMessage(websocket.TextMessage, msgBody)
		if err != nil {
			httplog.LogHTTPRequestFailed(ctx, r.tms, err)
			return nil, fmt.Errorf("error writing message %d to ws: %w", i, err)
		}

		// Represent WS socket in HTTP request for capturing errors and metrics
		tempU := u
		tempU.RawQuery += fmt.Sprintf("&messages[%d]", i)
		req := &http.Request{URL: &tempU}

		var messageResponses []byte
		countResponses := 0
		const perMsgTimeout = 3 * time.Second
		timer := time.NewTimer(perMsgTimeout)
		functionTimeout := time.After(20 * time.Second)

		// Wait for each message to receive expected number of responses
		// (Some messages receive multiple responses, and sometimes the information we need
		// is embedded in the nth response, as is the case with SchedulePlan)
	messageLoop:
		for {
			select {
			case resp := <-r.responseChan:
				messageResponses = append(messageResponses, resp...)
				countResponses++
				log.Debugf(ctx, "received response %d/%d", countResponses, msg.NumExpectedResponses)

				// NOTE: phx_errors typically mean that the request had invalid input (aka an HTTP 400)
				if strings.Contains(string(resp), `"status":"error"`) || strings.Contains(string(resp), "phx_error") {
					httpResp := &http.Response{StatusCode: http.StatusBadRequest}
					httplog.LogHTTPResponseCode(ctx, r.tms, httpResp.StatusCode)

					return responses, errtypes.NewHTTPResponseError(r.tms, req, httpResp, resp)
				}

				if strings.Contains(string(resp), `"reason":"stale"`) {
					httpResp := &http.Response{StatusCode: http.StatusUnauthorized}
					err401 := errtypes.NewHTTPResponseError(r.tms, req, httpResp, resp)

					// Send separate Sentry notif so API key can be updated
					httplog.LogHTTPResponseCode(ctx, r.tms, httpResp.StatusCode)
					log.Error(ctx, "urgent: relay API key expired, update DB", zap.Error(err401))

					return responses, errtypes.NewHTTPResponseError(r.tms, req, httpResp, resp)
				}
				httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusOK)

				if countResponses >= msg.NumExpectedResponses {
					break messageLoop
				}

			case <-timer.C:
				if countResponses < msg.NumExpectedResponses {
					httpResp := &http.Response{StatusCode: http.StatusRequestTimeout}
					httplog.LogHTTPResponseCode(ctx, r.tms, httpResp.StatusCode)

					// NOTE: Timeouts typically mean that the request had invalid input (aka an HTTP 400)
					return responses, errtypes.NewHTTPResponseError(r.tms, req, httpResp, []byte(fmt.Sprintf(
						"getting websocket response for msg timed out after receiving only %d/%d responses",
						countResponses, msg.NumExpectedResponses)))
				}
				log.Debugf(ctx, "individual timer timed out for message %d but we already have %d resps",
					i, countResponses)

				break messageLoop

			case err := <-r.errorChan:
				return responses, fmt.Errorf("error on message %d, %s: %w", i, msg.OperationName, err)

			case <-functionTimeout:
				return responses, errors.New("websocket timed out")
			}
		}

		responses = append(responses, messageResponses...)
	}

	return responses, nil
}

func (r *Relay) getHTML(
	ctx context.Context,
	path string,
	query url.Values,
	dataType s3backup.DataType) ([]byte, error) {

	addr := url.URL{Scheme: "https", Host: r.tms.Tenant, Path: path}
	if query != nil {
		addr.RawQuery = query.Encode()
	}
	headerMap := make(map[string]string)
	headerMap["cookie"] = "_relay_web_key=" + r.tms.APIKey
	headerMap["accept"] = "*/*"
	//nolint:bodyclose // body already closed in func
	body, resp, err := httputil.GetBytesWithToken(ctx, r.tms, addr, headerMap, nil, dataType)

	bodyStr := strings.ToLower(string(body))
	if strings.Contains(bodyStr, "please login") || strings.Contains(bodyStr, "sign in with google") {
		resp.StatusCode = http.StatusUnauthorized
		err401 := errtypes.NewHTTPResponseError(r.tms, &http.Request{Method: http.MethodGet, URL: &addr}, resp, body)

		// Send separate Sentry notif so API key can be updated
		log.Error(ctx, "urgent: relay API key expired, update DB", zap.Error(err401))
		httplog.LogHTTPResponseCode(ctx, r.tms, resp.StatusCode)

		return body, err401
	}

	return body, err
}
