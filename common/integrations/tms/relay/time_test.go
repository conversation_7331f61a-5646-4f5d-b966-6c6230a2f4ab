package relay

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestParseTime(t *testing.T) {
	ctx := context.Background()
	defer func() { timeNowFunc = time.Now }()

	pstLoc, err := time.LoadLocation("America/Los_Angeles")
	require.NoError(t, err)

	chicagoLoc, err := time.LoadLocation("America/Chicago")
	require.NoError(t, err)

	tests := []struct {
		name          string
		timeStr       string
		future        bool
		mockNow       time.Time
		expectedDate  models.NullTime
		expectedError error
		tz            *time.Location
	}{
		// == No Year, Past Date Tests (future: false) ==
		{
			name:          "Past date, same year",
			timeStr:       "6/20 20:20",
			future:        false,
			mockNow:       time.Date(2024, 7, 1, 12, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2024, 6, 20, 20, 20, 0, 0, time.UTC), Valid: true},
			expectedError: nil,
		},
		{
			name:          "Past date, previous year (year-end crossover)",
			timeStr:       "12/31 23:59",
			future:        false,
			mockNow:       time.Date(2025, 1, 1, 0, 1, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2024, 12, 31, 23, 59, 0, 0, time.UTC), Valid: true},
			expectedError: nil,
		},
		// == No Year, Future Date Tests (future: true) ==
		{
			name:          "Future date, same year",
			timeStr:       "8/15 10:00",
			future:        true,
			mockNow:       time.Date(2024, 7, 1, 10, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2024, 8, 15, 10, 0, 0, 0, time.UTC), Valid: true},
			expectedError: nil,
		},
		{
			name:          "Future date, next year (year-end crossover)",
			timeStr:       "1/5 08:00",
			future:        true,
			mockNow:       time.Date(2024, 12, 31, 18, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2025, 1, 5, 8, 0, 0, 0, time.UTC), Valid: true},
			expectedError: nil,
		},
		{
			name:          "Override timezone",
			timeStr:       "1/5 08:00",
			future:        true,
			mockNow:       time.Date(2024, 12, 31, 18, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2025, 1, 5, 8, 0, 0, 0, chicagoLoc), Valid: true},
			expectedError: nil,
			tz:            chicagoLoc,
		},
		// == Has Year & Timezone ==
		{
			name:          "Year & Timezone Chicago",
			timeStr:       "1/5/2025 08:00 CST",
			future:        true,
			mockNow:       time.Date(2024, 12, 31, 18, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2025, 1, 5, 8, 0, 0, 0, chicagoLoc), Valid: true},
			expectedError: nil,
		},
		{
			name:          "Year & Timezone Los Angeles",
			timeStr:       "1/5/2025 08:00 PST",
			future:        true,
			mockNow:       time.Date(2024, 12, 31, 18, 0, 0, 0, time.UTC),
			expectedDate:  models.NullTime{Time: time.Date(2025, 1, 5, 8, 0, 0, 0, pstLoc), Valid: true},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock time.Now()
			timeNowFunc = func() time.Time { return tt.mockNow }

			parsedTime, err := parseTime(ctx, tt.timeStr, tt.future, tt.tz)
			require.NoError(t, err)
			require.True(t, parsedTime.Valid)

			assert.Equal(t, tt.expectedDate.Time, parsedTime.Time)
			assert.Equal(t, tt.expectedError, err)
		})
	}

}
