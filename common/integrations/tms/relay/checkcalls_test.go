package relay

import (
	"context"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const testBookingID = "7004096"

func TestLivePostCheckCall(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLivePostCheckCall: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS, Tenant: "training.relaytms.com"}
	r := New(ctx, tms)

	var fetchedLoad models.Load
	fetchedLoad, _, err := r.GetLoad(ctx, testLoadID)
	require.NoError(t, err)

	vTrue := true
	vFalse := false

	t.Run("Note", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID: fetchedLoad.FreightTrackingID,
			Status:            "Add Tracking Note",
			Notes:             "test note @ " + dt.Format(time.DateTime),
			Source:            "dispatcher",
			IsOnTime:          &vTrue,
			IsException:       &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "Note Captured"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Dispatch", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Dispatch Driver",
			Notes:                      "test dispatch @ " + dt.Format(time.DateTime),
			NextStopETAWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			Source:                     "dispatcher",
			IsOnTime:                   &vTrue,
			City:                       "Lexington",
			State:                      "KY",
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "Driver Dispatched"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("In Transit", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			City:                       "Chicago",
			State:                      "IL",
			Status:                     "In Transit Update",
			Reason:                     "",
			Notes:                      "test check call @ " + dt.Format(time.DateTime),
			Author:                     "",
			DateTime:                   models.NullTime{},
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{},
			Timezone:                   "",
			NextStopETAWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			NextStopID:                 "pickup",
			CapturedDatetime:           models.NullTime{},
			Source:                     "dispatcher",
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "In Transit"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Arrived at pickup", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:       fetchedLoad.FreightTrackingID,
			Status:                  "Mark Arrived at Pickup",
			Reason:                  "",
			Notes:                   "test at pickup check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone: models.NullTime{Time: dt, Valid: true},
			Source:                  "dispatcher",
			// IsOnTime:                nil,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		time.Sleep(200 * time.Millisecond)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Arrival At Stop"
		cc.City = "Stop 1"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Loaded", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Mark Loaded",
			Reason:                     "",
			Notes:                      "test loaded check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			Source:                     "dispatcher",
			// IsOnTime:                   &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Marked Loaded"
		cc.City = "Allbirds, Shepherdsville, KY"
		cc.State = "Shepherdsville, KY"
		// NOTE: Mark Loaded/Delivered produces 2 check calls on Relay's end:
		// 1 Arrival At Stop, and 1 Marked Loaded. Only the former actually retains the OnTime information
		cc.IsOnTime = nil
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Arrived at dropoff", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:       fetchedLoad.FreightTrackingID,
			Status:                  "Mark Arrived at Delivery",
			Reason:                  "",
			Notes:                   "test at dropoff check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone: models.NullTime{Time: dt, Valid: true},
			Source:                  "dispatcher",
			IsOnTime:                &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Arrival at Stop"
		cc.City = "Stop 2"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Delivered", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Mark Delivered",
			Reason:                     "",
			Notes:                      "test delivered check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour)},
			Source:                     "dispatcher",
			IsOnTime:                   &vTrue,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Relay prefixes statuses with "Marked" so update the expected object
		cc.Status = "Marked Delivered"
		// NOTE: Mark Loaded/Delivered produces 2 check calls on Relay's end:
		// 1 Arrival At Stop, and 1 Marked Delivered. Only the former actually retains the OnTime information
		cc.IsOnTime = nil
		checkTest(ctx, t, checkCalls, cc)
	})

}

func checkTest(ctx context.Context, t *testing.T, checkCalls []models.CheckCall, expected models.CheckCall) {

	var res models.CheckCall
	for i, c := range checkCalls {
		if strings.EqualFold(c.Notes, expected.Notes) {
			log.Infof(ctx, "found submitted check call at index %d", i)
			res = c
			break
		}
	}

	require.NotEmpty(t, res)
	assert.NotEmpty(t, res.CapturedDatetime)
	assert.Equal(t, expected.Status, res.Status)
	assert.Equal(t, expected.Source, res.Source)
	assert.Equal(t, expected.Notes, res.Notes)
	assert.Equal(t, expected.City, res.City)
	assert.Equal(t, expected.State, res.State)

	assert.True(t, res.DateTimeWithoutTimezone.Equal(expected.DateTimeWithoutTimezone),
		"expected: %s, actual: %s", expected.DateTimeWithoutTimezone, res.DateTimeWithoutTimezone)
	assert.True(t, res.EndDateTimeWithoutTimezone.Equal(expected.EndDateTimeWithoutTimezone),
		"expected: %s, actual: %s", expected.EndDateTimeWithoutTimezone, res.EndDateTimeWithoutTimezone)

	if expected.IsException != nil {
		assert.Equal(t, *expected.IsException, *res.IsException)
	}
	if expected.IsOnTime != nil {
		assert.Equal(t, *expected.IsOnTime, *res.IsOnTime)
	}

}

func TestGetCheckCalls(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)
	html, err := os.ReadFile("./testdata/checkcalls.html")
	require.NoError(t, err)

	checkCalls, err := r.parseCheckCallHTML(ctx, html, 123, "FREIGHT123")
	require.NoError(t, err)

	loc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	// vTrue := true
	vFalse := false

	expected := []models.CheckCall{
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "dispatcher",
			City:              "",
			State:             "",
			Status:            "Marked Delivered",
			Reason:            "",
			Timezone:          "",
			Notes:             "",
			Author:            "",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 16, 19, 0, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 17, 9, 33, 0, 0,
				loc), Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "dispatcher",
			City:              "SOUTHHAVEN WAREHOUSE",
			State:             "Southaven, MS",
			Status:            "Stop Marked Delivered",
			Reason:            "",
			Timezone:          "",
			Notes:             "",
			Author:            "Jane Doe",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 16, 18, 0, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 16, 19, 0, 0, 0, time.UTC),
				Valid: true},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 17, 9, 33, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:                     123,
			FreightTrackingID:          "FREIGHT123",
			Source:                     "dispatcher",
			City:                       "",
			State:                      "",
			Status:                     "Note Captured",
			Reason:                     "",
			Timezone:                   "",
			Notes:                      "Updated DEL ETA 7/16 1800",
			Author:                     "Jane Doe",
			IsException:                &vFalse,
			DateTimeWithoutTimezone:    models.NullTime{Valid: false},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 16, 11, 22, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "dispatcher",
			City:              "",
			State:             "",
			Status:            "In Transit",
			Reason:            "",
			Timezone:          "",
			Notes:             "Updated DEL ETA 1800",
			Author:            "Jane Doe",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 16, 10, 0, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 16, 18, 0, 0, 0, time.UTC),
				Valid: true},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 16, 11, 22, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "dispatcher",
			City:              "",
			State:             "",
			Status:            "In Transit",
			Reason:            "",
			Timezone:          "",
			Notes:             "DEL ETA 7/15 20:00",
			Author:            "Jane Doe",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 15, 8, 30, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 15, 20, 0, 0, 0, time.UTC),
				Valid: true},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 15, 9, 41, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "four_kites_status_update_process_manager",
			City:              "NEWBURY MILL",
			State:             "Linwood, NC",
			Status:            "Marked Loaded",
			Reason:            "",
			Timezone:          "",
			Notes:             "Marked loaded by FourKites",
			Author:            "",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 13, 19, 32, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 13, 20, 30, 0, 0, time.UTC),
				Valid: true},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 13, 20, 31, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:                  123,
			FreightTrackingID:       "FREIGHT123",
			Source:                  "four_kites_status_update_process_manager",
			City:                    "Stop 1",
			State:                   "",
			Status:                  "Departed from Stop",
			Reason:                  "",
			Timezone:                "",
			Notes:                   "",
			Author:                  "",
			DateTimeWithoutTimezone: models.NullTime{Valid: false},
			EndDateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 13, 20, 30, 0, 0, time.UTC),
				Valid: true},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 13, 20, 31, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			Source:            "four_kites_status_update_process_manager",
			City:              "Stop 1",
			State:             "",
			Status:            "Arrival At Stop",
			Reason:            "",
			Timezone:          "",
			Notes:             "Marked arrived at stop via FourKites",
			Author:            "",
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 7, 13, 19, 32, 0, 0, time.UTC),
				Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 13, 19, 33, 0, 0, loc),
				Valid: true},
		},
		{
			LoadID:                     123,
			FreightTrackingID:          "FREIGHT123",
			Source:                     "dispatcher",
			City:                       "",
			State:                      "",
			Status:                     "Note Captured",
			Reason:                     "",
			Timezone:                   "",
			Notes:                      "PU ETA 7/13 driver held up at prior load",
			Author:                     "Jane Doe",
			DateTimeWithoutTimezone:    models.NullTime{Valid: false},
			EndDateTimeWithoutTimezone: models.NullTime{Valid: false},
			NextStopETAWithoutTimezone: models.NullTime{Valid: false},
			IsException:                &vFalse,
			CapturedDatetime: models.NullTime{Time: time.Date(2024, 7, 12, 9, 33, 0, 0, loc),
				Valid: true},
		},
	}

	for i, cc := range checkCalls {
		require.Equalf(t, expected[i], cc, "check call %d does not match", i+1)
	}
}
