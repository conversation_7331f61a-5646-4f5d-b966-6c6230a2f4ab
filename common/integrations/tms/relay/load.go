package relay

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/common/util/timezone"
)

// NOTE: freightTrackingID can be either the Relay Load ID (PRO #)
// or the Booking ID
func (r *Relay) GetLoad(
	ctx context.Context,
	freightTrackingID string,
) (load models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("freight_tracking_id", freightTrackingID))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = r.planningBoardAttributes()

	// check if load exists in TMS already
	foundLoad, err := loadDB.GetLoadByFreightIDAndTMSID(ctx, r.tms.ID, freightTrackingID)
	if err != nil {
		foundLoad, err = loadDB.GetLoadByExternalTMSIDAndTMSID(ctx, r.tms.ID, freightTrackingID)
		if err != nil {
			foundLoad, _, err = r.getLoadFromLoadBoard(ctx, freightTrackingID)
			if err != nil {
				log.Infof(ctx, "Relay load with ID %s not found on load board", freightTrackingID)
			}
		}
	}

	if len(foundLoad.FreightTrackingID) > 0 {
		load, err = r.parseLoadDetailHTML(ctx, foundLoad.ExternalTMSID)
	} else {
		load, err = r.parseLoadDetailHTML(ctx, freightTrackingID)
	}

	if err != nil {
		return load, attrs, fmt.Errorf("parseLoadDetailHTML error: %w", err)
	}

	// Our understanding is if there is no carrier booked/operator assigned, reference numbers and rate details
	// are not shown on the load detail page. In that case, fetch PO nums from Load Board via websocket.
	// Some Optimization: Get from load board only if we're missing ref #s to avoid 3 lookups per fetch.
	if strings.TrimSpace(load.PONums) == "" && strings.TrimSpace(load.Customer.RefNumber) == "" {
		loadBoardRes, _, err := r.getLoadFromLoadBoard(ctx, load.ExternalTMSID)
		// Fail-open, but a 404 is *not* expected since the load should still be on the load board
		if err != nil {
			log.Warn(ctx, "error getting ref #s from load board", zap.Error(err))
		} else {
			load.PONums = loadBoardRes.PONums
			load.Customer.RefNumber = loadBoardRes.Customer.RefNumber
			load.Pickup.RefNumber = loadBoardRes.Pickup.RefNumber
			load.Consignee.RefNumber = loadBoardRes.Consignee.RefNumber
		}

	}

	err = r.parseCarrierInfoHTML(ctx, &load)
	// It's possible for a load to be off the load board but not have a tracking page because the original carrier
	// was bounced (i.e. removed from the load).
	if err != nil && !errtypes.IsEntityNotFoundError(err) {
		return load, attrs, fmt.Errorf("parseCarrierInfoHTML error: %w", err)
	}
	attrs = r.trackingBoardAttributes()

	return load, attrs, nil
}

func (r *Relay) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) (_ models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("externalTMSID", curLoad.ExternalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = r.GetDefaultLoadAttributes()

	// Optimization: Call each update function only if necessary. We intentionally do not use goroutines
	// to avoid race conditions of making multiple updates to the same load at the same time.
	// NOTE: These conditions should be updated as Relay PUT functions are updated/added
	if strings.EqualFold(curLoad.Operator, updatedLoad.Operator) {
		log.Debug(ctx, "no Operator diff, skipping schedulePlan")
	} else {
		if err = r.assignOperator(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("assignOperator error: %w", err)
		}
		// NOTE: On Drumkit, to remove the operator, user sets the field to "". But on success, Relay returns
		// No Assignment. So that the user doesn't see a "partial update" warning on the front-end,
		// we manually reassign Operator to the value we will parse from Relay
		if updatedLoad.Operator == "" {
			updatedLoad.Operator = "No Assignment"
		}

		// Pause so Relay can update load detail page before we re-parse it
		time.Sleep(200 * time.Millisecond)
	}

	if len(curLoad.Pickup.Diff(updatedLoad.Pickup)) > 0 || len(curLoad.Consignee.Diff(updatedLoad.Consignee)) > 0 {
		if err = r.schedulePlan(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("schedule plan error: %w", err)
		}
	} else {
		log.Debug(ctx, "no Pickup/Consignee diff, skipping schedulePlan")
	}

	if len(curLoad.Carrier.Diff(updatedLoad.Carrier)) == 0 {
		log.Debug(ctx, "no Carrier diff, skipping tracking page updates")

		return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
	}

	// Check if load is on tracking board and thus, if carrier/equipment info is editable
	_, err = r.getHTML(ctx, "tracking/tracking_load_detail/"+url.PathEscape(updatedLoad.FreightTrackingID),
		nil, "")
	if err != nil {
		// If tracking page is 404, then re-parse load and return
		if errtypes.IsEntityNotFoundError(err) {
			return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
		}

		// Fail-open and try to update tracking info
		log.WarnNoSentry(ctx, "error checking if tracking page exists", zap.Error(err))
	}
	attrs = r.trackingBoardAttributes()

	if err = r.addDriverEquipmentInfo(ctx, updatedLoad); err != nil {
		return models.Load{}, attrs, fmt.Errorf("addDriverEquipmentInfo error: %w", err)
	}

	// NOTE: On Relay, dispatching and adding driver info are separate actions.
	// But NFI requested for adding driver info to trigger dispatching, so we do as long as
	// DispatchSource is provided
	if !curLoad.Carrier.ExpectedPickupTime.Equal(updatedLoad.Carrier.ExpectedPickupTime) ||
		!curLoad.Carrier.DispatchedTime.Equal(updatedLoad.Carrier.DispatchedTime) ||
		!strings.EqualFold(curLoad.Carrier.DispatchCity, updatedLoad.Carrier.DispatchCity) ||
		!strings.EqualFold(curLoad.Carrier.DispatchState, updatedLoad.Carrier.DispatchState) ||
		!strings.EqualFold(curLoad.Carrier.DispatchSource, updatedLoad.Carrier.DispatchSource) {

		if err = r.dispatchDriver(ctx, curLoad, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("dispatchDriver error: %w", err)
		}
	}

	// Check if we need to update carrier info
	if !strings.EqualFold(curLoad.Carrier.Dispatcher, updatedLoad.Carrier.Dispatcher) ||
		!strings.EqualFold(curLoad.Carrier.Phone, updatedLoad.Carrier.Phone) ||
		!strings.EqualFold(curLoad.Carrier.Email, updatedLoad.Carrier.Email) ||
		!strings.EqualFold(curLoad.Carrier.Notes, updatedLoad.Carrier.Notes) {
		if err = r.addCarrierContactInfo(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("addCarrierContactInfo error: %w", err)
		}
	}

	return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
}

// TODO: Switch to API and filter by time.
//
// Relay has different boards for different phases of a load -- Load Board for when it's first created,
// Planning Board for planning/scheduling appointments, tracking board for tracking its progress.
// When a load is first created, it lives on the Load and Planning Board. Once a carrier is booked/assigned,
// the shipment is no longer on the load board.
//
// // This function isn't guaranteed to get *every* new load if
// 1) The load is created and before 10 minute refresh, it's moved off the load board
// 2) The highest ID in the DB is inaccurate because some loads were previously skipped
func (r *Relay) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (res []string, err error) {
	// MESSAGE 1: Connect
	payload1 := make([]any, 5)
	payload1[0] = "3"                 // JoinRef
	payload1[1] = "3"                 // Ref
	payload1[2] = "load_board_v2:all" // Topic
	payload1[3] = "phx_join"          // Event
	payload1[4] = map[string]any{
		"apiVersion": nil,
		"current_user": map[string]any{
			"user_id":                651,
			"name":                   "Jinyan Zang",
			"email_address":          "<EMAIL>",
			"email":                  "<EMAIL>",
			"advertisement_enabled?": false,
		},
		"target_load_board_location_filter": "all",
		"ready_date_filter":                 "all",
		"pickup_date_filter":                "all",
		"assignment_filter":                 "all",
		"filter_text":                       "",
		"sort_desc":                         true,
		"sort_by":                           "load_number",
	}
	connectMsg := wsPayload{OperationName: "Load Board - Connect", Message: payload1, NumExpectedResponses: 1}

	// MESSAGE 2: Get load board table
	payload2 := make([]any, 5)
	payload2[0] = "3"
	payload2[1] = "4"
	payload2[2] = "load_board_v2:all"    // Topic
	payload2[3] = "load_board:reconnect" // Event
	payload2[4] = map[string]any{
		"target_load_board_location_filter": "all",
		"ready_date_filter":                 "all",
		"pickup_date_filter":                "all",
		"assignment_filter":                 "all",
		"filter_text":                       "",
		"sort_desc":                         true, // Sort in desc order
		"sort_by":                           "load_number",
	}
	message2 := wsPayload{OperationName: "Load Board - Show table", Message: payload2, NumExpectedResponses: 1}

	queryParams := url.Values{}
	queryParams.Set("token", "undefined")
	queryParams.Set("vsn", "2.0.0")

	_, err = r.sendWebSocketMessages(ctx, false, "socket/websocket", queryParams, false, connectMsg)
	if err != nil {
		return nil, fmt.Errorf("error connecting to load board websocket: %w", err)
	}

	respBody, err := r.sendWebSocketMessages(ctx, true, "socket/websocket", queryParams, false, message2)
	if err != nil {
		return nil, fmt.Errorf("error getting load board: %w", err)
	}

	var arrayList []any
	err = json.Unmarshal(respBody, &arrayList)
	if err != nil {
		return nil, fmt.Errorf("error marshaling into JSON list: %w", err)
	}

	if count := len(arrayList); count < 5 {
		log.WarnNoSentry(ctx, "unexpected response body", zap.Any("responseBody", arrayList))

		return nil, fmt.Errorf("expected 5 items in response body, received %d, view logs for details", count)
	}

	loadData, err := json.Marshal(arrayList[4])
	if err != nil {
		return nil, fmt.Errorf("error marshaling 5th item in JSON array: %w", err)
	}

	var loadResp LoadResp
	err = json.Unmarshal(loadData, &loadResp)
	if err != nil {
		return nil, fmt.Errorf("error unmarshaling data rows into LoadResp: %w", err)
	}

	if loadResp.Data.TotalRows == 0 {
		return res, errtypes.EntityNotFoundError(r.tms, "load board", "")
	}

	for _, row := range loadResp.Data.Loads {
		idToCheck := fmt.Sprint(row.LoadNumber)
		if idToCheck < query.FromFreightTrackingID {
			log.Infof(ctx, "breaking because ID %s is less than %s", idToCheck, query.FromFreightTrackingID)
			break
		}
		res = append(res, idToCheck)

	}

	return res, err
}

func (r *Relay) GetLoadsByIDType(
	ctx context.Context,
	id, idType string,
) (_ []models.Load, _ models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("id", id), attribute.String("idType", idType))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case LoadNumber:
		load, attrs, err := r.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	case BookingID:
		load, attrs, err := r.getLoadByBookingID(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return nil, r.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

func (r *Relay) getLoadByBookingID(
	ctx context.Context,
	bookingID string,
) (load models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("bookingID", bookingID))

	ctx, metaSpan := otel.StartSpan(ctx, "getLoadByBookingID", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Initialize load
	attrs = r.GetDefaultLoadAttributes()
	load.ServiceID = r.tms.ServiceID
	load.FreightTrackingID = bookingID

	// Get carrier info and Relay load number from tracking page first
	if err = r.parseCarrierInfoHTML(ctx, &load); err != nil {
		return load, attrs, fmt.Errorf("parseCarrierInfoHTML error: %w", err)
	}

	if strings.TrimSpace(load.ExternalTMSID) == "" {
		return load, attrs, errors.New("unable to parse loadNumber from tracking page")
	}
	// Now that we have the loadNumber aka ExternalTMSID, get the rest of the details

	load, attrs, err = r.GetLoad(ctx, load.ExternalTMSID)

	return load, attrs, err
}

func (r *Relay) parseCarrierInfoHTML(ctx context.Context, load *models.Load) (err error) {
	// Get tracking page using booking ID aka freightTrackingID, e.g. 8081291
	respBody, err := r.getHTML(ctx, "tracking/tracking_load_detail/"+
		url.PathEscape(load.FreightTrackingID), nil, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("error fetching tracking page: %w", err)
	}

	return r.mapCarrierInfoHTML(ctx, respBody, load)
}

// Get load board page using Relay Load # or PRO # (generally starts with 3)
func (r *Relay) parseLoadDetailHTML(ctx context.Context, relayProNum string) (models.Load, error) {

	respBody, err := r.getHTML(ctx, "sourcing/load_board_load_detail/"+
		url.PathEscape(relayProNum), nil, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, fmt.Errorf("error fetching load detail page: %w", err)
	}

	return r.mapLoadDetailHTML(ctx, respBody)
}

// Main difference between load detail page vs. tracking page is 1) PO nums (though both list shipment ID)
// and 2) operator assignment
func (r *Relay) mapLoadDetailHTML(ctx context.Context, respBody []byte) (load models.Load, err error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return load, fmt.Errorf("error reading document: %w", err)
	}

	load.TMSID = r.tms.ID
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = strings.TrimSpace(doc.Find(".relay-reference-number").Text())
	load.FreightTrackingID = strings.TrimSpace(doc.Find("h4.text-info.pl-3.d-inline").Text())

	// Mode
	modeText := strings.TrimSpace(doc.Find(
		".load-detail-header .d-flex.align-items-center").Eq(0).Find(".ml-3.pt-1.d-inline").Text())
	loadMode := models.StringToLoadMode(modeText)
	if loadMode == "" {
		log.WarnNoSentry(
			ctx,
			"Unknown Relay load mode",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("mode", modeText),
		)
	}
	load.Mode = loadMode

	// Customer
	load.Customer.Name = strings.ToTitle(
		strings.TrimSpace(doc.Find(`div[data-toggle="tooltip"] .text-dark.ml-2`).Text()))

	// Operator
	assigneeText := doc.Find(".load-detail-header div:nth-child(2) div:nth-child(1) div:nth-child(2)").Text()
	prefix := "Assigned to"
	index := strings.Index(assigneeText, prefix)
	if index != -1 {
		// Extract the substring after "Assigned to"
		load.Operator = strings.TrimSpace(assigneeText[index+len(prefix):])
	} else {
		load.Operator = strings.TrimSpace(assigneeText)
	}

	stops := doc.Find(".load-detail-stops .stop-detail")
	// Enable multi-stop loads on front-end
	load.MoreThanTwoStops = stops.Length() > 2
	if load.MoreThanTwoStops {
		log.Info(ctx, "load with more than 2 stops",
			zap.String("externalTMSID", load.ExternalTMSID), zap.String("freightTrackingID", load.FreightTrackingID))
	}
	// Pickup
	{
		pickup := stops.Eq(0)

		if label := strings.TrimSpace(pickup.Find(".stop-label").Text()); label != "Shipper" {
			return load, fmt.Errorf("unexpected label of first .stop-detail element to be 'Shipper', got %s", label)
		}

		parseCompanyCoreInfo(pickup, &load.Pickup.CompanyCoreInfo)
		load.Pickup.RefNumber = pickup.Find("button").AttrOr("data-content", "")

		pickupLoc, err := timezone.GetLocationByCity(ctx, load.Pickup.City, load.Pickup.State, load.Pickup.Country)
		if err != nil {
			return load, fmt.Errorf("error fetching pickup time.Location: %w", err)
		}
		load.Pickup.Timezone = pickupLoc.String()

		// NOTE: Appt block on Load Detail page can be either ready time or start time,
		// while only confirmed appointments appear on Tracking page so we parse from there instead
		// To avoid confusion and inconsistencies, we don't support Relay's ReadyTime for now,
		load.Pickup.ApptStartTime, err = parseApptTime(pickup, pickupLoc.String(), ".stop-appt")
		if err != nil {
			return load, fmt.Errorf("error parsing pickup appt start time: %w", err)
		}
		load.Pickup.ApptEndTime, err = parseApptTime(pickup, pickupLoc.String(), ".stop-appt-2")
		if err != nil {
			return load, fmt.Errorf("error parsing pickup appt end time: %w", err)
		}
	}

	// Delivery
	{
		dropoff := stops.Eq(-1)
		if label := strings.TrimSpace(dropoff.Find(".stop-label").Text()); label != "Receiver" {
			return load, fmt.Errorf("unexpected label of last .stop-detail to be 'Receiver', got %s", label)
		}

		parseCompanyCoreInfo(dropoff, &load.Consignee.CompanyCoreInfo)
		load.Consignee.RefNumber = dropoff.Find("button").AttrOr("data-content", "")

		dropoffLoc, err := timezone.GetLocationByCity(ctx,
			load.Consignee.City, load.Consignee.State, load.Consignee.Country)
		if err != nil {
			return load, fmt.Errorf("error fetching dropoff time.Location: %w", err)
		}
		load.Consignee.Timezone = dropoffLoc.String()

		load.Consignee.ApptStartTime, err = parseApptTime(dropoff, dropoffLoc.String(), ".stop-appt")
		if err != nil {
			return load, fmt.Errorf("error parsing dropoff appt start time: %w", err)
		}
		load.Consignee.ApptEndTime, err = parseApptTime(dropoff, dropoffLoc.String(), ".stop-appt-2")
		if err != nil {
			return load, fmt.Errorf("error parsing dropoff appt end time: %w", err)
		}
	}

	// Load details
	{
		loadDetails := doc.Find(".load-detail-booking .label-value-group")
		loadDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch strings.ToLower(label) {
			case "reference numbers":
				// It's already a comma-separated list
				load.PONums = strings.ReplaceAll(strings.TrimSpace(s.Find(".value").Text()), " ", "")
				refSplits := strings.Split(strings.TrimSpace(s.Find(".value").Text()), ",")

				// In mapLoadDetailHTML, there's evidence that the first of "reference numbers" is the shipment ID.
				// But if there's a tracking page for the load, we re-parse the value to be certain.
				if len(refSplits) > 0 {
					load.Customer.RefNumber = strings.TrimSpace(refSplits[0])
				}
			case "rate confirmation sent":
				load.Carrier.RateConfirmationSent = strings.ToLower(
					strings.TrimSpace(s.Find(".value").Text())) == "yes"
			case "carrier":
				load.Carrier.Name = strings.TrimSpace(s.Find(".value").Text())
			case "recipients":
				load.Carrier.Email = strings.TrimSpace(s.Find(".value").Text())
			case "total carrier rate":
				currencyType := strings.TrimSpace(s.Find(".value .mr-1").Text())
				if currencyType == "USD" {
					priceWithDollarSign := strings.TrimSpace(s.Find(".value .font-size-largest").Text())
					cleanedValue := strings.ReplaceAll(priceWithDollarSign, "$", "")
					cleanedValue = strings.ReplaceAll(cleanedValue, ",", "")

					// Convert the cleaned value to float32
					var floatValue float64
					floatValue, err = strconv.ParseFloat(cleanedValue, 32)
					if err == nil {
						load.DeclaredValueUSD = float32(floatValue)
					}
				}
			}
		})
	}

	// Cargo details
	{
		cargoDetails := doc.Find(".load-detail-cargo .label-value-group")
		cargoDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch label {
			case "Miles":
				milesString := strings.TrimSpace(s.Find(".value").Text())
				if milesString != "" {
					floatValue, parseErr := strconv.ParseFloat(milesString, 32)
					if parseErr == nil {
						load.Specifications.TotalDistance = models.ValueUnit{
							Val: float32(floatValue), Unit: models.MilesUnit}
					}
					err = parseErr
				}
			case "Weight":
				weightString := strings.TrimSpace(s.Find(".value").Text())
				if weightString != "" {
					floatValue, parseErr := strconv.ParseFloat(weightString, 32)
					if parseErr == nil {
						// Pounds is the only unit available on the create/edit load page
						load.Specifications.TotalWeight = models.ValueUnit{
							Val: float32(floatValue), Unit: models.LbsUnit}
					}
					err = parseErr
				}
			case "Pallets":
				palletsString := strings.TrimSpace(s.Find(".value").Text())
				if palletsString != "" {
					intValue, parseErr := strconv.Atoi(palletsString)
					if parseErr == nil {
						load.Specifications.TotalOutPalletCount = intValue
					}
					err = parseErr
				}

			case "Pieces":
				piecesStr := strings.TrimSpace(s.Find(".value").Text())
				if piecesStr != "" {
					intValue, parseErr := strconv.Atoi(piecesStr)
					if parseErr == nil {
						// Unit based on relaytms.com/planning_board/stop_management/{load#}
						load.Specifications.TotalPieces = models.ValueUnit{Val: float32(intValue), Unit: "boxes"}
					}
				}

			case "Commodity":
				list := s.Find(".value ul")
				if list.Length() > 0 {
					items := list.Find("li")
					var commodities []string
					items.Each(func(_ int, s *goquery.Selection) {
						commodities = append(commodities, strings.TrimSpace(s.Text()))
					})
					load.Specifications.Commodities = strings.Join(commodities, ",")
				} else {
					load.Specifications.Commodities = strings.TrimSpace(s.Find(".value").Text())
				}
			}

		})
	}

	// Rate details
	{
		rateDetails := doc.Find(".load-detail-money .label-value-group")
		rateDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch label {
			case "Customer Rate":
				customerRate := s.Find(".customer-rate-breakdown tr")
				customerRate.Each(func(_ int, s *goquery.Selection) {
					rateType := strings.TrimSpace(s.Find(".text-muted").Text())
					switch rateType {
					case "Linehaul":
						// rate := strings.TrimSpace(s.Find(".text-right").Text())
						// cleanedValue := strings.ReplaceAll(rate, "$", "")
						// floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						// if parseErr == nil {
						// 	load.RateData.CustomerLHRateUSD = float32(floatValue /
						// 		float64(load.Specifications.TotalDistance.Val))
						// }
					case "Fuel surcharge":
						// rate := strings.TrimSpace(s.Find(".text-right").Text())
						// cleanedValue := strings.ReplaceAll(rate, "$", "")
						// if cleanedValue != "" {
						// 	// floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						// 	// if parseErr == nil {
						// 	// 	load.RateData.CarrierLHRateUSD = float32(floatValue) /
						// 	// 		load.Specifications.TotalDistance.Val
						// 	// }
						// }
					}
				})
			case "Max Buy":
				currencyType := strings.TrimSpace(s.Find("small").Text())
				if currencyType == "USD" {
					totalBuyingValue := strings.TrimSpace(s.Find("span.value").Text())
					cleanedValue := strings.ReplaceAll(totalBuyingValue, "$", "")
					if cleanedValue != "" {
						cleanedValue = strings.ReplaceAll(cleanedValue, ",", "")
						// Convert the cleaned value to float32
						floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						if parseErr == nil {
							load.RateData.CarrierMaxRate = float32(floatValue)
						}
						err = parseErr
					}
				}
			}
		})
	}

	return load, err
}

func (r *Relay) mapCarrierInfoHTML(ctx context.Context, respBody []byte, load *models.Load) error {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return fmt.Errorf("error reading document: %w", err)
	}

	load.TMSID = r.tms.ID
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = strings.TrimSpace(doc.Find(".relay-reference-number").Text())
	load.FreightTrackingID = strings.TrimSpace(doc.Find(".text-info.pl-3.d-inline").Text())
	load.Status = parseStatus(doc)
	load.Notes = parseNotes(ctx, doc)

	// Appointment block on Load Detail page may be ready date OR actual appointment.
	// But only confirmed appointments appear on tracking page so we use that as the reliable source
	stops := doc.Find(".stop-detail")
	// Pickup
	{
		pickup := stops.Eq(0)

		if label := strings.TrimSpace(pickup.Find(".stop-label").Text()); label != "Origin" {
			return fmt.Errorf("unexpected label of first .stop-detail element to be 'Origin', got %s", label)
		}

		// Parse timestamps only if mapLoadBoardHTML was called first
		// different for getLoadByBookingID which has to get Tracking page first
		if load.Pickup.City != "" {
			pickupLoc, err := timezone.GetLocationByCity(ctx, load.Pickup.City, load.Pickup.State, load.Pickup.Country)
			if err != nil {
				return fmt.Errorf("error fetching pickup time.Location: %w", err)
			}
			load.Pickup.Timezone = pickupLoc.String()

			load.Pickup.ApptStartTime, err = parseApptTime(pickup, pickupLoc.String(), ".stop-appt")
			if err != nil {
				return fmt.Errorf("error parsing pickup appt start time: %w", err)
			}
			load.Pickup.ApptEndTime, err = parseApptTime(pickup, pickupLoc.String(), ".stop-appt-2")
			if err != nil {
				return fmt.Errorf("error parsing pickup appt end time: %w", err)
			}
		}
	}

	// Delivery
	{
		// -2 because -1 is check call section
		dropoff := stops.Eq(-2)
		if label := strings.TrimSpace(dropoff.Find(".stop-label").Text()); label != "Destination" {
			return fmt.Errorf("unexpected label of last .stop-detail to be 'Destination', got %s", label)
		}

		// Parse timestamps only if mapLoadBoardHTML was called first
		// different for getLoadByBookingID which has to get Tracking page first
		if load.Consignee.City != "" {
			dropoffLoc, err := timezone.GetLocationByCity(ctx,
				load.Consignee.City, load.Consignee.State, load.Consignee.Country)
			if err != nil {
				return fmt.Errorf("error fetching dropoff time.Location: %w", err)
			}
			load.Consignee.Timezone = dropoffLoc.String()

			load.Consignee.ApptStartTime, err = parseApptTime(dropoff, dropoffLoc.String(), ".stop-appt")
			if err != nil {
				return fmt.Errorf("error parsing dropoff appt start time: %w", err)
			}
			load.Consignee.ApptEndTime, err = parseApptTime(dropoff, dropoffLoc.String(), ".stop-appt-2")
			if err != nil {
				return fmt.Errorf("error parsing dropoff appt end time: %w", err)
			}
		}
	}

	moneyDiv := doc.Find(".load-detail-money .label-value-group")

	moneyDiv.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		// In mapLoadDetailHTML, there's evidence that that the first of the list of "reference numbers"
		// is the shipment ID. But if there's a tracking page for the load, we re-parse the value with certainty.
		if strings.ToLower(label) == "shipment id" {
			load.Customer.RefNumber = value
		}
	})

	carrierDiv := doc.Find(".load-detail-cargo .label-value-group")

	carrierDiv.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		switch strings.ToLower(label) {
		// In mapLoadDetailHTML, there's evidence that that the first of the list of "reference numbers"
		// is the shipment ID. But if there's a tracking page for the load, we re-parse the value with certainty.
		case "shipment id":
			load.Customer.RefNumber = value

		case "carrier":
			load.Carrier.Name = value

		case "driver":
			driverDetails := s.Find(".value")
			if driverDetails.Length() > 0 {
				driverNames := strings.Split(strings.TrimSpace(driverDetails.Eq(0).Text()), ",")
				if len(driverNames) == 1 {
					load.Carrier.FirstDriverName = driverNames[0]
				}
				if len(driverNames) >= 2 {
					load.Carrier.FirstDriverName = strings.TrimSpace(driverNames[1]) + " " +
						strings.TrimSpace(driverNames[0])
				}
				load.Carrier.FirstDriverPhone = strings.TrimSpace(s.Find("span").Eq(0).Text())
			}
			if driverDetails.Length() > 1 {
				load.Carrier.SecondDriverName = strings.TrimSpace(driverDetails.Eq(1).Text())
				load.Carrier.SecondDriverPhone = strings.TrimSpace(s.Find("span").Eq(1).Text())
			}

		case "truck/trailer number":
			load.Carrier.ExternalTMSTruckID = value
			load.Carrier.ExternalTMSTrailerID = strings.TrimSpace(s.Find("span").Text())
		}

	})
	contactDetails := doc.Find(".load-detail-contact .label-value-group")
	contactDetails.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		switch strings.ToLower(label) {
		case "tracking contact":
			load.Carrier.Dispatcher = value
			load.Carrier.Phone = strings.TrimSpace(s.Find("span").Eq(0).Text())
			load.Carrier.Email = strings.TrimSpace(s.Find("span").Eq(1).Text())

		case "contact notes":
			load.Carrier.Notes = value
		}
	})

	checkCalls, err := r.parseCheckCallHTML(ctx, respBody, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open
		log.WarnNoSentry(ctx, "error parsing check calls", zap.Error(err))
		return nil
	}

	index := slices.IndexFunc(checkCalls, func(cc models.CheckCall) bool {
		return strings.EqualFold(cc.Status, "driver dispatched")
	})
	if index > -1 {
		dispatchCC := checkCalls[index]
		load.Carrier.DispatchCity = dispatchCC.City
		load.Carrier.DispatchState = dispatchCC.State
		load.Carrier.DispatchSource = dispatchCC.Source

		eta, err := timezone.DenormalizeUTC(dispatchCC.NextStopETAWithoutTimezone.Time, load.Pickup.Timezone)
		if err != nil {
			log.WarnNoSentry(ctx, "error denormalizing expectedPickup",
				zap.Error(err), zap.String("timezone", load.Pickup.Timezone))
		}
		load.Carrier.ExpectedPickupTime = models.ToValidNullTime(eta)

		loc, err := timezone.GetTimezone(ctx, dispatchCC.City, dispatchCC.State, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting dispatched timezone, falling back to TZ-agnostic", zap.Error(err))
			load.Carrier.DispatchedTime = models.ToValidNullTime(dispatchCC.DateTimeWithoutTimezone.Time)

			return nil
		}
		dispTime, err := timezone.DenormalizeUTC(dispatchCC.DateTimeWithoutTimezone.Time, loc)
		if err != nil {
			log.WarnNoSentry(ctx, "error denormalizing dispatchedTime, falling back to TZ-agnostic",
				zap.Error(err), zap.String("timezone", loc))
			load.Carrier.DispatchedTime = models.ToValidNullTime(dispatchCC.DateTimeWithoutTimezone.Time)
			return nil
		}
		load.Carrier.DispatchedTime = models.ToValidNullTime(dispTime)

	}

	return nil
}

// parseCompanyCoreInfo parses common company core information from the given selection.
func parseCompanyCoreInfo(selection *goquery.Selection, company *models.CompanyCoreInfo) {
	company.Name = strings.TrimSpace(selection.Find(".location-address .title").Text())

	address := selection.Find(".location-address .subtitle")
	company.AddressLine1 = strings.TrimSpace(address.Find(".address-1").Text())
	company.AddressLine2 = strings.TrimSpace(address.Find(".address-2").Text())

	cityStateZip := strings.TrimSpace(selection.Find(".city-state-zip").Text())
	cityStateZip = strings.ReplaceAll(cityStateZip, " ", " ")

	cityStateZipArray := strings.Split(cityStateZip, ",")

	if len(cityStateZipArray) == 2 {
		company.City = strings.TrimSpace(cityStateZipArray[0])
		stateZip := strings.Split(strings.TrimSpace(cityStateZipArray[1]), " ")

		if len(stateZip) >= 2 {
			company.State = strings.TrimSpace(stateZip[0])
			// Handle Canada (e.g. K8N 0A3)
			company.Zipcode = strings.TrimSpace(strings.Join(stateZip[1:], " "))
		}
	}
}

// parseApptTime parses the appointment time for the given selection.
func parseApptTime(selection *goquery.Selection, timezoneName, className string) (models.NullTime, error) {
	const dateTimeHyphensLayout = "2006-01-02 15:04"
	const dateTimeSlashesLayout = "1/2/2006 15:04"
	const dateTimeSlashesNoYearLayout = "1/2 15:04"

	apptString := strings.TrimSpace(strings.ReplaceAll(selection.Find(className).Text(), "\x0a", ""))
	if apptString == "" {
		return models.NullTime{}, nil
	}

	location, err := time.LoadLocation(timezoneName)
	if err != nil {
		return models.NullTime{}, fmt.Errorf("error fetching time.Location: %w", err)
	}

	// If on tracking page, appt reference is appended so we extract just the date using regex
	var re *regexp.Regexp
	if strings.Contains(apptString, "/") {
		// Match both formats: with and without year
		re = regexp.MustCompile(`\d{1,2}/\d{1,2}(/\d{4})? \d{1,2}:\d{2}`)
		apptString = re.FindString(apptString)
	}

	var layout string
	switch {
	case !strings.Contains(apptString, " "):
		layout = time.DateOnly
	case strings.Contains(apptString, "/") && strings.Count(apptString, "/") == 2:
		layout = dateTimeSlashesLayout
	case strings.Contains(apptString, "/"):
		layout = dateTimeSlashesNoYearLayout
	default:
		layout = dateTimeHyphensLayout
	}

	var apptTime time.Time
	if layout == dateTimeSlashesNoYearLayout {
		// For dates without year, append current year between date and time
		currentYear := time.Now().In(location).Year()
		parts := strings.Split(apptString, " ")
		if len(parts) == 2 {
			apptString = fmt.Sprintf("%s/%d %s", parts[0], currentYear, parts[1])
		}
		layout = dateTimeSlashesLayout
	}

	apptTime, err = time.ParseInLocation(layout, apptString, location)
	if err != nil {
		return models.NullTime{}, err
	}

	return models.NullTime{
		Time:  apptTime,
		Valid: true,
	}, nil
}

func parseStatus(trackingDoc *goquery.Document) (status string) {
	// Check first if the load was cancelled
	trackingDoc.Find("div.load-detail-money .label-value-group").Each(func(_ int, s *goquery.Selection) {
		if label := strings.TrimSpace(s.Find(".label").Text()); label == "Customer Money" {
			// Evidence supports that if 1) there's a tracking page but 2) no customer or vendor charges,
			// then the load was cancelled
			if strings.ToLower(strings.TrimSpace(s.Find(".value").Text())) == "no charges" && status == "" {
				status = "Cancelled"
			}
		}
	})
	if status != "" {
		return status
	}

	// If not cancelled, then the most recent check call is its status
	trackingDoc.Find("div.stop-detail .tracking-update .update-title").Each(func(_ int, s *goquery.Selection) {
		recentStatus := strings.TrimSpace(s.Text())
		if recentStatus != "Note Captured" && status == "" {
			status = recentStatus
			return
		}
	})

	// If no check calls but there's a tracking page for it, then it's only been Booked
	if status == "" {
		return "Booked"
	}

	prefix := "Marked"
	index := strings.Index(status, prefix)
	if index != -1 {
		// Extract the substring after "Marked" if it exists (e.g. Marked Delivered, Marked Loaded)
		return strings.TrimSpace(status[index+len(prefix):])
	}

	return status
}
