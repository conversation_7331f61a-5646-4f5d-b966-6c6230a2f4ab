package revenova

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

type Revenova struct {
	tms models.Integration
}

const (
	devTMSHost  = "https://cs95.salesforce.com"
	prodTMSHost = "https://cs95.salesforce.com"
)

var tmsHost string

func New(ctx context.Context, tms models.Integration) *Revenova {
	if env.Vars.AppEnv == "prod" {
		tmsHost = prodTMSHost
	} else {
		tmsHost = devTMSHost
	}
	log.With(ctx, zap.Uint("axleTMSID", tms.ID),
		zap.String("tmsName", "revenova"), zap.String("host", tmsHost))
	return &Revenova{tms: tms}
}

func (r Revenova) GetLoadsByIDType(
	context.Context,
	string, string,
) (loads []models.Load, attr models.LoadAttributes, _ error) {
	return loads, attr, util.NotImplemented(models.Revenova, "GetLoadsByIDType")
}

func (r Revenova) GetDefaultLoadAttributes() (attr models.LoadAttributes) {
	panic("revenova.GetDefaultLoadAttributes not implemented, TODO (be sure to use ApplyTMSFeatureFlags)")
}

func (r Revenova) GetCheckCallsHistory(context.Context, uint, string) (checkCalls []models.CheckCall, _ error) {
	return checkCalls, util.NotImplemented(models.Revenova, "GetCheckCallsHistory")
}

func (r Revenova) GetTestLoads() map[string]bool {
	return nil
}

func (r Revenova) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return util.NotImplemented(models.Revenova, "PostCheckCall")
}

func (r Revenova) GetCustomers(context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, util.NotImplemented(models.Aljex, "GetCustomers")
}

func (r Revenova) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, util.NotImplemented(models.Revenova, "GetLocations")
}

func (r Revenova) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.Revenova, "GetCarriers")
}

func (r Revenova) CreateQuote(
	context.Context,
	models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, util.NotImplemented(models.Revenova, "CreateQuote")
}

func (r Revenova) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, util.NotImplemented(models.Revenova, "GetUsers")
}

func (r Revenova) PostException(context.Context, *models.Load, models.Exception) error {
	return util.NotImplemented(models.Revenova, "PostException")
}

func (r Revenova) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, util.NotImplemented(models.Revenova, "GetExceptionHistory")
}

func (r Revenova) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, util.NotImplemented(models.Revenova, "PostNote")
}

func (r Revenova) LoginWithSoap(ctx context.Context, host, path string,
	headerMap map[string]string, postBody, dst any) error {
	addr := url.URL{Scheme: "https", Host: host, Path: path}
	body, _, err := httputil.PostBytesWithToken(ctx, r.tms, addr, postBody, headerMap,
		nil, s3backup.TypeTokens)
	if err != nil {
		return err
	}
	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return nil
}

func (r Revenova) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	authorization *string,
	dataType s3backup.DataType) error {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	body, _, err := httputil.GetBytesWithToken(ctx, r.tms, addr, nil, authorization, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (r Revenova) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	dataType s3backup.DataType) (err error) {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	body, _, err := httputil.PostBytesWithToken(ctx, r.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (r Revenova) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	dataType s3backup.DataType) (err error) {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	body, _, err := httputil.PutBytesWithToken(ctx, r.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}
