package revenova

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/models"
)

func (r <PERSON>eno<PERSON>) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	request models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	token, err := r.GetToken(ctx, request.Username, request.Password)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	return models.OnboardTMSResponse{
		Username:    request.Username,
		AccessToken: token,
	}, nil

}

func (r <PERSON>eno<PERSON>) GetToken(ctx context.Context, username, password string) (string, error) {
	loginHost := ""
	if env.Vars.AppEnv == "prod" {
		loginHost = "https://login.salesforce.com"
	} else {
		loginHost = "https://test.salesforce.com"
	}

	// TODO : required organisation id to send login request.
	postBody := fmt.Sprintf(`<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
										xmlns:urn="urn:enterprise.soap.sforce.com">
										<soapenv:Header>
											<urn:LoginScopeHeader>
												<urn:organizationId>[15-character org ID]</urn:organizationId>	
											</urn:LoginScopeHeader>
										</soapenv:Header>
									<soapenv:Body>
									<urn:login>
										<urn:username>%s</urn:username>
										<urn:password>%s</urn:password>
									</urn:login>
									</soapenv:Body>
									</soapenv:Envelope>`,
		username, password)
	var token TokenResp
	headerMap := map[string]string{
		"Content-Type": "text/xml",
		"SOAPAction":   "",
	}
	err := r.LoginWithSoap(ctx, loginHost, "services/Soap/c/42.0", headerMap, postBody, &token)
	if err != nil {
		return "", err
	}
	return token.Body.LoginResponse.Result.SessionID, nil
}
