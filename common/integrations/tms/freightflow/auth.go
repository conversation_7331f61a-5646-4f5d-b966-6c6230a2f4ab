package freightflow

import (
	"context"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

var tmsDBUpdateFunc = integrationDB.Update

type (
	TokenRequestBody struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	TokenResponse struct {
		Token string `json:"token"`
	}
)

// authenticate authenticates with FreightFlow
func (f *FreightFlow) authenticate(ctx context.Context, username, password string) (models.OnboardTMSResponse, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "AuthenticateFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(nil) }()

	token, err := f.getToken(ctx, username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("failed to get token: %w", err)
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("failed to encrypt password: %w", err)
	}

	// Set expiration to 1 hour from now
	expirationDate := time.Now().Add(1 * time.Hour)

	return models.OnboardTMSResponse{
		AccessToken:               token,
		AccessTokenExpirationDate: expirationDate,
		EncryptedPassword:         encryptedPassword,
		Username:                  username,
	}, nil
}

// getToken gets a token from FreightFlow
func (f *FreightFlow) getToken(
	ctx context.Context,
	username, password string,
) (
	token string,
	err error,
) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetTokenFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(err) }()

	reqBody := TokenRequestBody{
		Email:    username,
		Password: password,
	}

	var tokenResp TokenResponse
	err = f.postNoAuth(ctx, "/v2-beta/auth/token", nil, reqBody, &tokenResp, nil, s3backup.TypeTokens)
	if err != nil {
		return "", fmt.Errorf("failed to get token: %w", err)
	}

	return tokenResp.Token, nil
}

// RefreshToken refreshes the access token
func (f *FreightFlow) RefreshToken(ctx context.Context) (models.OnboardTMSResponse, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "RefreshTokenFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(nil) }()

	password, err := crypto.DecryptAESGCM(ctx, string(f.tms.EncryptedPassword), nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("failed to decrypt password: %w", err)
	}

	response, err := f.authenticate(ctx, f.tms.Username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	// Update the TMS integration with the response data
	f.tms.AccessToken = response.AccessToken
	f.tms.AccessTokenExpirationDate = models.NullTime{
		Time:  response.AccessTokenExpirationDate,
		Valid: true,
	}
	f.tms.EncryptedPassword = []byte(response.EncryptedPassword)

	if err := tmsDBUpdateFunc(ctx, &f.tms); err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("failed to update integration: %w", err)
	}

	return response, nil
}

// InitialOnboard checks for valid credentials and calls authenticate
func (f *FreightFlow) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	req models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnboardFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(nil) }()

	if req.Username == "" || req.Password == "" {
		return models.OnboardTMSResponse{}, fmt.Errorf("missing FreightFlow credentials")
	}

	return f.authenticate(ctx, req.Username, req.Password)
}
