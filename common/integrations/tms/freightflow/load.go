package freightflow

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

// GetLoads gets all loads from FreightFlow
func (f *FreightFlow) GetLoads(ctx context.Context) ([]models.Load, error) {
	var loads []Load
	if err := f.get(ctx, "/v2-beta/loads", nil, &loads, s3backup.TypeLoads); err != nil {
		return nil, fmt.Errorf("failed to get loads: %w", err)
	}

	var result []models.Load
	for _, load := range loads {
		result = append(result, f.mapLoad(ctx, load))
	}

	return result, nil
}

// GetLoadByID gets a load by ID from FreightFlow
func (f *FreightFlow) GetLoadByID(ctx context.Context, loadID string) (*models.Load, error) {
	var load Load
	if err := f.get(ctx, fmt.Sprintf("/v2-beta/loads/%s", loadID), nil, &load, s3backup.TypeLoads); err != nil {
		return nil, fmt.Errorf("failed to get load: %w", err)
	}

	result := f.mapLoad(ctx, load)
	return &result, nil
}

// mapLoad maps a FreightFlow load to a Drumkit load
func (f *FreightFlow) mapLoad(ctx context.Context, load Load) models.Load {
	var loadMode models.LoadMode
	if load.Mode != nil {
		loadMode = models.StringToLoadMode(*load.Mode)
		if loadMode == "" {
			log.Warn(
				ctx,
				"Unknown FreightFlow load mode",
				zap.String("loadID", load.ID),
				zap.String("mode", *load.Mode),
			)
		}
	}

	// Check if ID is empty
	if load.ID == "" {
		log.Warn(
			ctx,
			"FreightFlow load ID is empty",
			zap.String("loadID", load.ID),
		)
	}

	result := models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Status:           load.Status,
			Mode:             loadMode,
			MoreThanTwoStops: false, // FreightFlow doesn't support more than 2 stops
			PONums:           strings.Join(load.PONums, ","),
			Operator:         safeString(load.Operator),
		},
		ExternalTMSID:     load.ID,
		FreightTrackingID: load.ID,
		ServiceID:         f.tms.ServiceID,
		TMSID:             f.tms.ID,
	}

	// Map Customer if available
	if load.Customer != nil {
		result.Customer = models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         safeString(load.Customer.Name),
				AddressLine1: safeString(load.Customer.AddressLine1),
				AddressLine2: safeString(load.Customer.AddressLine2),
				City:         safeString(load.Customer.City),
				State:        safeString(load.Customer.State),
				Zipcode:      safeString(load.Customer.Zipcode),
				Country:      safeString(load.Customer.Country),
				Contact:      safeString(load.Customer.Contact),
				Phone:        safeString(load.Customer.Phone),
				Email:        safeString(load.Customer.Email),
			},
			RefNumber: safeString(load.Customer.RefNumber),
		}
	}

	// Map BillTo if available
	if load.BillTo != nil {
		result.BillTo = models.BillTo{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         safeString(load.BillTo.Name),
				AddressLine1: safeString(load.BillTo.AddressLine1),
				AddressLine2: safeString(load.BillTo.AddressLine2),
				City:         safeString(load.BillTo.City),
				State:        safeString(load.BillTo.State),
				Zipcode:      safeString(load.BillTo.Zipcode),
				Country:      safeString(load.BillTo.Country),
				Contact:      safeString(load.BillTo.Contact),
				Phone:        safeString(load.BillTo.Phone),
				Email:        safeString(load.BillTo.Email),
			},
		}
	}

	// Map Pickup if available
	if load.Pickup != nil {
		result.Pickup = models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         safeString(load.Pickup.Name),
				AddressLine1: safeString(load.Pickup.AddressLine1),
				AddressLine2: safeString(load.Pickup.AddressLine2),
				City:         safeString(load.Pickup.City),
				State:        safeString(load.Pickup.State),
				Zipcode:      safeString(load.Pickup.Zipcode),
				Country:      safeString(load.Pickup.Country),
				Contact:      safeString(load.Pickup.Contact),
				Phone:        safeString(load.Pickup.Phone),
				Email:        safeString(load.Pickup.Email),
			},
			RefNumber:    safeString(load.Pickup.RefNumber),
			ApptNote:     safeString(load.Pickup.ApptNote),
			ApptRequired: safeBool(load.Pickup.ApptRequired),
			ApptType:     safeString(load.Pickup.ApptType),
			ApptEndTime:  parseTime(safeString(load.Pickup.ApptEndTime)),
			Timezone:     safeString(load.Pickup.Timezone),
		}
	}

	// Map Consignee if available
	if load.Consignee != nil {
		result.Consignee = models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         safeString(load.Consignee.Name),
				AddressLine1: safeString(load.Consignee.AddressLine1),
				AddressLine2: safeString(load.Consignee.AddressLine2),
				City:         safeString(load.Consignee.City),
				State:        safeString(load.Consignee.State),
				Zipcode:      safeString(load.Consignee.Zipcode),
				Country:      safeString(load.Consignee.Country),
				Contact:      safeString(load.Consignee.Contact),
				Phone:        safeString(load.Consignee.Phone),
				Email:        safeString(load.Consignee.Email),
			},
			RefNumber:     safeString(load.Consignee.RefNumber),
			BusinessHours: safeString(load.Consignee.BusinessHours),
			MustDeliver:   models.NullTime{Valid: safeBool(load.Consignee.MustDeliver)},
			ApptType:      safeString(load.Consignee.ApptType),
			ApptStartTime: parseTime(safeString(load.Consignee.ApptStartTime)),
			ApptEndTime:   parseTime(safeString(load.Consignee.ApptEndTime)),
			ApptNote:      safeString(load.Consignee.ApptNote),
			Timezone:      safeString(load.Consignee.Timezone),
		}
	}

	// Map Carrier if available
	if load.Carrier != nil {
		result.Carrier = models.Carrier{
			Name:                     safeString(load.Carrier.Name),
			DOTNumber:                safeString(load.Carrier.DOTNumber),
			Phone:                    safeString(load.Carrier.Phone),
			Email:                    safeString(load.Carrier.Email),
			Dispatcher:               safeString(load.Carrier.Dispatcher),
			FirstDriverName:          safeString(load.Carrier.FirstDriverName),
			FirstDriverPhone:         safeString(load.Carrier.FirstDriverPhone),
			ExternalTMSTruckID:       safeString(load.Carrier.ExternalTMSTruckID),
			ExternalTMSTrailerID:     safeString(load.Carrier.ExternalTMSTrailerID),
			PickupStart:              parseTime(safeString(load.Carrier.PickupStart)),
			PickupEnd:                parseTime(safeString(load.Carrier.PickupEnd)),
			DeliveryStart:            parseTime(safeString(load.Carrier.DeliveryStart)),
			DeliveryEnd:              parseTime(safeString(load.Carrier.DeliveryEnd)),
			MCNumber:                 safeString(load.Carrier.MCNumber),
			SealNumber:               safeString(load.Carrier.SealNumber),
			Notes:                    safeString(load.Carrier.Notes),
			SCAC:                     safeString(load.Carrier.SCAC),
			SecondDriverName:         safeString(load.Carrier.SecondDriverName),
			SecondDriverPhone:        safeString(load.Carrier.SecondDriverPhone),
			DispatchCity:             safeString(load.Carrier.DispatchCity),
			DispatchState:            safeString(load.Carrier.DispatchState),
			DispatchSource:           safeString(load.Carrier.DispatchSource),
			RateConfirmationSent:     safeBool(load.Carrier.RateConfirmationSent),
			ConfirmationSentTime:     parseTime(safeString(load.Carrier.ConfirmationSentTime)),
			ConfirmationReceivedTime: parseTime(safeString(load.Carrier.ConfirmationReceivedTime)),
			DispatchedTime:           parseTime(safeString(load.Carrier.DispatchedTime)),
			ExpectedPickupTime:       parseTime(safeString(load.Carrier.ExpectedPickupTime)),
			ExpectedDeliveryTime:     parseTime(safeString(load.Carrier.ExpectedDeliveryTime)),
		}
	}

	// Map Specifications
	result.Specifications = models.Specifications{
		TotalPieces: models.ValueUnit{
			Val:  safeIntToFloat32(load.TotalPackages),
			Unit: "pieces",
		},
		TotalWeight: models.ValueUnit{
			Val:  parseFloat(safeString(load.TotalWeightLbs)),
			Unit: "lbs",
		},
		TransportType: strings.Join(load.TruckTypes, ","),
	}

	// Map pickup and delivery windows if available
	if len(load.Pickups) > 0 {
		pickup := load.Pickups[0]
		result.Pickup.ApptStartTime = parseTime(pickup.WindowStart)
		result.Pickup.ApptEndTime = parseTime(pickup.WindowEnd)
	}

	if len(load.Deliveries) > 0 {
		delivery := load.Deliveries[0]
		result.Consignee.ApptStartTime = parseTime(delivery.WindowStart)
		result.Consignee.ApptEndTime = parseTime(delivery.WindowEnd)
	}

	// Map customer information from the customers array if available
	if len(load.Customers) > 0 {
		customer := load.Customers[0]
		result.Customer.Name = customer.Name
		result.Customer.Email = customer.Email
		// Map external IDs if available
		for _, extID := range customer.ExternalIDs {
			if extID.Name == "ID" {
				result.Customer.RefNumber = extID.Value
				break
			}
		}
	}

	return result
}

// Helper function to parse float strings
func parseFloat(s string) float32 {
	if s == "" {
		return 0
	}
	f, err := strconv.ParseFloat(s, 32)
	if err != nil {
		return 0
	}
	return float32(f)
}

// GetLoadIDs implements the TMS interface
func (f *FreightFlow) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) ([]string, error) {
	loadIDMap := make(map[string]struct{}) // Use map for deduplication
	const pageSize = 100
	page := 1

	for {
		queryParams := url.Values{}
		if query.FromDate.Valid {
			queryParams.Set("from", query.FromDate.Time.UTC().Format(time.RFC3339))
		}
		if query.ToDate.Valid {
			queryParams.Set("to", query.ToDate.Time.UTC().Format(time.RFC3339))
		}
		queryParams.Set("page", strconv.Itoa(page))
		queryParams.Set("limit", strconv.Itoa(pageSize))

		var resp LoadResponse
		if err := f.get(ctx, "/v2-beta/loads", queryParams, &resp, s3backup.TypeLoads); err != nil {
			return mapKeysToSlice(loadIDMap), fmt.Errorf("failed to get loads: %w", err)
		}

		if len(resp.Loads) == 0 {
			break
		}

		for _, load := range resp.Loads {
			loadIDMap[load.ID] = struct{}{}
		}

		page++
	}

	allLoadIDs := mapKeysToSlice(loadIDMap)
	return allLoadIDs, nil
}

// Helper function to convert a map keys to a slice
func mapKeysToSlice(m map[string]struct{}) []string {
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return result
}

// GetLoad implements the TMS interface
func (f *FreightFlow) GetLoad(
	ctx context.Context,
	externalTMSID string,
) (load models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(
		otel.IntegrationAttrs(f.tms),
		attribute.String("external_tms_id", externalTMSID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadFreightFlow", spanAttrs)
	defer func() { metaSpan.End(err) }()

	loadPtr, err := f.GetLoadByID(ctx, externalTMSID)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, err
	}
	return *loadPtr, f.GetDefaultLoadAttributes(), nil
}

// PostLoad implements the TMS interface
func (f *FreightFlow) PostLoad(context.Context, *models.Load) (err error) {
	return util.NotImplemented(models.FreightFlow, "PostLoad")
}

// CreateLoad implements the TMS interface
func (f *FreightFlow) CreateLoad(context.Context, models.Load, *models.TMSUser) (models.Load, error) {
	return models.Load{}, util.NotImplemented(models.FreightFlow, "CreateLoad")
}

// Helper function to parse time strings into models.NullTime
func parseTime(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{}
	}

	// Parse the time string based on the format used by FreightFlow
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return models.NullTime{}
	}

	return models.NullTime{
		Valid: true,
		Time:  t,
	}
}

// Helper function to safely handle string pointers
func safeString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// Helper function to safely handle bool pointers
func safeBool(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// Helper function to safely handle int pointers
func safeIntToFloat32(i *int) float32 {
	if i == nil {
		return 0
	}
	return float32(*i)
}
