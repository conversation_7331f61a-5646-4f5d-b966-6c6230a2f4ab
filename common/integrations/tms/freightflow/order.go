package freightflow

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

type (
	// OrderListResponse represents the response from the list orders endpoint
	OrderListResponse struct {
		Orders []Order `json:"orders"`
	}

	// Order represents a FreightFlow order
	Order struct {
		ID                        string       `json:"id"`
		Name                      string       `json:"name"`
		Email                     string       `json:"email"`
		PhoneNumbers              []string     `json:"phoneNumbers"`
		Address                   Address      `json:"address"`
		Website                   string       `json:"website"`
		Notes                     string       `json:"notes"`
		Tags                      []string     `json:"tags"`
		Comments                  []Comment    `json:"comments"`
		ExternalIDs               []string     `json:"externalIds"`
		Branches                  []Branch     `json:"branches"`
		Insurance                 Insurance    `json:"insurance"`
		MCNumbers                 []string     `json:"mcNumbers"`
		SCACCode                  string       `json:"scacCode"`
		DOTNumber                 string       `json:"dotNumber"`
		IsBlacklisted             bool         `json:"isBlacklisted"`
		IsArchived                bool         `json:"isArchived"`
		CreatedAt                 time.Time    `json:"createdAt"`
		UpdatedAt                 time.Time    `json:"updatedAt"`
		CompanyID                 string       `json:"companyId"`
		Alias                     string       `json:"alias"`
		Type                      string       `json:"type"`
		TimeStrategy              string       `json:"timeStrategy"`
		DefaultWindowStart        string       `json:"defaultWindowStart"`
		DefaultWindowEnd          string       `json:"defaultWindowEnd"`
		ExpectedMinutesAtLocation int          `json:"expectedMinutesAtLocation"`
		ContactTypeTags           []string     `json:"contactTypeTags"`
		LoadOptions               []LoadOption `json:"loadOptions"`
		TruckTypes                []TruckType  `json:"truckTypes"`
	}

	Address struct {
		Street  string `json:"street"`
		City    string `json:"city"`
		State   string `json:"state"`
		Zip     string `json:"zip"`
		Country string `json:"country"`
	}

	Comment struct {
		CreatorID        *string `json:"creatorId"`
		ExternalSourceID *string `json:"externalSourceId"`
		IsPublic         bool    `json:"isPublic"`
		Content          string  `json:"content"`
		CreationSource   string  `json:"creationSource"`
	}

	Branch struct {
		TeamID   string `json:"teamId"`
		Name     string `json:"name"`
		ID       uint   `json:"id"`
		Slug     string `json:"slug"`
		IsActive bool   `json:"isActive"`
	}

	Insurance struct {
		Broker                      string        `json:"broker"`
		PolicyNumber                string        `json:"policyNumber"`
		ExpirationDate              string        `json:"expirationDate"`
		LossPayee                   bool          `json:"lossPayee"`
		SignedBrokerIndemnification bool          `json:"signedBrokerIndemnification"`
		Type                        InsuranceType `json:"type"`
		EffectiveDate               string        `json:"effectiveDate"`
		Exempt                      bool          `json:"exempt"`
		Insurer                     string        `json:"insurer"`
		AdditionalInsured           bool          `json:"additionalInsured"`
		Amount                      float64       `json:"amount"`
		Currency                    string        `json:"currency"`
	}

	InsuranceType struct {
		Name string `json:"name"`
	}

	LoadOption struct {
		Name string `json:"name"`
	}

	TruckType struct {
		Name string `json:"name"`
	}

	OrderResponse struct {
		Order Order `json:"order"`
	}

	// CreateOrderRequest represents the request body for creating an order
	CreateOrderRequest struct {
		TotalWeightLbs        *float64           `json:"totalWeightLbs,omitempty"`
		Deliveries            []PublicOrderStop  `json:"deliveries,omitempty"`
		ReceivedTotalWeight   *float64           `json:"receivedTotalWeight,omitempty"`
		Comments              []PublicComment    `json:"comments,omitempty"`
		LoadID                *string            `json:"loadId,omitempty"`
		SalesTeam             string             `json:"salesTeam"`
		IsVoided              bool               `json:"isVoided"`
		Reference             string             `json:"reference"`
		ReceivedTotalTotes    *float64           `json:"receivedTotalTotes,omitempty"`
		TotalTotes            *float64           `json:"totalTotes,omitempty"`
		BillingItems          []PublicLineItem   `json:"billingItems,omitempty"`
		CostItems             []PublicLineItem   `json:"costItems,omitempty"`
		Branches              []BranchSchemaBase `json:"branches,omitempty"`
		ExternalIDs           []ExternalIDPublic `json:"externalIds,omitempty"`
		TotalPackages         *float64           `json:"totalPackages,omitempty"`
		TruckBrokerID         *string            `json:"truckBrokerId,omitempty"`
		TotalPallets          *float64           `json:"totalPallets,omitempty"`
		CarrierID             *string            `json:"carrierId,omitempty"`
		Notes                 *string            `json:"notes,omitempty"`
		Pickups               []PublicOrderStop  `json:"pickups,omitempty"`
		Type                  string             `json:"type"`
		CrossdockWarehouseID  *string            `json:"crossdockWarehouseId,omitempty"`
		ReceivedTotalPackages *float64           `json:"receivedTotalPackages,omitempty"`
		ReceivedTotalPallets  *float64           `json:"receivedTotalPallets,omitempty"`
		CustomerID            *string            `json:"customerId,omitempty"`
	}

	PublicOrderStop struct {
		ExpectedAt            any               `json:"expectedAt"`
		Items                 []PublicOrderItem `json:"items"`
		ContactID             string            `json:"contactId"`
		DepartedAt            *string           `json:"departedAt,omitempty"`
		ReceivedTotalPackages *float64          `json:"receivedTotalPackages,omitempty"`
		Type                  string            `json:"type"`
		CrossdockWarehouseID  *string           `json:"crossdockWarehouseId,omitempty"`
		ReceivedTotalPallets  *float64          `json:"receivedTotalPallets,omitempty"`
	}

	PublicOrderItem struct {
		Commodity         *string  `json:"commodity,omitempty"`
		Description       string   `json:"description"`
		TotalWeightLbs    float64  `json:"totalWeightLbs"`
		UnitWeightLbs     *float64 `json:"unitWeightLbs,omitempty"`
		ReceivedTotes     float64  `json:"receivedTotes"`
		ProductNumber     *string  `json:"productNumber,omitempty"`
		ReceivedPallets   float64  `json:"receivedPallets"`
		UnitPrice         *float64 `json:"unitPrice,omitempty"`
		PickupNumber      *string  `json:"pickupNumber,omitempty"`
		UnitCost          *float64 `json:"unitCost,omitempty"`
		ReceivedWeight    float64  `json:"receivedWeight"`
		EmptyPalletWeight *float64 `json:"emptyPalletWeight,omitempty"`
		UnitsPerPallet    *int32   `json:"unitsPerPallet,omitempty"`
		TotalPackages     int      `json:"totalPackages"`
		PackagingType     *string  `json:"packagingType,omitempty"`
		UnitCostCurrency  *string  `json:"unitCostCurrency,omitempty"`
		UnitPriceCurrency *string  `json:"unitPriceCurrency,omitempty"`
		ReceivedPackages  float64  `json:"receivedPackages"`
		AppointmentNumber *string  `json:"appointmentNumber,omitempty"`
		AppointmentTime   *string  `json:"appointmentTime,omitempty"`
	}

	PublicComment struct {
		ID             string         `json:"id"`
		IsPublic       bool           `json:"isPublic"`
		Content        string         `json:"content"`
		Creator        ContactElastic `json:"creator"`
		CreationSource string         `json:"creationSource"`
		ContactID      *string        `json:"contactId,omitempty"`
		CreatedAtUNIX  any            `json:"createdAtUNIX"`
		OrderID        *string        `json:"orderId,omitempty"`
	}

	ContactElastic struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	PublicLineItem struct {
		ChargeCodeID    *string  `json:"chargeCodeId,omitempty"`
		Description     *string  `json:"description,omitempty"`
		Qty             *float64 `json:"qty,omitempty"`
		RateType        any      `json:"rateType,omitempty"`
		UnitRate        *float64 `json:"unitRate,omitempty"`
		IsAssessorial   bool     `json:"isAssessorial"`
		Amount          float64  `json:"amount"`
		ContactID       *string  `json:"contactId,omitempty"`
		Currency        string   `json:"currency"`
		ReferenceNumber *string  `json:"referenceNumber,omitempty"`
	}

	BranchSchemaBase struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		Slug     string `json:"slug"`
		IsActive bool   `json:"isActive"`
	}

	ExternalIDPublic struct {
		ID   string `json:"id"`
		Type string `json:"type"`
	}
)

// GetOrderByID implements the TMS interface
func (f *FreightFlow) GetOrderByID(ctx context.Context, orderID string) (order *models.Order, err error) {
	spanAttrs := append(
		otel.IntegrationAttrs(f.tms),
		attribute.String("order_id", orderID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetOrderByIDFreightFlow", spanAttrs)
	defer func() { metaSpan.End(err) }()

	var resp OrderResponse
	if err := f.get(ctx, fmt.Sprintf("/v2-beta/orders/%s", orderID), nil, &resp, s3backup.TypeLoads); err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	mappedOrder := f.mapOrder(resp.Order)
	return &mappedOrder, nil
}

// ListOrders implements the TMS interface
func (f *FreightFlow) ListOrders(ctx context.Context, queryParams url.Values) ([]models.Order, error) {
	var resp OrderListResponse
	if err := f.get(ctx, "/v2-beta/orders", queryParams, &resp, s3backup.TypeLoads); err != nil {
		return nil, fmt.Errorf("failed to list orders: %w", err)
	}

	var orders []models.Order
	for _, order := range resp.Orders {
		orders = append(orders, f.mapOrder(order))
	}

	return orders, nil
}

// mapOrder maps a FreightFlow order to a Drumkit order
func (f *FreightFlow) mapOrder(order Order) models.Order {
	return models.Order{
		OrderCoreInfo: models.OrderCoreInfo{
			ExternalOrderID:  order.ID,
			OrderTrackingID:  order.Name,
			Status:           "pending", // Default status
			Mode:             "FTL",     // Default mode
			MoreThanTwoStops: false,
			Notes: models.Notes{
				models.Note{
					Note: order.Notes,
				},
			},
			Specifications: models.Specifications{
				OrderType: order.Type,
			},
		},
		IsBooked:     false,
		IsVoided:     false,
		Reference:    order.Name,
		Type:         order.Type,
		CustomerID:   order.CompanyID,
		BillingItems: []models.BillingItem{}, // Empty for now
		CostItems:    []models.BillingItem{}, // Empty for now
	}
}

// GetOrderIDs implements the TMS interface
func (f *FreightFlow) GetOrderIDs(context.Context, models.SearchLoadsQuery) (orderIDs []string, err error) {
	return nil, util.NotImplemented(models.FreightFlow, "GetOrderIDs")
}

// PostOrder implements the TMS interface
func (f *FreightFlow) PostOrder(ctx context.Context, order *models.Order) error {
	spanAttrs := append(
		otel.IntegrationAttrs(f.tms),
		attribute.String("order_id", order.ExternalOrderID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "PostOrderFreightFlow", spanAttrs)
	defer func() { metaSpan.End(nil) }()

	// Convert Drumkit order to FreightFlow order request
	req := CreateOrderRequest{
		TotalWeightLbs: &order.TotalWeightLbs,
		Reference:      order.Reference,
		Type:           order.Type,
		SalesTeam:      order.SalesTeam,
		IsVoided:       order.IsVoided,
		Notes:          &order.Notes[0].Note,
		CustomerID:     &order.CustomerID,
	}

	// Add billing items if present
	if len(order.BillingItems) > 0 {
		req.BillingItems = make([]PublicLineItem, len(order.BillingItems))
		for i, item := range order.BillingItems {
			req.BillingItems[i] = PublicLineItem{
				Description:     &item.Description,
				Amount:          item.Amount,
				Currency:        item.Currency,
				IsAssessorial:   item.IsAssessorial,
				ReferenceNumber: &item.ReferenceNumber,
			}
		}
	}

	// Add cost items if present
	if len(order.CostItems) > 0 {
		req.CostItems = make([]PublicLineItem, len(order.CostItems))
		for i, item := range order.CostItems {
			req.CostItems[i] = PublicLineItem{
				Description:     &item.Description,
				Amount:          item.Amount,
				Currency:        item.Currency,
				IsAssessorial:   item.IsAssessorial,
				ReferenceNumber: &item.ReferenceNumber,
			}
		}
	}

	// Add pickups if present
	if len(order.Pickups) > 0 {
		req.Pickups = make([]PublicOrderStop, len(order.Pickups))
		for i, pickup := range order.Pickups {
			req.Pickups[i] = PublicOrderStop{
				ContactID: pickup.ContactID,
				Type:      "pickup",
				Items: []PublicOrderItem{
					{
						Description:    pickup.Items[0].Description,
						TotalWeightLbs: pickup.Items[0].TotalWeightLbs,
						TotalPackages:  pickup.Items[0].TotalPackages,
					},
				},
			}
		}
	}

	// Add deliveries if present
	if len(order.Deliveries) > 0 {
		req.Deliveries = make([]PublicOrderStop, len(order.Deliveries))
		for i, delivery := range order.Deliveries {
			req.Deliveries[i] = PublicOrderStop{
				ContactID: delivery.ContactID,
				Type:      "delivery",
				Items: []PublicOrderItem{
					{
						Description:    delivery.Items[0].Description,
						TotalWeightLbs: delivery.Items[0].TotalWeightLbs,
						TotalPackages:  delivery.Items[0].TotalPackages,
					},
				},
			}
		}
	}

	// Make the API call
	var resp OrderResponse
	if err := f.post(ctx, "/v2-beta/orders", nil, req, &resp, nil, s3backup.TypeLoads); err != nil {
		return fmt.Errorf("failed to create order: %w", err)
	}

	// Update the order with the response data
	order.ExternalOrderID = resp.Order.ID
	order.OrderTrackingID = resp.Order.Name

	return nil
}

// CreateOrder implements the TMS interface
func (f *FreightFlow) CreateOrder(context.Context, models.Order) (models.Order, error) {
	return models.Order{}, util.NotImplemented(models.FreightFlow, "CreateOrder")
}

// GetDefaultOrderAttributes implements the TMS interface
func (f *FreightFlow) GetDefaultOrderAttributes() models.OrderAttributes {
	return models.OrderAttributes{
		OrderCoreInfoAttributes: models.OrderCoreInfoAttributes{
			Status:           models.FieldAttributes{IsReadOnly: true},
			Mode:             models.FieldAttributes{IsReadOnly: true},
			MoreThanTwoStops: models.FieldAttributes{IsReadOnly: true},
			Customer: models.CustomerAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsReadOnly: true},
					City:          models.FieldAttributes{IsReadOnly: true},
					State:         models.FieldAttributes{IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsReadOnly: true},
					Country:       models.FieldAttributes{IsReadOnly: true},
					Contact:       models.FieldAttributes{IsReadOnly: true},
					Phone:         models.FieldAttributes{IsReadOnly: true},
					Email:         models.FieldAttributes{IsReadOnly: true},
				},
				RefNumber: models.FieldAttributes{IsReadOnly: true},
			},
			BillTo: models.BillToAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsReadOnly: true},
					City:          models.FieldAttributes{IsReadOnly: true},
					State:         models.FieldAttributes{IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsReadOnly: true},
					Country:       models.FieldAttributes{IsReadOnly: true},
					Contact:       models.FieldAttributes{IsReadOnly: true},
					Phone:         models.FieldAttributes{IsReadOnly: true},
					Email:         models.FieldAttributes{IsReadOnly: true},
				},
			},
			RateData:   models.InitUnsupportedRateData,
			Pickups:    []models.OrderStopAttributes{},
			Deliveries: []models.OrderStopAttributes{},
			Pickup: models.PickupAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsReadOnly: true},
					City:          models.FieldAttributes{IsReadOnly: true},
					State:         models.FieldAttributes{IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsReadOnly: true},
					Country:       models.FieldAttributes{IsReadOnly: true},
					Contact:       models.FieldAttributes{IsReadOnly: true},
					Phone:         models.FieldAttributes{IsReadOnly: true},
					Email:         models.FieldAttributes{IsReadOnly: true},
				},
				ExternalTMSStopID: models.FieldAttributes{IsReadOnly: true},
				BusinessHours:     models.FieldAttributes{IsReadOnly: true},
				RefNumber:         models.FieldAttributes{IsReadOnly: true},
				ReadyTime:         models.FieldAttributes{IsReadOnly: true},
				ApptRequired:      models.FieldAttributes{IsReadOnly: true},
				ApptType:          models.FieldAttributes{IsReadOnly: true},
				ApptStartTime:     models.FieldAttributes{IsReadOnly: true},
				ApptEndTime:       models.FieldAttributes{IsReadOnly: true},
				ApptNote:          models.FieldAttributes{IsReadOnly: true},
				Timezone:          models.FieldAttributes{IsReadOnly: true},
				ZipPrefix:         models.FieldAttributes{IsReadOnly: true},
			},
			Consignee: models.ConsigneeAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsReadOnly: true},
					City:          models.FieldAttributes{IsReadOnly: true},
					State:         models.FieldAttributes{IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsReadOnly: true},
					Country:       models.FieldAttributes{IsReadOnly: true},
					Contact:       models.FieldAttributes{IsReadOnly: true},
					Phone:         models.FieldAttributes{IsReadOnly: true},
					Email:         models.FieldAttributes{IsReadOnly: true},
				},
				ExternalTMSStopID: models.FieldAttributes{IsReadOnly: true},
				BusinessHours:     models.FieldAttributes{IsReadOnly: true},
				RefNumber:         models.FieldAttributes{IsReadOnly: true},
				MustDeliver:       models.FieldAttributes{IsReadOnly: true},
				ApptRequired:      models.FieldAttributes{IsReadOnly: true},
				ApptType:          models.FieldAttributes{IsReadOnly: true},
				ApptStartTime:     models.FieldAttributes{IsReadOnly: true},
				ApptEndTime:       models.FieldAttributes{IsReadOnly: true},
				ApptNote:          models.FieldAttributes{IsReadOnly: true},
				Timezone:          models.FieldAttributes{IsReadOnly: true},
				ZipPrefix:         models.FieldAttributes{IsReadOnly: true},
			},
			Specifications: models.SpecificationsAttributes{
				OrderType: models.FieldAttributes{IsReadOnly: true},
			},
			Notes: models.NoteAttributes{
				Note: models.FieldAttributes{IsReadOnly: true},
			},
			PickupDate:  models.FieldAttributes{IsReadOnly: true},
			DropoffDate: models.FieldAttributes{IsReadOnly: true},
		},
		LoadID:                models.FieldAttributes{IsReadOnly: true},
		Load:                  models.FieldAttributes{IsReadOnly: true},
		RequestedPickupDate:   models.FieldAttributes{IsReadOnly: true},
		RequestedDeliveryDate: models.FieldAttributes{IsReadOnly: true},
		Priority:              models.FieldAttributes{IsReadOnly: true},
		IsFulfilled:           models.FieldAttributes{IsReadOnly: true},
		ServiceID:             models.FieldAttributes{IsReadOnly: true},
		Service:               models.FieldAttributes{IsReadOnly: true},
		OrganizationName:      models.FieldAttributes{IsReadOnly: true},
		OrderTMSStatus:        models.FieldAttributes{IsReadOnly: true},
		IsInPlanning:          models.FieldAttributes{IsReadOnly: true},
		IsHot:                 models.FieldAttributes{IsReadOnly: true},
		BillingStatus:         models.FieldAttributes{IsReadOnly: true},
		IntegrationStatus:     models.FieldAttributes{IsReadOnly: true},
		DoNotOverwrite:        models.FieldAttributes{IsReadOnly: true},
		NeedsReview:           models.FieldAttributes{IsReadOnly: true},
		ExternalReferences:    models.FieldAttributes{IsReadOnly: true},
		IsBooked:              models.FieldAttributes{IsReadOnly: true},
		IsVoided:              models.FieldAttributes{IsReadOnly: true},
		Reference:             models.FieldAttributes{IsReadOnly: true},
		SalesTeam:             models.FieldAttributes{IsReadOnly: true},
		BillingItems:          models.FieldAttributes{IsReadOnly: true},
		CostItems:             models.FieldAttributes{IsReadOnly: true},
		Branches:              models.FieldAttributes{IsReadOnly: true},
		TruckBrokerID:         models.FieldAttributes{IsReadOnly: true},
		CarrierID:             models.FieldAttributes{IsReadOnly: true},
		CreatorID:             models.FieldAttributes{IsReadOnly: true},
		Type:                  models.FieldAttributes{IsReadOnly: true},
		CrossdockType:         models.FieldAttributes{IsReadOnly: true},
		CustomerID:            models.FieldAttributes{IsReadOnly: true},
	}
}

// UpdateOrder updates an existing order in FreightFlow
func (f *FreightFlow) UpdateOrder(context.Context, models.Order) (models.Order, error) {
	// Implementation would depend on FreightFlow API capabilities
	return models.Order{}, nil
}

// DeleteOrder deletes an order from FreightFlow
func (f *FreightFlow) DeleteOrder(context.Context, string) error {
	// Implementation would depend on FreightFlow API capabilities
	return nil
}

// AssociateLoadWithOrder associates a load with an order in FreightFlow
func (f *FreightFlow) AssociateLoadWithOrder(context.Context, uint, uint) error {
	// Implementation would depend on FreightFlow API capabilities
	return nil
}

// GetOrders implements the TMS interface
func (f *FreightFlow) GetOrders(context.Context, time.Time, time.Time) (orders []*models.Order, err error) {
	// Implementation would depend on FreightFlow API capabilities
	// For now, we'll return an empty slice
	return nil, nil
}

// GetOrder implements the TMS interface
func (f *FreightFlow) GetOrder(
	ctx context.Context,
	externalTMSID string,
) (order *models.Order, attrs models.OrderAttributes, err error) {
	spanAttrs := append(
		otel.IntegrationAttrs(f.tms),
		attribute.String("external_tms_id", externalTMSID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetOrderFreightFlow", spanAttrs)
	defer func() { metaSpan.End(err) }()

	orderPtr, err := f.GetOrderByID(ctx, externalTMSID)
	if err != nil {
		return nil, models.OrderAttributes{}, err
	}
	return orderPtr, f.GetDefaultOrderAttributes(), nil
}
