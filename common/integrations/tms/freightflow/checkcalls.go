package freightflow

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

// PostCheckCall implements the TMS interface
func (f *FreightFlow) PostCheckCall(
	context.Context,
	*models.Load,
	models.CheckCall,
) (err error) {
	return util.NotImplemented(models.FreightFlow, "PostCheckCall")
}

// GetCheckCallsHistory implements the TMS interface
func (f *FreightFlow) GetCheckCallsHistory(
	context.Context,
	uint,
	string,
) (calls []models.CheckCall, err error) {
	return nil, util.NotImplemented(models.FreightFlow, "GetCheckCallsHistory")
}
