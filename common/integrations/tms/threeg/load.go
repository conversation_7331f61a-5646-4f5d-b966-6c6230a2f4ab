package threeg

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loaddb "github.com/drumkitai/drumkit/common/rds/load"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

// Address represents a physical address
type Address struct {
	Line1   string `xml:"line1"`
	Line2   string `xml:"line2"`
	City    string `xml:"city"`
	State   string `xml:"state"`
	Zipcode string `xml:"zipcode"`
	Country string `xml:"country"`
}

// Rate represents rate information with type, line haul, units, and currency
type Rate struct {
	Type        string  `xml:"type"`
	LineHaul    float32 `xml:"lineHaul"`
	NumUnits    float32 `xml:"numUnits"`
	Unit        string  `xml:"unit"`
	TotalCharge float32 `xml:"totalCharge"`
	TotalCost   float32 `xml:"totalCost"`
	Currency    string  `xml:"currency"`
}

// Note represents a note with its metadata
type Note struct {
	Note      string `xml:"note"`
	CreatedAt string `xml:"createdAt"`
	UpdatedBy string `xml:"updatedBy"`
	Source    string `xml:"source"`
}

// ErrorResponse represents the error response from 3G TMS
type ErrorResponse struct {
	XMLName      xml.Name `xml:"ExportLoadsResponse"`
	Result       string   `xml:"Result"`
	ErrorMessage string   `xml:"ErrorMessage"`
	ExportError  struct {
		Message    string `xml:"Message"`
		Cause      string `xml:"Cause"`
		EntityType string `xml:"EntityType"`
		ThreadName string `xml:"ThreadName"`
		ServerName string `xml:"ServerName"`
		ServerTime string `xml:"ServerTime"`
	} `xml:"ExportError"`
}

// LoadResponse represents the XML response from 3G TMS for a load
type LoadResponse struct {
	XMLName    xml.Name `xml:"LoadResponse"`
	LoadID     string   `xml:"loadId"`
	LoadNumber string   `xml:"loadNumber"`
	ProNumber  string   `xml:"proNumber"`
	BOLNumber  string   `xml:"bolNumber"`
	Status     string   `xml:"status"`
	Mode       string   `xml:"mode"`
	PONumbers  string   `xml:"poNumbers"`
	OrderNums  []string `xml:"OrderNums>OrderNum"`
	Operator   string   `xml:"operator"`

	// Customer information
	Customer Company `xml:"customer"`

	// Pickup information
	Pickup Location `xml:"pickup"`

	// Delivery information
	Delivery DeliveryLocation `xml:"delivery"`

	// Carrier information
	Carrier struct {
		ID        string `xml:"id"`
		Name      string `xml:"name"`
		MCNumber  string `xml:"mcNumber"`
		DOTNumber string `xml:"dotNumber"`
		SCAC      string `xml:"scac"`
		Phone     string `xml:"phone"`
		Email     string `xml:"email"`
		Notes     string `xml:"notes"`

		DriverName    string `xml:"driverName"`
		DriverPhone   string `xml:"driverPhone"`
		Dispatcher    string `xml:"dispatcher"`
		TruckNumber   string `xml:"truckNumber"`
		TrailerNumber string `xml:"trailerNumber"`
	} `xml:"carrier"`

	// Specifications
	Specifications Specifications `xml:"specifications"`

	// Rate data
	RateData struct {
		CustomerRate  Rate    `xml:"customerRate"`
		CarrierRate   Rate    `xml:"carrierRate"`
		FSCPercent    float32 `xml:"fscPercent"`
		NetProfit     float32 `xml:"netProfit"`
		ProfitPercent float32 `xml:"profitPercent"`
	} `xml:"rateData"`

	// Notes
	Notes []Note `xml:"notes>note"`

	// Orders
	Orders struct {
		Order []struct {
			EntityHeader struct {
				DateCreated      string `xml:"DateCreated"`
				CreatedBy        string `xml:"CreatedBy"`
				DateLastModified string `xml:"DateLastModified"`
				LastModifiedBy   string `xml:"LastModifiedBy"`
			} `xml:"EntityHeader"`
			OrganizationName     string `xml:"OrganizationName"`
			OrdNum               string `xml:"OrdNum"`
			OrdType              string `xml:"OrdType"`
			FreightTerms         string `xml:"FreightTerms"`
			OrderTMSStatus       string `xml:"OrderTMSStatus"`
			IsPrePayment         bool   `xml:"IsPrePayment"`
			ScheduledEarlyPickup string `xml:"ScheduledEarlyPickup"`
			ScheduledLatePickup  string `xml:"ScheduledLatePickup"`
			TotalGrossWeight     struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalGrossWeight"`
			TotalNetWeight struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalNetWeight"`
			TotalGrossVolume struct {
				VolumeValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"VolumeValue"`
			} `xml:"TotalGrossVolume"`
			TotalNetVolume struct {
				VolumeValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"VolumeValue"`
			} `xml:"TotalNetVolume"`
			TotalPieceCount       int    `xml:"TotalPieceCount"`
			Currency              string `xml:"Currency"`
			TotalNetFreightCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetFreightCharge"`
			TotalNetAccessorialCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetAccessorialCharge"`
			TotalNetCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetCharge"`
			IsHazmat      bool   `xml:"IsHazmat"`
			BillingStatus string `xml:"BillingStatus"`
			Client        struct {
				TradingPartnerName string `xml:"TradingPartnerName"`
				TradingPartnerNum  string `xml:"TradingPartnerNum"`
			} `xml:"Client"`
			OrderComments struct {
				Comment []struct {
					Qualifier struct {
						QualifierName string `xml:"QualifierName"`
						Description   string `xml:"Description"`
						QualifierType string `xml:"QualifierType"`
					} `xml:"Qualifier"`
					CommentValue string `xml:"CommentValue"`
				} `xml:"Comment"`
			} `xml:"OrderComments"`
			OrderRefNums struct {
				RefNum []struct {
					Qualifier struct {
						QualifierName string `xml:"QualifierName"`
						Description   string `xml:"Description"`
						QualifierType string `xml:"QualifierType"`
					} `xml:"Qualifier"`
					RefNumValue string `xml:"RefNumValue"`
				} `xml:"RefNum"`
			} `xml:"OrderRefNums"`
			DestinationContact struct {
				ContactNum  string `xml:"ContactNum"`
				ContactName string `xml:"ContactName"`
				Phone1      string `xml:"Phone1"`
				Email       string `xml:"Email"`
				IsActive    bool   `xml:"IsActive"`
			} `xml:"DestinationContact"`
		} `xml:"Order"`
	} `xml:"Orders"`
}

// LoadData represents the root XML response from 3G TMS
type LoadData struct {
	XMLName   xml.Name `xml:"http://schemas.3gtms.com/tms/v1/tns LoadData"`
	BatchInfo struct {
		BatchDateTime   string `xml:"BatchDateTime"`
		SentBy          string `xml:"SentBy"`
		PageNum         int    `xml:"PageNum"`
		PageCnt         int    `xml:"PageCnt"`
		EcosystemAction string `xml:"EcosystemAction"`
	} `xml:"BatchInfo"`
	Loads struct {
		Load []struct {
			EntityHeader struct {
				DateCreated      string `xml:"DateCreated"`
				CreatedBy        string `xml:"CreatedBy"`
				DateLastModified string `xml:"DateLastModified"`
				LastModifiedBy   string `xml:"LastModifiedBy"`
			} `xml:"EntityHeader"`
			OrganizationName         string                `xml:"OrganizationName"`
			LoadNum                  string                `xml:"LoadNum"`
			FreightTerms             string                `xml:"FreightTerms"`
			PlannedStart             string                `xml:"PlannedStart"`
			PlannedEnd               string                `xml:"PlannedEnd"`
			TransitTime              int                   `xml:"TransitTime"`
			Currency                 string                `xml:"Currency"`
			TradingPartnerClientName string                `xml:"TradingPartnerClientName"`
			TradingPartnerCarrier    TradingPartnerCarrier `xml:"TradingPartnerCarrier"`
			Client                   struct {
				EntityHeader struct {
					DateCreated      string `xml:"DateCreated"`
					CreatedBy        string `xml:"CreatedBy"`
					DateLastModified string `xml:"DateLastModified"`
					LastModifiedBy   string `xml:"LastModifiedBy"`
				} `xml:"EntityHeader"`
				OrganizationName   string `xml:"OrganizationName"`
				TradingPartnerName string `xml:"TradingPartnerName"`
				TradingPartnerNum  string `xml:"TradingPartnerNum"`
				TradingPartnerType string `xml:"TradingPartnerType"`
				IsActive           bool   `xml:"IsActive"`
				Currency           string `xml:"Currency"`
			} `xml:"Client"`
			TotalNetFreightCost struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetFreightCost"`
			TotalNetAccessorialCost struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetAccessorialCost"`
			TotalNetCost struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetCost"`
			TotalDistance struct {
				DistanceValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"DistanceValue"`
			} `xml:"TotalDistance"`
			TotalGrossWeight struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalGrossWeight"`
			TotalNetWeight struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalNetWeight"`
			TotalPieceCount int    `xml:"TotalPieceCount"`
			LoadTMSStatus   string `xml:"LoadTMSStatus"`
			TransportMode   string `xml:"TransportMode"`
			Stops           struct {
				Stop []struct {
					StopNum          int    `xml:"StopNum"`
					StopType         string `xml:"StopType"`
					PlannedArrival   string `xml:"PlannedArrival"`
					PlannedDeparture string `xml:"PlannedDeparture"`
					StopLocInfo      struct {
						OrganizationName string `xml:"OrganizationName"`
						LocNum           string `xml:"LocNum"`
						AddrName         string `xml:"AddrName"`
						Addr1            string `xml:"Addr1"`
						Addr2            string `xml:"Addr2"`
						CityName         string `xml:"CityName"`
						StateCode        string `xml:"StateCode"`
						CountryISO2      string `xml:"CountryISO2"`
						PostalCode       string `xml:"PostalCode"`
						Latitude         string `xml:"Latitude"`
						Longitude        string `xml:"Longitude"`
					} `xml:"StopLocInfo"`
					OrderNums struct {
						OrderNum []string `xml:"OrderNum"`
					} `xml:"OrderNums"`
				} `xml:"Stop"`
			} `xml:"Stops"`
			AvailableEquipmentRatedName string `xml:"AvailableEquipmentRatedName"`
		} `xml:"Load"`
	} `xml:"Loads"`
	Orders struct {
		Order []struct {
			EntityHeader struct {
				DateCreated      string `xml:"DateCreated"`
				CreatedBy        string `xml:"CreatedBy"`
				DateLastModified string `xml:"DateLastModified"`
				LastModifiedBy   string `xml:"LastModifiedBy"`
			} `xml:"EntityHeader"`
			OrganizationName     string `xml:"OrganizationName"`
			OrdNum               string `xml:"OrdNum"`
			OrdType              string `xml:"OrdType"`
			FreightTerms         string `xml:"FreightTerms"`
			OrderTMSStatus       string `xml:"OrderTMSStatus"`
			IsPrePayment         bool   `xml:"IsPrePayment"`
			ScheduledEarlyPickup string `xml:"ScheduledEarlyPickup"`
			ScheduledLatePickup  string `xml:"ScheduledLatePickup"`
			TotalGrossWeight     struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalGrossWeight"`
			TotalNetWeight struct {
				WeightValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"WeightValue"`
			} `xml:"TotalNetWeight"`
			TotalGrossVolume struct {
				VolumeValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"VolumeValue"`
			} `xml:"TotalGrossVolume"`
			TotalNetVolume struct {
				VolumeValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"VolumeValue"`
			} `xml:"TotalNetVolume"`
			TotalPieceCount       int    `xml:"TotalPieceCount"`
			Currency              string `xml:"Currency"`
			TotalNetFreightCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetFreightCharge"`
			TotalNetAccessorialCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetAccessorialCharge"`
			TotalNetCharge struct {
				CurrencyValue struct {
					Value string `xml:",chardata"`
					UOM   string `xml:"uom,attr"`
				} `xml:"CurrencyValue"`
			} `xml:"TotalNetCharge"`
			IsHazmat      bool   `xml:"IsHazmat"`
			BillingStatus string `xml:"BillingStatus"`
			Client        struct {
				TradingPartnerName string `xml:"TradingPartnerName"`
				TradingPartnerNum  string `xml:"TradingPartnerNum"`
			} `xml:"Client"`
			OrderComments struct {
				Comment []struct {
					Qualifier struct {
						QualifierName string `xml:"QualifierName"`
						Description   string `xml:"Description"`
						QualifierType string `xml:"QualifierType"`
					} `xml:"Qualifier"`
					CommentValue string `xml:"CommentValue"`
				} `xml:"Comment"`
			} `xml:"OrderComments"`
			OrderRefNums struct {
				RefNum []struct {
					Qualifier struct {
						QualifierName string `xml:"QualifierName"`
						Description   string `xml:"Description"`
						QualifierType string `xml:"QualifierType"`
					} `xml:"Qualifier"`
					RefNumValue string `xml:"RefNumValue"`
				} `xml:"RefNum"`
			} `xml:"OrderRefNums"`
			DestinationContact struct {
				ContactNum  string `xml:"ContactNum"`
				ContactName string `xml:"ContactName"`
				Phone1      string `xml:"Phone1"`
				Email       string `xml:"Email"`
				IsActive    bool   `xml:"IsActive"`
			} `xml:"DestinationContact"`
		} `xml:"Order"`
	} `xml:"Orders"`
}

// TradingPartnerCarrier represents the TradingPartnerCarrier XML structure
type TradingPartnerCarrier struct {
	EntityHeader struct {
		DateCreated      string `xml:"DateCreated"`
		CreatedBy        string `xml:"CreatedBy"`
		DateLastModified string `xml:"DateLastModified"`
		LastModifiedBy   string `xml:"LastModifiedBy"`
	} `xml:"EntityHeader"`
	OrganizationName     string `xml:"OrganizationName"`
	TradingPartnerName   string `xml:"TradingPartnerName"`
	TradingPartnerNum    string `xml:"TradingPartnerNum"`
	TradingPartnerType   string `xml:"TradingPartnerType"`
	IsActive             bool   `xml:"IsActive"`
	Currency             string `xml:"Currency"`
	TradingPartnerDetail struct {
		TradingPartnerCarrier struct {
			Scac                        string `xml:"Scac"`
			MccNum                      string `xml:"MccNum"`
			MccNumPrefix                string `xml:"MccNumPrefix"`
			InterstateCcID              string `xml:"InterstateCcId"`
			ApVendorNum                 string `xml:"ApVendorNum"`
			HasW9                       bool   `xml:"HasW9"`
			ProNumAutoAssignAction      string `xml:"ProNumAutoAssignAction"`
			TrackingLinkURL             string `xml:"TrackingLinkUrl"`
			IsRailway                   bool   `xml:"IsRailway"`
			IsRailDrayage               bool   `xml:"IsRailDrayage"`
			IsPortDrayage               bool   `xml:"IsPortDrayage"`
			ServicesAirport             bool   `xml:"ServicesAirport"`
			HasTiaWatchdogReport        bool   `xml:"HasTiaWatchdogReport"`
			IsUiaaCertified             bool   `xml:"IsUiaaCertified"`
			IsCarbCompliant             bool   `xml:"IsCarbCompliant"`
			IsSmartWayCertified         bool   `xml:"IsSmartWayCertified"`
			IsCtpatCertified            bool   `xml:"IsCtpatCertified"`
			HasTwicDrivers              bool   `xml:"HasTwicDrivers"`
			HasTsaDrivers               bool   `xml:"HasTsaDrivers"`
			IsFastCertified             bool   `xml:"IsFastCertified"`
			HasTeams                    bool   `xml:"HasTeams"`
			HasReeferEquipment          bool   `xml:"HasReeferEquipment"`
			HandlesOverweight           bool   `xml:"HandlesOverweight"`
			HandlesOversize             bool   `xml:"HandlesOversize"`
			HasTankerEndorsement        bool   `xml:"HasTankerEndorsement"`
			IsHazmatCertified           bool   `xml:"IsHazmatCertified"`
			IsBonded                    bool   `xml:"IsBonded"`
			HazmatCertificationNum      string `xml:"HazmatCertificationNum"`
			TransportationExportBondNum string `xml:"TransportationExportBondNum"`
			WsibNum                     string `xml:"WsibNum"`
			WsibExpirationDate          string `xml:"WsibExpirationDate"`
			LockIsActive                bool   `xml:"LockIsActive"`
			LockDoNotAssign             bool   `xml:"LockDoNotAssign"`
			IsFreightForwarder          bool   `xml:"IsFreightForwarder"`
			CarrierSafety               struct {
				PublicLiabilityLimit struct {
					CurrencyValue struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyValue"`
					CurrencyBase struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyBase"`
				} `xml:"PublicLiabilityLimit"`
				PublicLiabilityExpiration string `xml:"PublicLiabilityExpiration"`
				ProductLiabilityLimit     struct {
					CurrencyValue struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyValue"`
					CurrencyBase struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyBase"`
				} `xml:"ProductLiabilityLimit"`
				ProductLiabilityExpiration string `xml:"ProductLiabilityExpiration"`
				WorkersCompLimit           struct {
					CurrencyValue struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyValue"`
					CurrencyBase struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyBase"`
				} `xml:"WorkersCompLimit"`
				WorkersCompExpiration string `xml:"WorkersCompExpiration"`
				WaiveWorkersComp      bool   `xml:"WaiveWorkersComp"`
				AutoInsuranceLimit    struct {
					CurrencyValue struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyValue"`
					CurrencyBase struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyBase"`
				} `xml:"AutoInsuranceLimit"`
				AutoInsuranceExpiration               string `xml:"AutoInsuranceExpiration"`
				DataLastUpdated                       string `xml:"DataLastUpdated"`
				DataCurrentAsOf                       string `xml:"DataCurrentAsOf"`
				UnsafeDriving                         string `xml:"UnsafeDriving"`
				UnderInvestigationUnsafeDriving       bool   `xml:"UnderInvestigationUnsafeDriving"`
				ScoreUnsafeDriving                    string `xml:"ScoreUnsafeDriving"`
				IsAlertOnUnsafeDriving                bool   `xml:"IsAlertOnUnsafeDriving"`
				FatiguedDriving                       string `xml:"FatiguedDriving"`
				UnderInvestigationFatiguedDriving     bool   `xml:"UnderInvestigationFatiguedDriving"`
				ScoreFatiguedDriving                  string `xml:"ScoreFatiguedDriving"`
				IsAlertOnFatiguedDriving              bool   `xml:"IsAlertOnFatiguedDriving"`
				DriverFitness                         string `xml:"DriverFitness"`
				UnderInvestigationDriverFitness       bool   `xml:"UnderInvestigationDriverFitness"`
				ScoreDriverFitness                    string `xml:"ScoreDriverFitness"`
				IsAlertOnDriverFitness                bool   `xml:"IsAlertOnDriverFitness"`
				ControlledSubstance                   string `xml:"ControlledSubstance"`
				UnderInvestigationControlledSubstance bool   `xml:"UnderInvestigationControlledSubstance"`
				ScoreControlledSubstance              string `xml:"ScoreControlledSubstance"`
				IsAlertOnControlledSubstance          bool   `xml:"IsAlertOnControlledSubstance"`
				VehicleMaintenance                    string `xml:"VehicleMaintenance"`
				UnderInvestigationVehicleMaintenance  bool   `xml:"UnderInvestigationVehicleMaintenance"`
				ScoreVehicleMaintenance               string `xml:"ScoreVehicleMaintenance"`
				IsAlertOnVehicleMaintenance           bool   `xml:"IsAlertOnVehicleMaintenance"`
				BypassCarrierSafetyValidation         bool   `xml:"BypassCarrierSafetyValidation"`
				BypassCarrierInsuranceValidation      bool   `xml:"BypassCarrierInsuranceValidation"`
				IsMonitored                           bool   `xml:"IsMonitored"`
				PowerUnitCount                        string `xml:"PowerUnitCount"`
				DriverCount                           string `xml:"DriverCount"`
				DistAnnual                            string `xml:"Dist_Annual"`
				AnnualDistanceYear                    string `xml:"AnnualDistanceYear"`
				IsCargoCoverageRequired               bool   `xml:"IsCargoCoverageRequired"`
				IsBondSuretyRequired                  bool   `xml:"IsBondSuretyRequired"`
				HasBrokerBond                         bool   `xml:"HasBrokerBond"`
				SafeyReviewDueDate                    string `xml:"SafeyReviewDueDate"`
				OutOfServiceDate                      string `xml:"OutOfServiceDate"`
				RevocationDate                        string `xml:"RevocationDate"`
				ReinstateDate                         string `xml:"ReinstateDate"`
				DotProfileDate                        string `xml:"DotProfileDate"`
				IntraStateOperatingNum                string `xml:"IntraStateOperatingNum"`
				RemediationPlanStatus                 string `xml:"RemediationPlanStatus"`
				RemediationReviewDate                 string `xml:"RemediationReviewDate"`
				SeaDriverScore                        string `xml:"SeaDriverScore"`
				SeaVehicleScore                       string `xml:"SeaVehicleScore"`
				SeaManagementScore                    string `xml:"SeaManagementScore"`
				FmcsaSafetyRatingDate                 string `xml:"FmcsaSafetyRatingDate"`
				FmcsaSafetyReviewDate                 string `xml:"FmcsaSafetyReviewDate"`
				IsContractCarrierPending              bool   `xml:"IsContractCarrierPending"`
				IsContractCarrierRevoked              bool   `xml:"IsContractCarrierRevoked"`
				IsCommonCarrierPending                bool   `xml:"IsCommonCarrierPending"`
				IsCommonCarrierRevoked                bool   `xml:"IsCommonCarrierRevoked"`
				IsBrokerPending                       bool   `xml:"IsBrokerPending"`
				IsBrokerRevoked                       bool   `xml:"IsBrokerRevoked"`
				DoNotAssign                           bool   `xml:"DoNotAssign"`
				HasSeriousViolation                   bool   `xml:"HasSeriousViolation"`
				BipdRequiredAmount                    struct {
					CurrencyValue struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyValue"`
					CurrencyBase struct {
						Value string `xml:",chardata"`
						UOM   string `xml:"uom,attr"`
					} `xml:"CurrencyBase"`
				} `xml:"BipdRequiredAmount"`
				CarrierAssignmentStatusSafety    string `xml:"CarrierAssignmentStatusSafety"`
				CarrierAssignmentStatusInsurance string `xml:"CarrierAssignmentStatusInsurance"`
				SafetyServiceOverrideDate        string `xml:"SafetyServiceOverrideDate"`
				SafetyServiceCsaBasics           string `xml:"SafetyServiceCsaBasics"`
				SafetyServiceInspectionDate      string `xml:"SafetyServiceInspectionDate"`
				SafetyServiceInsCertificatesDate string `xml:"SafetyServiceInsCertificatesDate"`
				SafetyServiceDotInsuranceDate    string `xml:"SafetyServiceDotInsuranceDate"`
				HasAuthorityMexico               bool   `xml:"HasAuthorityMexico"`
				HasAuthorityCanada               bool   `xml:"HasAuthorityCanada"`
				HasReeferBreakdownInsurance      bool   `xml:"HasReeferBreakdownInsurance"`
			} `xml:"CarrierSafety"`
		} `xml:"TradingPartnerCarrier"`
	} `xml:"TradingPartnerDetail"`
}

// GetLoad retrieves a load from 3G TMS by its ID
func (t *ThreeG) GetLoad(ctx context.Context, externalTMSID string) (models.Load, models.LoadAttributes, error) {
	url := fmt.Sprintf("%s/loads/?loadNum=%s", t.baseURL, externalTMSID)
	url = t.addAuthParams(url)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error reading response body: %w", err)
	}

	// Try to unmarshal the response
	var loadData LoadData
	if err := xml.Unmarshal(body, &loadData); err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error decoding response: %w", err)
	}

	// Check if we have any loads
	if len(loadData.Loads.Load) == 0 {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("no load found with ID %s", externalTMSID)
	}

	// Get the first load (should be the only one)
	load := loadData.Loads.Load[0]

	// Convert the 3G TMS load to our load model
	result := models.Load{
		ExternalTMSID:     load.LoadNum,
		FreightTrackingID: load.LoadNum,
		ServiceID:         t.integration.ServiceID,
		TMSID:             t.integration.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:   load.LoadTMSStatus,
			Mode:     models.LoadMode(load.TransportMode),
			Operator: load.EntityHeader.CreatedBy,
			Carrier: models.Carrier{
				Name:      load.TradingPartnerCarrier.TradingPartnerName,
				MCNumber:  load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.MccNum,
				DOTNumber: load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.InterstateCcID,
				SCAC:      load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.Scac,
			},
		},
	}

	// Map customer data
	if load.OrganizationName != "" {
		result.Customer = models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: load.OrganizationName,
			},
		}
	}

	// Map bill-to data (using customer data as bill-to for now)
	if load.TradingPartnerClientName != "" {
		result.BillTo = models.BillTo{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: load.TradingPartnerClientName,
			},
		}
	}

	// Add pickup and delivery information if available
	if len(load.Stops.Stop) >= 2 {
		pickup := load.Stops.Stop[0]
		delivery := load.Stops.Stop[1]

		result.Pickup = models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         pickup.StopLocInfo.AddrName,
				AddressLine1: pickup.StopLocInfo.Addr1,
				AddressLine2: pickup.StopLocInfo.Addr2,
				City:         pickup.StopLocInfo.CityName,
				State:        pickup.StopLocInfo.StateCode,
				Zipcode:      pickup.StopLocInfo.PostalCode,
				Country:      pickup.StopLocInfo.CountryISO2,
			},
		}

		result.Consignee = models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         delivery.StopLocInfo.AddrName,
				AddressLine1: delivery.StopLocInfo.Addr1,
				AddressLine2: delivery.StopLocInfo.Addr2,
				City:         delivery.StopLocInfo.CityName,
				State:        delivery.StopLocInfo.StateCode,
				Zipcode:      delivery.StopLocInfo.PostalCode,
				Country:      delivery.StopLocInfo.CountryISO2,
			},
		}

		// Map the appointment times to carrier fields
		result.Carrier.PickupStart = parseTime(pickup.PlannedArrival)
		result.Carrier.PickupEnd = parseTime(pickup.PlannedDeparture)
		result.Carrier.DeliveryStart = parseTime(delivery.PlannedArrival)
		result.Carrier.DeliveryEnd = parseTime(delivery.PlannedDeparture)
	}

	// Add specifications
	result.Specifications = models.Specifications{
		TotalWeight: models.ValueUnit{
			Val:  parseFloat(load.TotalGrossWeight.WeightValue.Value),
			Unit: load.TotalGrossWeight.WeightValue.UOM,
		},
		TotalDistance: models.ValueUnit{
			Val:  parseFloat(load.TotalDistance.DistanceValue.Value),
			Unit: load.TotalDistance.DistanceValue.UOM,
		},
		TotalPieces: models.ValueUnit{
			Val:  float32(load.TotalPieceCount),
			Unit: "pieces",
		},
		TransportType: load.AvailableEquipmentRatedName,
	}

	// Create the load in the database so that we can associate the orders with it
	if err := loaddb.UpsertLoad(ctx, &result, &t.integration); err != nil {
		log.Error(
			ctx,
			"Error upserting load in database",
			zap.String("load_id", result.ExternalTMSID),
			zap.Uint("service_id", t.integration.ServiceID),
			zap.Error(err),
		)
	}

	// Process orders if found
	if len(loadData.Orders.Order) > 0 {
		for _, orderData := range loadData.Orders.Order {
			// Create order model from load response data
			order := models.Order{
				OrderCoreInfo: models.OrderCoreInfo{
					ExternalLoadID:  load.LoadNum,
					ExternalOrderID: orderData.OrdNum,
					OrderTrackingID: orderData.OrdNum,
					Status:          orderData.OrderTMSStatus,
					Mode:            orderData.OrdType,
					FreightTerms:    orderData.FreightTerms,
					IsPrePayment:    orderData.IsPrePayment,
					Currency:        orderData.Currency,
					TotalWeight:     float64(parseFloat(orderData.TotalGrossWeight.WeightValue.Value)),
					TotalVolume:     float64(parseFloat(orderData.TotalGrossVolume.VolumeValue.Value)),
					PieceCount:      orderData.TotalPieceCount,
					IsHazmat:        orderData.IsHazmat,
					RateData: models.RateData{
						CustomerTotalCharge: models.ValueUnit{
							Val:  float32(parseFloat(orderData.TotalNetCharge.CurrencyValue.Value)),
							Unit: orderData.Currency,
						},
					},
					Specifications: models.Specifications{
						TotalWeight: models.ValueUnit{
							Val:  parseFloat(orderData.TotalGrossWeight.WeightValue.Value),
							Unit: orderData.TotalGrossWeight.WeightValue.UOM,
						},
					},
				},
				LoadID:                result.ID,
				ExternalLoadID:        load.LoadNum,
				RequestedPickupDate:   parseTime(orderData.ScheduledEarlyPickup).Time,
				RequestedDeliveryDate: parseTime(orderData.ScheduledLatePickup).Time,
				Reference:             orderData.OrdNum,
				ServiceID:             t.integration.ServiceID,
				Type:                  orderData.OrdType,
			}

			// Add destination contact if available
			if orderData.DestinationContact.ContactName != "" {
				order.Consignee = models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Contact: orderData.DestinationContact.ContactName,
						Phone:   orderData.DestinationContact.Phone1,
						Email:   orderData.DestinationContact.Email,
					},
				}
			}

			// Add comments if available
			if len(orderData.OrderComments.Comment) > 0 {
				notes := make([]models.Note, len(orderData.OrderComments.Comment))
				for i, comment := range orderData.OrderComments.Comment {
					notes[i] = models.Note{
						Note:      comment.CommentValue,
						Source:    comment.Qualifier.QualifierName,
						UpdatedBy: orderData.EntityHeader.LastModifiedBy,
					}
				}
				order.Notes = notes
			}

			// Add reference numbers if available
			if len(orderData.OrderRefNums.RefNum) > 0 {
				refNums := make([]string, len(orderData.OrderRefNums.RefNum))
				for i, refNum := range orderData.OrderRefNums.RefNum {
					refNums[i] = refNum.RefNumValue
				}
				order.Reference = strings.Join(refNums, ", ")
			}

			// Create the order in the database
			if err := orderdb.CreateOrderInDB(ctx, &order); err != nil {
				log.Error(
					ctx,
					"Error creating order in database",
					zap.String("order_id", order.ExternalOrderID),
					zap.Uint("load_id", result.ID),
					zap.Uint("service_id", t.integration.ServiceID),
					zap.Error(err),
				)
				continue
			}
		}
	}

	return result, t.GetDefaultLoadAttributes(), nil
}

// GetLoadsByIDType retrieves loads from 3G TMS by ID type (loadNum, proNum, bolNum)
func (t *ThreeG) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) ([]models.Load, models.LoadAttributes, error) {
	url := fmt.Sprintf("%s/loads/", t.baseURL)

	// Add the appropriate query parameter based on the ID type
	switch strings.ToLower(idType) {
	case "loadnum":
		load, loadAttributes, err := t.GetLoad(ctx, id)
		return []models.Load{load}, loadAttributes, err
	case "pronum", "pronumber":
		url = fmt.Sprintf("%s?proNum=%s", url, id)
	case "bolnum", "bolnumber":
		url = fmt.Sprintf("%s?bolNum=%s", url, id)
	default:
		return nil, models.LoadAttributes{}, fmt.Errorf("unsupported ID type: %s", idType)
	}

	url = t.addAuthParams(url)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, models.LoadAttributes{}, fmt.Errorf("error reading response body: %w", err)
		}
		return nil, models.LoadAttributes{}, fmt.Errorf(
			"error response from 3G TMS: %s - %s",
			resp.Status,
			string(body),
		)
	}

	// For simplicity, we'll assume a single load response
	// In a real implementation, you would handle multiple loads in the response
	var loadResponse LoadResponse
	if err := xml.NewDecoder(resp.Body).Decode(&loadResponse); err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error decoding response: %w", err)
	}

	// Convert the response to a load
	load, _, err := t.GetLoad(ctx, loadResponse.LoadID)
	if err != nil {
		return nil, models.LoadAttributes{}, err
	}

	return []models.Load{load}, t.GetDefaultLoadAttributes(), nil
}

// login performs authentication with 3G TMS and returns the session cookie
func (t *ThreeG) login(ctx context.Context) error {
	endpoint := fmt.Sprintf("%s/web/resources/j_spring_security_check", t.baseURL)

	// Decrypt the password
	decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(t.integration.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	// Create form data for login
	formData := url.Values{}
	formData.Set("j_username", t.integration.Username)
	formData.Set("j_password", decryptedPassword)

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return fmt.Errorf("error creating login request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,"+
		"image/avif,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Origin", t.baseURL)
	req.Header.Set("Referer", fmt.Sprintf("%s/web/login", t.baseURL))

	// Make the request
	resp, err := t.client.Do(req)
	if err != nil {
		return fmt.Errorf("error making login request: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful login
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("login failed with status: %s", resp.Status)
	}

	// Verify we got a session cookie
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		return fmt.Errorf("error parsing URL: %w", err)
	}

	cookies := t.client.Jar.Cookies(parsedURL)
	if len(cookies) == 0 {
		return fmt.Errorf("no session cookie received after login")
	}

	// Check for JSESSIONID cookie
	hasSessionCookie := false
	for _, cookie := range cookies {
		if cookie.Name == "JSESSIONID" {
			hasSessionCookie = true
			break
		}
	}

	if !hasSessionCookie {
		return fmt.Errorf("no JSESSIONID cookie received after login")
	}

	return nil
}

// GetLoadIDs implements the TMS interface
func (t *ThreeG) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) ([]string, error) {
	// Ensure we have a valid session by logging in
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error during login: %w", err)
	}

	endpoint := fmt.Sprintf("%s/web/loadList", t.baseURL)

	// Create the query filter for date range
	dateFilter := map[string]any{
		"fieldName":      "createdDate",
		"fieldType":      "ZonedDateTime",
		"translatedName": "Created Date",
		"fieldNames":     nil,
		"fieldIds": []string{
			"fn1", "comparisonType1", "dateFieldOne1", "dateFieldTwo1",
			"dateFieldThree1", "dateFieldFour1", "dateFieldFive1Label",
			"dateFieldFive1", "dateFieldSix1Label", "dateFieldSix1",
		},
		"userDateFormat": "MM/dd/yyyy HH:mm",
		"values":         []string{"blank", "Relative", "gt", "Minutes", "-300"},
	}

	// If we have a from date, use it instead of the default filter
	if query.FromDate.Valid {
		dateFilter["values"] = []string{
			"blank",
			"Absolute",
			"gt",
			query.FromDate.Time.Format("01/02/2006 15:04"),
			"",
			"",
		}
	}

	// Convert the filter to JSON
	filterJSON, err := json.Marshal([]any{dateFilter})
	if err != nil {
		return nil, fmt.Errorf("error marshaling filter: %w", err)
	}

	// Create form data
	formData := url.Values{}
	formData.Set("filterscount", "0")
	formData.Set("groupscount", "0")
	formData.Set("pagenum", "0")
	formData.Set("pagesize", "500")
	formData.Set("recordstartindex", "0")
	formData.Set("recordendindex", "500")
	formData.Set("query", string(filterJSON))
	formData.Set("quicksearch", "")
	formData.Set("savedQueryId", "-1")

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Origin", t.baseURL)
	req.Header.Set("Referer", fmt.Sprintf("%s/web/clearLoads/", t.baseURL))

	// Make the request
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response from 3G TMS: %s - %s", resp.Status, string(body))
	}

	// Parse the response
	var response struct {
		Records []struct {
			LoadNum string `json:"loadNum"`
		} `json:"Records"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error decoding response: %w", err)
	}

	// Extract load IDs
	loadIDs := make([]string, 0, len(response.Records))
	for _, record := range response.Records {
		if record.LoadNum != "" {
			loadIDs = append(loadIDs, record.LoadNum)
		}
	}

	return loadIDs, nil
}

// CreateLoad implements the TMS interface
func (t *ThreeG) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (models.Load, error) {
	// Implementation would depend on 3G TMS API capabilities
	return models.Load{}, nil
}

// UpdateLoad implements the TMS interface
func (t *ThreeG) UpdateLoad(
	_ context.Context,
	_ *models.Load,
	_ *models.Load,
) (models.Load, models.LoadAttributes, error) {
	// Implementation would depend on 3G TMS API capabilities
	return models.Load{}, models.LoadAttributes{}, nil
}

// Helper function to parse time strings into models.NullTime
func parseTime(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{}
	}

	// Parse the time string based on the format used by 3G TMS
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return models.NullTime{}
	}

	return models.NullTime{
		Valid: true,
		Time:  t,
	}
}

// Helper function to parse float values
func parseFloat(s string) float32 {
	f, err := strconv.ParseFloat(s, 32)
	if err != nil {
		return 0
	}
	return float32(f)
}
