package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

// PostNote implements the TMS interface
func (t *ThreeG) PostNote(_ context.Context, _ *models.Load, _ models.Note) ([]models.Note, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return a not implemented error
	return nil, util.NotImplemented(models.ThreeG, "PostNote")
}
