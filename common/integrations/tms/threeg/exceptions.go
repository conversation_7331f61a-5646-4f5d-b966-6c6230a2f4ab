package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
)

// PostException implements the TMS interface
func (t *ThreeG) PostException(_ context.Context, _ *models.Load, _ models.Exception) error {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return a not implemented error
	return util.NotImplemented(models.ThreeG, "PostException")
}

// GetExceptionHistory implements the TMS interface
func (t *ThreeG) GetExceptionHistory(
	_ context.Context,
	_ uint,
	_ string,
) ([]models.Exception, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.Exception{}, util.NotImplemented(models.ThreeG, "GetExceptionHistory")
}
