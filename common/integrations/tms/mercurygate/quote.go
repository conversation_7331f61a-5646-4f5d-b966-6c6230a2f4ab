package mercurygate

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
)

func (m MercuryGate) CreateQuote(
	ctx context.Context,
	_ models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "CreateQuoteMercuryGate", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	return nil, util.NotImplemented(models.MercuryGate, "CreateQuote")
}
