package mercurygate

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (m MercuryGate) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardMercuryGate", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Tenant == "" || onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing MercuryGate API credentials")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, onboardRequest.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		APIKey:            onboardRequest.APIKey,
		Tenant:            onboardRequest.Tenant,
		Username:          onboardRequest.Username,
		EncryptedPassword: encryptedPassword,
	}, nil
}

func (m *MercuryGate) Auth(ctx context.Context) error {
	log.Info(ctx, "attempting MercuryGate login")

	err := m.login(ctx)
	if err != nil {
		return err
	}

	log.Info(ctx, "successfully authenticated MercuryGate client")
	return nil
}

func (m *MercuryGate) login(ctx context.Context) error {
	formData := url.Values{}

	formData.Set("UserId", m.config.UserName)
	formData.Set("Password", m.config.Password)
	formData.Set("submitbutton", "Sign In")

	_, cookies, err := m.postWithHeaders(
		ctx,
		fmt.Sprintf("%s/login/LoginProcess.jsp", m.getAPIURL()),
		strings.NewReader(formData.Encode()),
		map[string]string{
			"Content-Type": "application/x-www-form-urlencoded",
		},
		true,
		s3backup.TypeTokens,
	)
	if err != nil {
		return fmt.Errorf("failed to login to MercuryGate: %w", err)
	}

	// Get the response cookies from the formAction request
	m.cookies = cookies
	if len(m.cookies) == 0 {
		return fmt.Errorf("failed to get session cookies from MercuryGate login")
	}

	return nil
}
