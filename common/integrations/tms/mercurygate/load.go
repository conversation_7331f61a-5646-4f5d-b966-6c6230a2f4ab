package mercurygate

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (m MercuryGate) GetLoadIDs(context.Context, models.SearchLoadsQuery) (
	[]string,
	error,
) {
	return nil, util.NotImplemented(models.MercuryGate, "GetLoads")
}

func (m MercuryGate) GetLoadsByIDType(
	ctx context.Context,
	id string,
	_ string,
) ([]models.Load, models.LoadAttributes, error) {
	load, attrs, err := m.GetLoad(ctx, id)
	return []models.Load{load}, attrs, err
}

func (m MercuryGate) GetLoad(ctx context.Context, shipmentID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(m.tms), attribute.String("freight_tracking_id", shipmentID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadMercuryGate", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := fmt.Sprintf("%s/rest/api/v1/enterprises/26160712297/loadboard/globalSearch", m.getAPIURL())

	var globalSearchReq GlobalSearchReq
	globalSearchReq.SearchValue = shipmentID
	reqBody, err := json.Marshal(globalSearchReq)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, _, err := m.post(ctx, endPoint, bytes.NewReader(reqBody), s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}

	var shipmentData ShipmentResp
	if err = json.Unmarshal(resp, &shipmentData); err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("failed to unmarshal MercuryGate response: %w", err)
	}

	if len(shipmentData.ExecutionLoads) == 0 {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("no execution loads found for shipment %s", shipmentID)
	}

	loadData := m.MercuryGateShipmentToLoad(shipmentData.ExecutionLoads[0])
	loadData.FreightTrackingID = shipmentID

	return loadData, DefaultLoadAttributes, nil
}

func (m MercuryGate) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (result models.Load, err error) {
	return result, util.NotImplemented(models.MercuryGate, "CreateLoad")
}

func (m MercuryGate) UpdateLoad(
	_ context.Context,
	_ *models.Load,
	_ *models.Load,
) (models.Load, models.LoadAttributes, error) {
	return models.Load{}, m.GetDefaultLoadAttributes(), util.NotImplemented(models.MercuryGate, "UpdateLoad")
}

func (m MercuryGate) MercuryGateShipmentToLoad(executionLoad ExecutionLoad) models.Load {
	var result models.Load

	result.FreightTrackingID = strconv.FormatInt(executionLoad.Oid, 10)
	result.ExternalTMSID = strconv.FormatInt(executionLoad.Oid, 10)
	result.Status = executionLoad.Status
	result.ServiceID = m.tms.ServiceID
	result.TMSID = m.tms.ID

	// Origin
	result.Pickup.City = executionLoad.Origin.City
	result.Pickup.State = executionLoad.Origin.State
	result.Pickup.Country = executionLoad.Origin.Country
	result.Pickup.AddressLine1 = executionLoad.Origin.Address1
	result.Pickup.AddressLine2 = executionLoad.Origin.Address2

	// Destination
	result.Consignee.City = executionLoad.Destination.City
	result.Consignee.State = executionLoad.Destination.State
	result.Consignee.Country = executionLoad.Destination.Country
	result.Consignee.AddressLine1 = executionLoad.Destination.Address1
	result.Consignee.AddressLine2 = executionLoad.Destination.Address2

	// Enterprise/Customer info
	result.Customer.ExternalTMSID = strconv.FormatInt(executionLoad.OwningEnterprise.Oid, 10)
	result.Customer.Name = executionLoad.OwningEnterprise.Name

	return result
}
