package truckmate

import (
	"context"
	"fmt"
	"net/url"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (t Truckmate) getToken(ctx context.Context, username, password string) (string, error) {
	postdata := map[string]string{
		"username": username,
		"password": password,
	}
	queryParams := url.Values{}
	auth := ""
	var tokenResp TokenResp
	err := t.post(ctx, "/login", queryParams, postdata, &tokenResp, &auth, s3backup.TypeTokens)
	return tokenResp.JWT, err
}

func (t Truckmate) authenticate(ctx context.Context, username, password string) (models.OnboardTMSResponse, error) {
	token, err := t.getToken(ctx, username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed %w", err)
	}

	return models.OnboardTMSResponse{
		Username:          username,
		EncryptedPassword: encryptedPassword,
		AccessToken:       token,
	}, err
}

func (t Truckmate) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	if onboardRequest.APIKey == "" || onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, fmt.Errorf("missing TruckMate API credentials")
	}
	return t.authenticate(ctx, onboardRequest.Username, onboardRequest.Password)
}
