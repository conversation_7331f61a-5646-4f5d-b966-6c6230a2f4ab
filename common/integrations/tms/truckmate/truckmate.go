package truckmate

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

// TODO: Store in Truckmate struct as this as package-level var affects other instances
const (
	tmsHost     = ""
	tokenHeader = ""
)

type Truckmate struct {
	tms models.Integration
}

func (t Truckmate) GetLoadsByIDType(
	context.Context,
	string,
	string,
) (loads []models.Load, attr models.LoadAttributes, _ error) {
	return loads, attr, util.NotImplemented(models.TruckMate, "GetLoadsByIDType")
}

func (t Truckmate) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return util.NotImplemented(models.TruckMate, "PostCheckCall")
}

func New(ctx context.Context, tms models.Integration) *Truckmate {
	log.With(ctx, zap.Uint("axleTMSID", tms.ID), zap.String("tmsName", string(models.TruckMate)),
		zap.String("host", tmsHost))
	return &Truckmate{tms}
}

func (t Truckmate) GetDefaultLoadAttributes() (result models.LoadAttributes) {
	// attrs := DefaultLoadAttributes
	// tmsutil.ApplyTMSFeatureFlags(&t.tms, &attrs)

	// return attrs
	panic("not implemented yet, TODO")
}

func (t Truckmate) GetCustomers(context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, util.NotImplemented(models.Aljex, "GetCustomers")
}

func (t Truckmate) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, util.NotImplemented(models.TruckMate, "GetLocations")
}

func (t Truckmate) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.TruckMate, "GetCarriers")
}

func (t Truckmate) CreateQuote(
	context.Context,
	models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, util.NotImplemented(models.TruckMate, "CreateQuote")
}

func (t Truckmate) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, util.NotImplemented(models.TruckMate, "GetUsers")
}

func (t Truckmate) PostException(context.Context, *models.Load, models.Exception) error {
	return util.NotImplemented(models.TruckMate, "PostException")
}

func (t Truckmate) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, util.NotImplemented(models.TruckMate, "GetExceptionHistory")
}

func (t Truckmate) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, util.NotImplemented(models.TruckMate, "PostNote")
}

func (t Truckmate) GetTestLoads() map[string]bool {
	return map[string]bool{}
}

func (t Truckmate) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	authorization *string,
	dataType s3backup.DataType,
) error {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.AccessToken

	body, _, err := httputil.GetBytesWithToken(ctx, t.tms, addr, headerMap, authorization, dataType)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return nil
}

func (t Truckmate) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	dataType s3backup.DataType,
) error {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.AccessToken

	body, _, err := httputil.PostBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return err
}

func (t Truckmate) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	auth *string,
	dataType s3backup.DataType,
) (err error) {
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.AccessToken

	body, _, err := httputil.PutBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, auth, dataType)
	if err != nil {
		return err
	}
	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return
}
