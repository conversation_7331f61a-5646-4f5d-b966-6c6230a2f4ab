package mcleod

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (m *Mcleod) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (calls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(m.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)

	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryMcleod", spanAttrs)
	defer func() { metaSpan.End(err) }()

	load, err := loadDB.GetLoadByID(ctx, loadID)
	if err != nil {
		return calls, fmt.Errorf("get loadDB failed: %w", err)
	}

	var checkCallResp TrackOrderDetails
	endPoint := fmt.Sprintf("/orders/%s", load.ExternalTMSID)
	err = m.get(ctx, endPoint, nil, &checkCallResp, s3backup.TypeCheckCalls)
	if err != nil {
		return calls, fmt.Errorf("get checkcall response failed: %w", err)
	}

	for _, resp := range checkCallResp.ProgressUpdates {
		call := models.CheckCall{}
		call.LoadID = loadID
		call.FreightTrackingID = freightTrackingID
		call.Status = resp.Event
		call.City = resp.Position.City
		call.State = resp.Position.State
		call.Lat = resp.Position.Latitude
		call.Lon = resp.Position.Longitude
		call.DateTime = parseTime(resp.Timestamp)

		calls = append(calls, call)
	}

	return calls, err
}
