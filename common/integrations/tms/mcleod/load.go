package mcleod

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/common/util/timezone"
)

func (m *Mcleod) GetLoadIDs(ctx context.Context, _ models.SearchLoadsQuery) (ids []string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsMcleod", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	var ordersResp []TrackOrderDetails
	err = m.get(ctx, "/orders", nil, &ordersResp, s3backup.TypeLoads)
	if err != nil {
		return ids, fmt.Errorf("get Load IDs response failed: %w", err)
	}
	for _, order := range ordersResp {
		ids = append(ids, order.OrderID)
	}
	return ids, nil
}

func (m *Mcleod) GetLoad(
	ctx context.Context,
	externalTMSID string,
) (result models.Load, defaultAttrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(m.tms), attribute.String("external_tms_id", externalTMSID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadMcleod", spanAttrs)
	defer func() { metaSpan.End(err) }()

	defaultAttrs = m.GetDefaultLoadAttributes()

	var orderResp OrderResp
	//TODO: require customer id here

	// endPoint := fmt.Sprintf("/ordercreation//customers/%s/order-requests/%s", "1CHC", externalTMSID)
	endPoint := fmt.Sprintf("ordercreation-sandbox/customers/%s/order-requests/%s", "1CHC", externalTMSID)
	err = m.get(ctx, endPoint, nil, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("get order resp failed: %w", err)
	}

	result = m.toLoad(ctx, orderResp)
	return result, defaultAttrs, nil
}

func (m *Mcleod) GetLoadsByIDType(
	ctx context.Context,
	id string,
	_ string,
) ([]models.Load, models.LoadAttributes, error) {
	load, attrs, err := m.GetLoad(ctx, id)
	return []models.Load{load}, attrs, err
}

func (m *Mcleod) CreateLoad(
	ctx context.Context,
	reqLoad models.Load,
	_ *models.TMSUser,
) (respLoad models.Load, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadMcleod", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	orderReqBody := m.toMcleodOrder(reqLoad)

	var orderResp OrderResp
	// endPoint := fmt.Sprintf("/ordercreation/customers/%s/order-requests", reqLoad.Customer.ExternalTMSID)
	endPoint := fmt.Sprintf("ordercreation-sandbox/customers/%s/order-requests", reqLoad.Customer.ExternalTMSID)
	err = m.post(ctx, endPoint, nil, &orderReqBody, &orderResp, nil, s3backup.TypeLoads)
	if err != nil {
		return reqLoad, fmt.Errorf("create order failed: %w", err)
	}

	loadResp := m.toLoad(ctx, orderResp)
	return loadResp, nil
}

func (m *Mcleod) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	reqLoad *models.Load,
) (respLoad models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(m.tms), otel.SafeIntAttribute("load_id", reqLoad.ID))
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadMcleod", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = m.GetDefaultLoadAttributes()

	orderReqBody := m.toMcleodOrder(*reqLoad)

	var orderResp OrderResp
	endPoint := fmt.Sprintf("customers/%s/order-requests/%s",
		reqLoad.Customer.ExternalTMSID, reqLoad.ExternalTMSID)
	err = m.put(ctx, endPoint, nil, &orderReqBody, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return respLoad, attrs, fmt.Errorf("update order failed: %w", err)
	}

	respLoad = m.toLoad(ctx, orderResp)
	return respLoad, attrs, nil
}

func (m *Mcleod) toLoad(ctx context.Context, orderResp OrderResp) (load models.Load) {
	load.TMSID = m.tms.ID
	load.TMS = m.tms
	load.Service.ID = m.tms.ServiceID
	load.Service = m.tms.Service

	load.FreightTrackingID = orderResp.RequestID
	load.ExternalTMSID = orderResp.RequestID
	load.Status = orderResp.Status.Action

	loadMode := models.StringToLoadMode(orderResp.OrderType)
	if loadMode == "" {
		log.Warn(
			ctx,
			"Unknown McLeod load mode",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("mode", orderResp.OrderType),
		)
	}
	load.Mode = loadMode

	// customer details
	load.Customer.Name = orderResp.Customer.Name
	load.Customer.Email = orderResp.Customer.Email
	load.Customer.Phone = orderResp.Customer.Phone

	// carrier details
	load.Carrier.SealNumber = orderResp.OrderReferenceNumbers.SealNumber

	// consignee details
	load.Consignee.RefNumber = orderResp.OrderReferenceNumbers.ConsigneeRefno

	// pick up
	for _, data := range orderResp.Stops {
		if data.StopType == "Pickup" {
			load.Pickup.City = data.CityName
			load.Pickup.State = data.State
			load.Pickup.AddressLine1 = data.Address
			load.Pickup.AddressLine2 = data.Address2
			load.Pickup.Zipcode = data.ZipCode
			load.Pickup.ApptStartTime = parseTime(data.ScheduledArrivalEarly)
			load.Pickup.ApptEndTime = parseTime(data.ScheduledArrivalLate)
			tz, err := timezone.GetTimezone(ctx, data.CityName, data.State, "")
			if err != nil {
				log.WarnNoSentry(ctx, "error fetching pickup's timezone", zap.Error(err))
			}
			load.Pickup.Timezone = tz

			// specifications
			load.Specifications.TotalInPalletCount = data.PalletsIn
			load.Specifications.TotalOutPalletCount = data.PalletsOut
		}
		if data.StopType == "Delivery" {
			load.Consignee.City = data.CityName
			load.Consignee.State = data.State
			load.Consignee.AddressLine1 = data.Address
			load.Consignee.AddressLine2 = data.Address2
			load.Consignee.Zipcode = data.ZipCode
			load.Consignee.ApptStartTime = parseTime(data.ScheduledArrivalEarly)
			load.Consignee.ApptEndTime = parseTime(data.ScheduledArrivalLate)
			load.Consignee.MustDeliver = parseTime(data.ScheduledArrivalLate)
			tz, err := timezone.GetTimezone(ctx, data.CityName, data.State, "")
			if err != nil {
				log.WarnNoSentry(ctx, "error fetching Delivery timezone", zap.Error(err))
			}
			load.Consignee.Timezone = tz
			// specifications
			load.Specifications.TotalInPalletCount = data.PalletsIn
			load.Specifications.TotalOutPalletCount = data.PalletsOut
		}
	}
	load.Specifications.MinTempFahrenheit = float32(orderResp.Commodity.TemperatureRange.Low)
	load.Specifications.MaxTempFahrenheit = float32(orderResp.Commodity.TemperatureRange.High)

	load.Specifications.TotalPieces = models.ValueUnit{
		Val:  float32(orderResp.Rating.Pieces),
		Unit: "",
	}
	load.Specifications.Hazmat = orderResp.Commodity.Hazmat
	load.Specifications.Commodities = orderResp.Commodity.Name
	// todo : need to confirm weight unit
	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(orderResp.Rating.Weight),
		Unit: "kg",
	}
	// todo : need to confirm distance unit
	load.Specifications.TotalDistance = models.ValueUnit{
		Val:  orderResp.Rating.BillDistance,
		Unit: "miles",
	}

	// rate details
	if orderResp.Rating.RateType == "Flat" {
		load.RateData.CustomerRateType = "FlatRate"
	}

	return load
}

func (m *Mcleod) toMcleodOrder(reqLoad models.Load) (orderReq OrderResp) {
	orderReq.Status.Action = reqLoad.Status
	orderReq.OrderType = string(reqLoad.Mode)

	orderReq.Customer.Name = reqLoad.Customer.Name
	orderReq.Customer.Email = reqLoad.Customer.Email
	orderReq.Customer.Phone = reqLoad.Customer.Phone

	orderReq.OrderReferenceNumbers.SealNumber = reqLoad.Carrier.SealNumber
	orderReq.OrderReferenceNumbers.ConsigneeRefno = reqLoad.Consignee.RefNumber

	orderReq.Stops = []Stop{
		// for Pickup
		{
			StopType:              "Pickup",
			CityName:              reqLoad.Pickup.City,
			State:                 reqLoad.Pickup.State,
			Address:               reqLoad.Pickup.AddressLine1,
			Address2:              reqLoad.Pickup.AddressLine2,
			ZipCode:               reqLoad.Pickup.Zipcode,
			ScheduledArrivalEarly: reqLoad.Pickup.ApptStartTime.Time.Format("2006-01-02 15:04:05-07:00"),
			ScheduledArrivalLate:  reqLoad.Pickup.ApptEndTime.Time.Format("2006-01-02 15:04:05-07:00"),
			PalletsIn:             reqLoad.Specifications.TotalInPalletCount,
			PalletsOut:            reqLoad.Specifications.TotalOutPalletCount,
		},
		// for Delivery
		{
			StopType:              "Delivery",
			CityName:              reqLoad.Consignee.City,
			State:                 reqLoad.Consignee.State,
			Address:               reqLoad.Consignee.AddressLine1,
			Address2:              reqLoad.Consignee.AddressLine2,
			ZipCode:               reqLoad.Consignee.Zipcode,
			ScheduledArrivalEarly: reqLoad.Consignee.ApptStartTime.Time.Format("2006-01-02 15:04:05-07:00"),
			ScheduledArrivalLate:  reqLoad.Consignee.ApptEndTime.Time.Format("2006-01-02 15:04:05-07:00"),
			PalletsIn:             reqLoad.Specifications.TotalInPalletCount,
			PalletsOut:            reqLoad.Specifications.TotalOutPalletCount,
		},
	}

	orderReq.Rating.CollectionMethod = "Collect"
	orderReq.Rating.Pieces = int(reqLoad.Specifications.TotalPieces.Val)
	orderReq.Commodity.Hazmat = reqLoad.Specifications.Hazmat
	orderReq.Commodity.Name = reqLoad.Specifications.Commodities
	orderReq.Rating.BillDistance = reqLoad.Specifications.TotalDistance.Val
	orderReq.Rating.Weight = int(reqLoad.Specifications.TotalWeight.Val)

	return orderReq
}

// Helper function to parse time strings into NullTime
func parseTime(timeStr string) models.NullTime {
	const timeFormat = "2006-01-02 15:04:05-07:00"

	parsedTime, err := time.Parse(timeFormat, timeStr)
	if err != nil {
		return models.NullTime{}
	}

	return models.NullTime{Time: parsedTime, Valid: true}
}
