package aljex

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestLivePostException(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLivePostException: run with LIVE_TEST=true to enable")
		return
	}

	var load models.Load
	load.FreightTrackingID = "2080005"

	require.Contains(t, axleTestPROs, load.FreightTrackingID)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	ctx := context.Background()

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	exception := models.Exception{
		LoadID:     46,
		EventCode:  "OTHER  -   OT000",
		WhoEntered: "AXLEAPI",
		Carrier:    "Carrier Name Test",
		Driver:     "Driver Name Test",
		Fault:      "",
		Trailer:    "1234",
		Note:       "Additional notes",
		Status:     "",
		DateTime:   "02/29/24  -  00:12:34",
		DateTimeWithoutTimezone: models.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
	}

	err = client.PostException(ctx, &load, exception)
	assert.NoError(t, err)
}

func TestLiveGetExceptionHistory(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetExceptionHistory: run with LIVE_TEST=true to enable")
		return
	}

	pro := "2080005"

	require.Contains(t, axleTestPROs, pro)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	ctx := context.Background()

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	history, err := client.GetExceptionHistory(ctx, 1, pro)
	assert.NoError(t, err)

	// Case-sensitive test
	// NOTE: Ordering of these 3 events may change as this load is tested in develop.
	// This is why we spot-check only the first 3 exceptions. If test is failing, check Aljex to see the updated
	// indices of these particular exceptions.
	expected := []models.Exception{
		{
			LoadID:     1,
			EventCode:  "CARRINV  -   VR000",
			WhoEntered: "AXLEAPI",
			Carrier:    "TEST",
			Driver:     "HALEY",
			Fault:      "",
			Trailer:    "AAAA-1234567",
			Note:       "CARRINV test",
			Status:     "",
			DateTime:   "03/07/24  -  16:18:47",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2024, 3, 7, 16, 18, 47, 0, time.UTC),
				Valid: true,
			},
		},
		{
			LoadID:     1,
			EventCode:  "TNU  -   TN000",
			WhoEntered: "AXLEAPI",
			Carrier:    "TEST",
			Driver:     "HALEY",
			Fault:      "",
			Trailer:    "AAAA-1234567",
			Note:       "TNU test",
			Status:     "",
			DateTime:   "03/07/24  -  16:14:21",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2024, 3, 7, 16, 14, 21, 0, time.UTC),
				Valid: true,
			},
		},
		{
			LoadID:     1,
			EventCode:  "PUAPPT  -   PU003",
			WhoEntered: "AXLEAPI",
			Carrier:    "TEST",
			Driver:     "HALEY",
			Fault:      "",
			Trailer:    "AAAA-1234567",
			Note:       "PUNUMB email notif test",
			Status:     "",
			DateTime:   "03/07/24  -  16:07:43",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2024, 3, 7, 16, 07, 43, 0, time.UTC),
				Valid: true,
			},
		},
	}

	require.Greater(t, len(history), 3)
	for i, e := range expected {
		t.Log("index: ", i)
		assert.Equal(t, e, history[i])
	}
}

func TestParseEventCode(t *testing.T) {
	tests := []struct {
		input        string
		expectedName string
		expectedAbbr string
	}{
		{"OTHER -  OT000", "OTHER", "OT"},
		{"NOTREADY  -   NR000", "NOTREADY", "NR"},
		{"INCIDENT  -   IN000", "INCIDENT", "IN"},
		{"Invalid Event Code", "", ""},
	}

	for _, test := range tests {
		name, abbr := parseEventCode(test.input)
		if name != test.expectedName {
			t.Errorf("For input %q, expected name %q, but got %q", test.input, test.expectedName, name)
		}
		if abbr != test.expectedAbbr {
			t.Errorf("For input %q, expected abbreviation %q, but got %q", test.input, test.expectedAbbr, abbr)
		}
	}
}
