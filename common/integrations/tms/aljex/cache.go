package aljex

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type SerializableCookie struct {
	Name    string
	Value   string
	Path    string
	Domain  string
	Expires time.Time
}

type SerializableAljex struct {
	Config  *Config
	Creds   *Credentials
	Cookies []SerializableCookie
}

type RateLimiter struct {
	RequestNumber int
	UpdatedAt     time.Time
}

func (a *Aljex) MarshalAljex() (string, error) {
	serializableCookies := make([]SerializableCookie, len(a.cookies))

	for i, cookie := range a.cookies {
		serializableCookies[i] = SerializableCookie{
			Name:    cookie.Name,
			Value:   cookie.Value,
			Path:    cookie.Path,
			Domain:  cookie.Domain,
			Expires: cookie.Expires,
		}
	}

	sa := SerializableAljex{
		Config:  a.config,
		Creds:   a.creds,
		Cookies: serializableCookies,
	}

	bytes, err := json.Marshal(sa)
	return string(bytes), err
}

func UnmarshalAljex(ctx context.Context, data string) (*Aljex, error) {
	var sa SerializableAljex
	err := json.Unmarshal([]byte(data), &sa)
	if err != nil {
		return nil, err
	}

	cookies := make([]*http.Cookie, len(sa.Cookies))
	for i, sc := range sa.Cookies {
		cookies[i] = &http.Cookie{
			Name:    sc.Name,
			Value:   sc.Value,
			Path:    sc.Path,
			Domain:  sc.Domain,
			Expires: sc.Expires,
		}
	}

	client := otel.TracingHTTPClient()
	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		log.Debug(ctx, "could not create Aljex cookie jar", zap.Error(err))
		client = otel.TracingHTTPClient()
	} else {
		client.Jar = cookieJar
	}

	a := &Aljex{
		httpClient: client,
		cookies:    cookies,
		config:     sa.Config,
		creds:      sa.Creds,
	}

	return a, nil
}

func redisClientKey(serviceID uint, tmsID uint) string {
	return fmt.Sprintf("service-%d-tms-%d-aljex", serviceID, tmsID)
}

func retrieveRedisClient(ctx context.Context, serviceID uint, tmsID uint) (*Aljex, error) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return nil, nil
	}

	str, err := redis.RDB.Get(ctx, redisClientKey(serviceID, tmsID)).Result()
	if err != nil {
		log.Warn(ctx, "failed to get Aljex session from Redis", zap.Error(err))
	}

	if str != "" {
		client, err := UnmarshalAljex(ctx, str)

		if err != nil {
			log.Warn(ctx, "failed to unmarshal Aljex session from Redis", zap.Error(err))
			return nil, err
		}

		if client != nil {
			log.Info(ctx, "re-using existing Aljex client")
		}

		return client, nil
	}

	return nil, nil
}

func (a *Aljex) dumpRedisClient(ctx context.Context) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return
	}

	serializedAljexSession, err := a.MarshalAljex()
	if err != nil {
		log.Warn(ctx, "failed to json marshal Aljex session for Redis", zap.Error(err))
	}

	redisKey := redisClientKey(a.tms.ServiceID, a.tms.ID)
	err = redis.RDB.Set(ctx, redisKey, serializedAljexSession, 3*time.Hour).Err()
	if err != nil {
		log.Warn(ctx, "failed to set Aljex session in Redis", zap.Error(err))
	}
}

func (a *Aljex) sessionRefreshCheck(ctx context.Context, htmlBody string) {
	if strings.Contains(strings.ToLower(htmlBody), "your session has timed out due to inactivity") ||
		strings.Contains(strings.ToLower(htmlBody), "no session credentials found") {

		log.Info(ctx, "refreshing aljex session")

		err := a.Auth(ctx)
		if err != nil {
			log.Error(ctx, "aljex session refresh failed", zap.Error(err))
			return
		}

		if redis.RDB != nil {
			serializedAljexSession, err := a.MarshalAljex()
			if err != nil {
				log.Warn(ctx, "failed to json marshal Aljex session for Redis", zap.Error(err))
			}

			redisKey := redisClientKey(a.tms.ServiceID, a.tms.ID)
			err = redis.RDB.Set(ctx, redisKey, serializedAljexSession, 3*time.Hour).Err()
			if err != nil {
				log.Warn(ctx, "failed to set Aljex session in Redis", zap.Error(err))
			}
		}
	}
}

func (a *Aljex) rateLimitCheck(ctx context.Context) error {
	// TODO: Find a way to emulate redis in CI tests
	disableRateLimit := os.Getenv("DISABLE_RATE_LIMIT")

	if disableRateLimit == "true" {
		return nil
	}

	if redis.RDB == nil {
		return errors.New("failed to connect to Redis")
	}

	var limiter *RateLimiter
	var serializedLimiter string

	key := fmt.Sprintf("%s-aljex-rate-limiter", a.config.Tenant)
	str, err := redis.RDB.Get(ctx, key).Result()
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get Aljex rate limiter from Redis",
			zap.String("tenant", a.config.Tenant), zap.Error(err))
	}

	if str != "" {
		limiter = &RateLimiter{}
		if err = json.Unmarshal([]byte(str), limiter); err != nil {
			log.Warn(ctx, "failed to unmarshal Aljex rate limiter from Redis",
				zap.String("tenant", a.config.Tenant), zap.Error(err))

			return err
		}

		if limiter.RequestNumber >= 600 && time.Since(limiter.UpdatedAt).Seconds() <= 60 {
			return errors.New("Aljex rate limit exceeded")
		}
	} else {
		limiter = &RateLimiter{
			RequestNumber: 0,
			UpdatedAt:     time.Now(),
		}
	}

	limiter.RequestNumber++
	limiter.UpdatedAt = time.Now()

	bytes, err := json.Marshal(limiter)
	if err != nil {
		log.Warn(ctx, "failed to marshal Aljex rate limiter",
			zap.String("tenant", a.config.Tenant), zap.Error(err))

		return err
	}

	serializedLimiter = string(bytes)
	err = redis.RDB.Set(ctx, key, serializedLimiter, 1*time.Minute).Err()
	if err != nil {
		log.Warn(ctx, "failed to set Aljex rate limiter in Redis",
			zap.String("tenant", a.config.Tenant), zap.Error(err))

		return err
	}

	return nil
}
