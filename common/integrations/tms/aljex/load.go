package aljex

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

type (
	// Map representation of LoadData{} for JSON marshaling/unmarshaling
	rawFreightData struct {
		Status         FormAttributes `json:"status"`
		OutPalletCount FormAttributes `json:"outPalletCount"`
		Operator       FormAttributes `json:"operator"`

		// All PO numbers will be flattened into a single CSV string
		PONums FormAttributes `json:"poNums"`

		Carrier   map[string]FormAttributes `json:"carrier"`
		Customer  map[string]FormAttributes `json:"customer"`
		BillTo    map[string]FormAttributes `json:"billTo"`
		PickUp    map[string]FormAttributes `json:"pickUp"`
		Consignee map[string]FormAttributes `json:"consignee"`
		RateData  map[string]FormAttributes `json:"rateData"`
	}

	FormAttributes = StringValue
)

func (a *Aljex) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (res []string, err error) {

	// Aljex table limits to 2000 results so we search for Open or Assigned loads to get all recently created loads
	params := url.Values{}
	params.Set("ctlrec", "") // QN can this be empty
	params.Set("qual", a.creds.Qual)
	params.Set("sys", "3a")
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("type", "login")
	params.Set("pagename", "Default")
	params.Set("prcnam", "t3alogin22")
	params.Set("filtershow", "1")
	params.Set("showmap", "1")
	params.Set("sptstate", "ALL")
	params.Set("spttstate", "ALL")
	params.Set("pagen", "Default")
	params.Set("seln", "00000000")
	// `selXX` are hidden inputs for checkbox's value; 1 indicates box is *not* checked (counterintuitive, I know)
	params.Set("box40", "on")  // Status open = true
	params.Set("sel40", "0")   // Status open = true
	params.Set("sel42", "0")   // Status assigned = true
	params.Set("sel41", "1")   // Status dispatched = false
	params.Set("sel44", "0")   // Status covered = false
	params.Set("sel80", "1")   // Status At PU = false
	params.Set("sel43", "1")   // Status Loaded = false
	params.Set("box119", "on") // ¯\_(ツ)_/¯ , commented out
	params.Set("sel119", "0")
	params.Set("sel38", "1") // Air freight = false
	params.Set("sel39", "1") // Intermodal = false
	// sel35 = Brokerage mode = true
	// sel37 = Logistics mode = true
	// sel36 = Trucking mode = true
	params.Set("sel83", "1")  // Spot loads = false
	params.Set("sel90", "1")  // Shipment ticket = false
	params.Set("sel91", "1")  // Exception Missed Pickup = false
	params.Set("sel102", "1") // Waiting for Signed Confirmation = false
	params.Set("fradius", "0")
	params.Set("tradius", "0")
	params.Set("sel71", "1") // ¯\_(ツ)_/¯ , commented out
	params.Set("sel72", "1") // ¯\_(ツ)_/¯ , commented out
	params.Set("sel70", "1") // ¯\_(ツ)_/¯ , commented out
	params.Set("sel74", "1") // ¯\_(ツ)_/¯ , commented out

	respBody, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(params.Encode()), true, s3backup.TypeLoads)
	if err != nil {
		return nil, err
	}

	document, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return nil, fmt.Errorf("goquery error: %w", err)
	}

	table := document.Find("#datagridtable")
	if table.Length() == 0 {
		return nil, fmt.Errorf("no datagridtable found in response")
	}

	trs := table.Find("tr .lo")
	if trs.Length() == 0 {
		return nil, fmt.Errorf("no trs found in datagridtable")
	}

	trs.Each(func(_ int, tr *goquery.Selection) {
		pro := strings.TrimSpace(tr.Find("td").Eq(1).Find("a").Text())
		pro = extractPRO(pro)

		if query.FromFreightTrackingID != "" && pro >= query.FromFreightTrackingID {
			res = append(res, pro)
		}
	})

	return res, nil
}

func (a *Aljex) GetLoad(ctx context.Context, freightTrackingID string) (res models.Load,
	attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("freight_tracking_id", freightTrackingID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetLoad")

	doc, err := a.getLoad(ctx, freightTrackingID)
	if err != nil {
		return res, attrs, fmt.Errorf("error loading PRO page in GetLoad: %w", err)
	}

	aljexLoad := a.parseLoadHTML(ctx, doc, freightTrackingID)
	load := aljexLoad.ToLoadModel(ctx, freightTrackingID, a.tms.ServiceID, a.tms.ID)
	attrs = aljexLoad.ToLoadAttributes()

	return *load, attrs, err
}

func (a *Aljex) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&a.tms, &attrs)

	return attrs
}

func (a *Aljex) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (models.Load, error) {
	return models.Load{}, errtypes.NotImplemented(a.tms.Name, "CreateLoad")
}

func (a *Aljex) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) (res models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), otel.LoadAttrs(*updatedLoad)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.UpdateLoad")

	// Truncate all time values to the minute since that's how many significant digits Aljex returns
	carrier := &updatedLoad.Carrier
	carrier.DispatchedTime.Time = carrier.DispatchedTime.Time.Truncate(time.Minute)
	carrier.ConfirmationSentTime.Time = carrier.ConfirmationSentTime.Time.Truncate(time.Minute)
	carrier.ConfirmationReceivedTime.Time = carrier.ConfirmationReceivedTime.Time.Truncate(time.Minute)
	carrier.ConfirmationReceivedTime.Time = carrier.ConfirmationReceivedTime.Time.Truncate(time.Minute)
	carrier.ExpectedPickupTime.Time = carrier.ExpectedPickupTime.Time.Truncate(time.Minute)
	carrier.PickupStart.Time = carrier.PickupStart.Time.Truncate(time.Minute)
	carrier.PickupEnd.Time = carrier.PickupEnd.Time.Truncate(time.Minute)
	carrier.ExpectedDeliveryTime.Time = carrier.ExpectedDeliveryTime.Time.Truncate(time.Minute)
	carrier.DeliveryStart.Time = carrier.DeliveryStart.Time.Truncate(time.Minute)
	carrier.DeliveryEnd.Time = carrier.DeliveryEnd.Time.Truncate(time.Minute)

	doc, err := a.getLoad(ctx, curLoad.FreightTrackingID)
	if err != nil {
		return res, attrs, fmt.Errorf("error loading PRO page in GetLoad: %w", err)
	}

	formMetadata := a.parseLoadHTML(ctx, doc, curLoad.FreightTrackingID)
	metadataMap, err := toStructMap(formMetadata)
	if err != nil {
		return res, attrs, fmt.Errorf("error converting metadata to rawFreightData: %w", err)
	}

	formData := url.Values{}

	// There are many more fields than those in models.Load{} (600+), so first add those to POST req
	allInputs := doc.Find("form[name='main'] input, textarea")

	// Settle in for a story, kids: Carrier LH Rate USD is referenced by both "fld103" and "payrate" form names.
	// But for some reason, "fld103" is always empty in the form, even though 1) it isn't in the browser source code
	// and 2) payrate isn't empty. My hypothesis is that there's a JS func that runs after the page loads
	// to set fld103 := payrate. This behavior causes the aljex.UpdateLoad() to incorrectly overwrite that field to 0.
	//
	// TL;DR fld103 is the form name actually required for the update, so to fix the issue,
	// we manually set it to the value of "payrate".
	var carrierLHRateUSD string
	allInputs.Each(func(_ int, input *goquery.Selection) {
		inputName, nameExists := input.Attr("name")
		value := input.AttrOr("value", input.Text())

		if inputName == "payrate" {
			log.Info(ctx, "found payrate field, setting fld103", zap.String("val", value))
			carrierLHRateUSD = value
		}

		// This will also preserve read-only fields, which we skip in addDataToForm
		if nameExists {
			formData.Add(inputName, value)
		}
	})
	formData.Set("fld103", carrierLHRateUSD)
	formData.Set("fld102", formMetadata.RateData.CarrierRateType.Value)

	// Main form includes repeated values so use Set not Add to uniquely define required keys
	formData.Set("pro", curLoad.FreightTrackingID)
	formData.Set("prcnam", "t3atagexa")
	formData.Set("type", "save")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("company", a.creds.Company) // NOTE: this is actually empty, but doesn't impact result
	formData.Set("c_tok", a.creds.Token)
	formData.Set("sys", "3a")
	formData.Set("ctlrec", "")

	updatedAljex := ToAljexData(*updatedLoad)

	updatesMap, err := toStructMap(updatedAljex)
	if err != nil {
		return res, attrs, fmt.Errorf("error converting updated load to rawFreightData")
	}

	// Status is automatically updated, and editing PONums & rate data is not supported right now
	if !formMetadata.Operator.IsReadOnly {
		formData.Set("fld62", updatedLoad.Operator)
	}
	addDataToForm(ctx, &formData, updatesMap.Customer, metadataMap.Customer)
	addDataToForm(ctx, &formData, updatesMap.BillTo, metadataMap.BillTo)
	addDataToForm(ctx, &formData, updatesMap.PickUp, metadataMap.PickUp)
	addDataToForm(ctx, &formData, updatesMap.Consignee, metadataMap.Consignee)
	addDataToForm(ctx, &formData, updatesMap.Carrier, metadataMap.Carrier)

	// If load update is successful, it returns a simple, small HTML page.
	// If not, it returns the same page but will a bunch of autosuggestions JS code
	// See example in ./livetest/update_pro_success_resp.html
	checkRespFunc := func(respBody string) int {
		if !strings.Contains(respBody, "<form method=post name=web action=/route.php>") {
			return http.StatusBadRequest
		}
		return http.StatusOK
	}

	_, _, err = a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()),
		true, s3backup.TypeLoads, checkRespFunc)
	if err != nil {
		return res, attrs, fmt.Errorf("failed to update Aljex load: %w", err)
	}

	// Re-parse load
	return a.GetLoad(ctx, curLoad.FreightTrackingID)
}

func (a *Aljex) GetLoadsByIDType(
	ctx context.Context,
	id string, idType string,
) (_ []models.Load, _ models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("id", id), attribute.String("idType", id))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDType", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case PROIDType:
		load, attrs, err := a.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	case CustomerRefIDType:
		load, attrs, err := a.getLoadByCustomerRef(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return nil, a.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

func (a *Aljex) getLoadByCustomerRef(
	ctx context.Context,
	customerRef string,
) (res models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("customer_ref", customerRef))

	ctx, metaSpan := otel.StartSpan(ctx, "getLoadByCustomerRef", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = a.GetDefaultLoadAttributes()

	// Search dates; limit to +/1 month as this search takes several seconds
	fdate := time.Now().AddDate(0, 0, -45)
	tdate := time.Now().AddDate(0, 0, 45)

	formData := url.Values{}
	formData.Add("prcnam", "vtag")
	formData.Add("QUAL", a.creds.Qual)
	formData.Add("name", a.creds.Name)
	formData.Add("c_tok", a.creds.Token)
	formData.Add("ctlrec", "")
	formData.Add("ctlval", "")
	formData.Add("sys", "3a")
	formData.Add("type", "lookup")
	formData.Add("status", "All")
	formData.Add("limit", "100")
	formData.Add("fdate", fdate.Format("1/2/06")) // Start (PU) Ready date
	formData.Add("tdate", tdate.Format("1/2/06")) // End (PU) Ready date
	formData.Add("reference", customerRef)
	formData.Add("screen", "on") // Display results on screen
	formData.Add("fregion", "?")
	formData.Add("tregion", "?")
	// formData.Add("equtype", "!") // ¯\_(ツ)_/¯

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()),
		true, s3backup.TypeLoads)
	if err != nil {
		return res, attrs, fmt.Errorf("error searching for customer ref: %w", err)
	}

	proNums, err := parseSearchResults(ctx, resp)
	if err != nil {
		return res, attrs, err
	}
	count := len(proNums)

	switch count {
	case 0:
		err = errtypes.HTTPResponseError{
			IntegrationName: a.tms.Name,
			AxleTSPID:       a.tms.ID,
			ServiceID:       a.tms.ServiceID,
			HTTPMethod:      http.MethodGet,
			URL:             fmt.Sprintf("%s=%s", customerRef, CustomerRefIDType),
			StatusCode:      http.StatusNotFound,
			ResponseBody:    []byte("404 Not Found"),
		}

		return res, attrs, err

	case 1:
		return a.GetLoad(ctx, proNums[0])

	default:
		err = errtypes.HTTPResponseError{
			IntegrationName: a.tms.Name,
			AxleTSPID:       a.tms.ID,
			ServiceID:       a.tms.ServiceID,
			HTTPMethod:      http.MethodGet,
			URL:             fmt.Sprintf("%s=%s", customerRef, CustomerRefIDType),
			StatusCode:      http.StatusConflict,
			ResponseBody: []byte(fmt.Sprintf("non-deterministic customerRef, %d results returned (%v)",
				count, proNums)),
		}

		return res, attrs, err
	}

}
