package aljex

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/url"
	"slices"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (a *Aljex) PostCheckCall(ctx context.Context, load *models.Load, c models.CheckCall) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), otel.LoadAttrs(*load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.PostCheckCall")

	// Front-end is read-only, but POST accepts bad input
	c.Author = a.tms.Username

	ccEDIAbrv := EDILongFormToAbbrv(c.Reason)
	if ccEDIAbrv == "" {
		return errors.New("unexpected EDI Reason: " + c.Reason)
	}

	newYorkLoc, err := time.LoadLocation("America/New_York")
	if err != nil {
		return fmt.Errorf("error loading NY location: %w", err)
	}

	params := url.Values{}

	// Note that Aljex allows duplicates
	params.Set("prcnam", "dispatch")
	params.Set("type", "postcc")
	params.Set("qual", a.creds.Qual)
	params.Set("pro", c.FreightTrackingID)
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("ccby", c.Author)
	params.Set("sys", "3a")
	params.Set("ctlrec", "")
	params.Set("ctlval", "")
	params.Set("isfbox", "1")
	// Although Aljex has front-end validation for Ct & St, it will accept bad input.
	params.Set("cccity", c.City)
	params.Set("ccstate", c.State)
	// POST succeeds on bad inputs and accepts value passed in (empty, enum, non-enum)
	params.Set("ccmsg", c.Status)
	// POST succeeds on bad input. If empty, field defaults to Normal Status. If non-empty, field is left empty.
	params.Set("ccedi", ccEDIAbrv)
	// If left empty or misformatted, defaults to now() in the check call location's timezone.
	params.Set("ccdate", time.Now().In(newYorkLoc).Format("1/2/06"))
	// If time == "", Aljex defaults date AND time to now, even if ccdate is another day.
	params.Set("cctime", time.Now().In(newYorkLoc).Format("15:04"))
	params.Set("cctxt", c.Notes)

	_, _, err = a.postForm(ctx, a.getAPIURL(), strings.NewReader(params.Encode()), true, s3backup.TypeCheckCalls)

	return err

}

func (a *Aljex) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint, freightTrackingID string,
) (history []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(a.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetCheckCallsHistory")

	params := url.Values{}
	params.Set("prcnam", "dispatch")
	params.Set("type", "justcc")
	params.Set("qual", a.creds.Qual)
	params.Set("pro", freightTrackingID)
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("ccby", "axleapi")
	params.Set("sys", "3a")

	resp, _, err := a.get(ctx, a.getAPIURL()+"?"+params.Encode(), s3backup.TypeCheckCalls)
	if err != nil {
		return history, fmt.Errorf("failed to get Aljex check call history: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return history, err
	}

	rows := doc.Find("body > div > table > tbody > tr > td > table > tbody > tr > td > table > tbody > " +
		"tr:nth-child(2) > td > table > tbody > tr:nth-child(8) > td > table > tbody tr")

	rows.Each(func(index int, row *goquery.Selection) {
		// Skip the header row
		if index == 0 {
			return
		}
		cells := row.Find("td")

		// Skip the row for adding a check call
		if cells.Eq(3).Find("option").Length() > 0 {
			return
		}

		date := strings.TrimSpace(cells.Eq(5).Text())
		clock := strings.TrimSpace(cells.Eq(6).Text())

		// When there are no check calls, there's an empty row between header and "add check call" row
		if date == "" {
			return
		}

		// NOTE: NFI interprets timestamps in the timezone of the check call's location
		// (i.e. 2 pm at Denver, CO means 2 pm MT, but 2 pm in Boston, MA means 2 pm ET).
		// But Aljex strings exclude TZ info so default everything to UTC to preserve raw values.
		dateTime, err := time.ParseInLocation("1/2/06 15:04:05", date+" "+clock, time.UTC)
		if err != nil {
			log.Warn(ctx, "error parsing check call datetime", zap.Error(err), zap.Int("row", index+1))

			return
		}

		call := models.CheckCall{
			LoadID:                  loadID,
			FreightTrackingID:       freightTrackingID,
			City:                    strings.TrimSpace(cells.Eq(0).Text()),
			State:                   strings.TrimSpace(cells.Eq(1).Text()),
			Status:                  strings.TrimSpace(cells.Eq(3).Text()),
			Reason:                  strings.TrimSpace(strings.Split(cells.Eq(4).Text(), "-")[0]),
			DateTimeWithoutTimezone: models.NullTime{Time: dateTime, Valid: true},
			Author:                  strings.TrimSpace(cells.Eq(7).Text()),
			Notes:                   strings.TrimSpace(cells.Eq(8).Text()),
		}

		history = append(history, call)
	})

	return history, nil

}

func EDILongFormToAbbrv(s string) string {
	switch strings.ToLower(s) {
	case "missed delivery":
		return "A1"
	case "incorrect address":
		return "A2"
	case "unable to locate":
		return "A5"
	case "customer requested future del":
		return "AD"
	case "accident":
		return "AF"
	case "consignee - related":
		return "AG"
	case "driver related":
		return "AH"
	case "mechincal breakdown":
		return "AI"
	case "overnite related":
		return "AJ"
	case "previous stop":
		return "AL"
	case "shipper related":
		return "AM"
	case "weather related":
		return "AO"
	case "recipient unavailable - delay":
		return "AQ"
	case "hold due to customs":
		return "AS"
	case "missed pickup":
		return "AY"
	case "alternate carrier delivered":
		return "AZ"
	case "held per customer":
		return "B5"
	case "carrier keying error":
		return "BF"
	case "industry disruption":
		return "BG"
	case "customer requires earlier del.":
		return "BJ"
	case "refused by recipient":
		return "BS"
	case "no req arrival date by shipper":
		return "CB"
	case "failed to call for appt.":
		return "HB"
	case "normal appt.":
		return "NA"
	case "normal status":
		return "NS"
	}

	return ""
}

// Case-insensitive
func IsValidEDIReasonEnum(s string) bool {
	s = strings.ToLower(s)
	return slices.Contains(EDIReasonEnums, strings.ToLower(s))
}

// Case-insensitive
func IsValidMessageEnum(s string) bool {
	return slices.Contains(MessageEnums, strings.ToLower(s))
}

// Static list of Aljex's EDI reasons
var EDIReasonEnums = []string{
	"missed delivery",
	"incorrect address",
	"unable to locate",
	"customer requested future del",
	"accident",
	"consignee - related",
	"driver related",
	"mechincal breakdown",
	"overnite related",
	"previous stop",
	"shipper related",
	"weather related",
	"recipient unavailable - delay",
	"hold due to customs",
	"missed pickup",
	"alternate carrier delivered",
	"held per customer",
	"carrier keying error",
	"industry disruption",
	"customer requires earlier del.",
	"refused by recipient",
	"no req arrival date by shipper",
	"failed to call for appt.",
	"normal appt.",
	"normal status",
}

var MessageEnums = []string{
	"on time",
	"arrive terminal",
	"at broker",
	"appt needed/change",
	"at shipper",
	"at consignee",
	"bad info",
	"cons closed",
	"custom released",
	"customs delay",
	"delay",
	"depart terminal",
	"detention",
	"dot shutdown",
	"empty return",
	"holiday",
	"in-gate",
	"in transit",
	"labor",
	"left message",
	"loaded late",
	"margin alert",
	"mech breakdown",
	"notify",
	"other",
	"os&d",
	"out 4 delivery",
	"out gate",
	"refused",
	"routing",
	"running late",
	"storage",
	"traffic",
	"undeliverable",
	"unidentified freight",
	"weather delays",
}
