package aljex

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/pquerna/otp/totp"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

const (
	loginURL        = "https://auth.gln.com/IdentityService/login/login"
	mfaURL          = "https://auth.gln.com/IdentityService/TwoFactorOptions"
	mfaPostURL      = "https://auth.gln.com/IdentityService/TwoFactorOptions/TwoFactorOptions"
	defaultDomainDB = "aljex.com"
	host            = "auth.gln.com"
	referer         = "https://auth.gln.com/IdentityService"
)

type LoginResponse struct {
	Success   bool   `json:"Success"`
	ReturnURL string `json:"ReturnUrl"`
}

func (a *Aljex) Auth(ctx context.Context) error {
	log.Info(ctx, "attempting Aljex login")

	returnURL, err := a.login(ctx)
	if err != nil {
		return err
	}

	ssoFormData, err := a.mfa(ctx, returnURL)
	if err != nil {
		return err
	}

	err = a.sso(ctx, ssoFormData)
	if err != nil {
		return err
	}

	log.Info(ctx, "successfully authenticated Aljex client")
	return nil
}

func (a *Aljex) login(ctx context.Context) (string, error) {
	formData := url.Values{}

	formData.Set("UserName", a.config.UserName)
	formData.Set("Password", a.config.Password)
	formData.Set("defaultDomainDB", defaultDomainDB)
	formData.Set("returnUrl", a.config.ReturnURL)
	formData.Set("appSymbol", a.config.AppSymbol)
	formData.Set("appId", a.config.AppID)
	formData.Set("testIndicator", a.config.TestIndicator)
	formData.Set("tenant", a.config.Tenant)
	formData.Set("options", a.config.Options)

	body, _, err := a.postForm(ctx, loginURL, strings.NewReader(formData.Encode()), false, s3backup.TypeTokens)
	if err != nil {
		return "", fmt.Errorf("failed to complete Aljex login flow: %w", err)
	}

	var aljexResponse LoginResponse

	err = json.Unmarshal(body, &aljexResponse)
	if err != nil {
		return "", fmt.Errorf("failed to get return url from Aljex")
	}

	return aljexResponse.ReturnURL, nil
}

// NOTE: The assumption is the Aljex account already has TOTP 2FA installed as we do for our NFI Aljex account.
// It does *not* handle adding an authenticator to the account when presented with 2fa setup page.
func (a *Aljex) mfa(ctx context.Context, returnURL string) (string, error) {
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
		"Host":         host,
		"Referer":      referer,
	}

	initialURL := "https://auth.gln.com" + returnURL

	resp, _, err := a.getWithHeaders(ctx, initialURL, headers, true, s3backup.TypeTokens)
	if err != nil {
		return "", fmt.Errorf("failed to complete Aljex MFA flow: %w", err)
	}

	document, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return "", fmt.Errorf("could not parse HTML from Aljex MFA flow - GET response: %w", err)
	}

	if document.Find("#otpForm").Length() == 0 {
		log.Info(ctx, "No OTP form found. Assuming 2FA is not required.")
		// Try to extract SSO form data from the document
		return extractSSOFormData(document)
	}

	// If we're on the 2FA page, submit code
	log.Info(ctx, "Detected 2FA page. Submitting code...")

	twoFactorCode, err := totp.GenerateCode(a.tms.TwoFactorSecret, time.Now())
	if err != nil {
		return "", fmt.Errorf("failed to generate 2FA code: %w", err)
	}

	mfaFormData := url.Values{}
	mfaFormData.Set("returnUrl", fmt.Sprintf("https://%s.aljex.com/sso/visionlogin.php", a.tms.Tenant))
	mfaFormData.Set("option", "A")
	mfaFormData.Set("truncatedMobile", "#")
	mfaFormData.Set("truncatedEmail", "#")
	mfaFormData.Set("hasMultipleOption", "N")
	mfaFormData.Set("hasAuthenticator", "Y")
	mfaFormData.Set("hasSMS", "N")
	mfaFormData.Set("hasEmail", "N")
	mfaFormData.Set("Otp", twoFactorCode)
	mfaFormData.Set("managetwofaChk", "false")

	encodedForm := mfaFormData.Encode()

	// Find the form action URL
	formAction, _ := document.Find("#otpForm").Attr("action")
	if formAction == "" {
		formAction = "/IdentityService/TwoFactorAuth/Otp"
	}

	// Make sure the formAction is a full URL
	if !strings.HasPrefix(formAction, "http") {
		formAction = "https://" + host + formAction
	}

	resp, cookies, err := a.postWithHeaders(ctx, formAction, strings.NewReader(encodedForm),
		headers, true, s3backup.TypeTokens)
	if err != nil {
		return "", fmt.Errorf("failed to submit 2FA code: %w", err)
	}

	// Get the response cookies from the formAction request
	a.cookies = cookies
	if len(a.cookies) == 0 {
		return "", fmt.Errorf("failed to get session cookies from Aljex MFA flow")
	}

	document, err = goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return "", fmt.Errorf("could not parse HTML after submitting 2FA code: %w", err)
	}

	// Try to extract SSO form data from the document
	return extractSSOFormData(document)
}

func extractSSOFormData(document *goquery.Document) (string, error) {
	ssoFormData := url.Values{}

	document.Find("form[name='web'] input").Each(func(_ int, element *goquery.Selection) {
		name, _ := element.Attr("name")
		value, _ := element.Attr("value")

		ssoFormData.Add(name, value)
	})

	if ssoFormData.Get("type") == "" || ssoFormData.Get("sys") == "" || ssoFormData.Get("prcnam") == "" ||
		ssoFormData.Get("qual") == "" || ssoFormData.Get("name") == "" || ssoFormData.Get("c_tok") == "" {
		return "", fmt.Errorf("failed to extract all required SSO form data: %v", ssoFormData)
	}

	orderedSsoFormData := fmt.Sprintf(
		"type=%s&sys=%s&prcnam=%s&qual=%s&name=%s&c_tok=%s",
		url.QueryEscape(ssoFormData.Get("type")),
		url.QueryEscape(ssoFormData.Get("sys")),
		url.QueryEscape(ssoFormData.Get("prcnam")),
		url.QueryEscape(ssoFormData.Get("qual")),
		url.QueryEscape(ssoFormData.Get("name")),
		url.QueryEscape(ssoFormData.Get("c_tok")),
	)

	return orderedSsoFormData, nil
}

func (a *Aljex) sso(ctx context.Context, orderedFormData string) error {
	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(orderedFormData),
		true, s3backup.TypeTokens)
	if err != nil {
		return fmt.Errorf("failed to complete Aljex SSO flow: %w", err)
	}

	document, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return fmt.Errorf("could not parse HTML from Aljex SSO flow - POST response: %w", err)
	}

	document.Find("input").Each(func(_ int, element *goquery.Selection) {
		name, _ := element.Attr("name")
		value, _ := element.Attr("value")

		switch name {
		case "qual":
			a.creds.Qual = value
		case "name":
			a.creds.Name = value
		case "company":
			a.creds.Company = value
		case "qualifier":
			a.creds.Qualifier = value
		case "c_tok":
			a.creds.Token = value
		}
	})

	if a.creds.Token == "" {
		log.Debug(ctx, "SSO response", zap.ByteString("html", resp))

		return errors.New("failed to retrieve token from Aljex SSO response")
	}

	return nil
}
