package aljex

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestLivePostCheckCall(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLivePostCheckCall: run with LIVE_TEST=true to enable")
		return
	}

	pro := "2080005"

	// Particularly important in this case to not modify real PROs as there's no way to delete check calls
	require.Contains(t, axleTestPROs, pro)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	ctx := context.Background()

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	c := models.CheckCall{
		City:   "BOSTON",
		State:  "MA",
		Status: "On Time",
		Reason: "Normal Status",
		DateTimeWithoutTimezone: models.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		Notes: "Test check call",
	}

	err = client.PostCheckCall(ctx, &models.Load{}, c)
	assert.NoError(t, err)
}

func TestIsValidEDIReasonEnum(t *testing.T) {
	t.Run("OK", func(t *testing.T) {
		assert.True(t, IsValidEDIReasonEnum("UNABLE TO LOCATE"))
	})

	t.Run("OK - case insensitive", func(t *testing.T) {
		assert.True(t, IsValidEDIReasonEnum("unAblE TO locate"))
	})

	t.Run("Not OK", func(t *testing.T) {
		assert.False(t, IsValidEDIReasonEnum("RandomString"))
	})
}

func TestIsValidMessageEnum(t *testing.T) {
	t.Run("OK", func(t *testing.T) {
		assert.True(t, IsValidMessageEnum("On Time"))
	})

	t.Run("OK - case insensitive", func(t *testing.T) {
		assert.True(t, IsValidMessageEnum("on time"))
	})

	t.Run("Not OK", func(t *testing.T) {
		assert.False(t, IsValidMessageEnum("RandomString"))
	})
}

func TestLiveGetCheckCallsHistory(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetCheckCallHistory: run with LIVE_TEST=true to enable")
		return
	}

	// Some invalid enum warnings expected due to testing and inability to delete check calls
	pro := "2080005"

	require.Contains(t, axleTestPROs, pro)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	ctx := context.Background()

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	history, err := client.GetCheckCallsHistory(ctx, 1, pro)
	assert.NoError(t, err)

	// Case-sensitive test
	repCall := models.CheckCall{
		LoadID:            1,
		FreightTrackingID: pro,
		City:              "Boston",
		State:             "MA",
		Status:            "On Time",
		Reason:            "NORMAL STATUS",
		DateTimeWithoutTimezone: models.NullTime{
			Time:  time.Date(2023, 12, 3, 10, 0, 0, 0, time.UTC),
			Valid: true,
		},
		Author: "AXLEAPI",
		Notes: "On Time Test check call On Time Test check call On Time Test check call " +
			"On Time Test check call On Time Testing no pro On Time Testing no pro",
	}

	expected := []models.CheckCall{
		repCall,
		repCall,
		repCall,
		repCall,
		{
			LoadID:            1,
			FreightTrackingID: pro,
			City:              "",
			State:             "",
			Status:            "On Time",
			Reason:            "",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2023, 12, 3, 10, 0, 0, 0, time.UTC),
				Valid: true,
			},
			Author: "AXLEAPI",
			Notes: "On Time Test check call On Time Test check call On Time Test check call " +
				"On Time Test check call On Time Testing no pro On Time Testing no pro",
		},
		{
			LoadID:            1,
			FreightTrackingID: pro,
			City:              "Boston",
			State:             "",
			Status:            "On Time",
			Reason:            "",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2023, 12, 3, 10, 0, 0, 0, time.UTC),
				Valid: true,
			},
			Author: "AXLEAPI",
			Notes: "On Time Test check call On Time Test check call On Time Test check call " +
				"On Time Test check call On Time Testing no pro On Time Testing no pro",
		},
		{
			LoadID:            1,
			FreightTrackingID: pro,
			City:              "Fakecity124235",
			State:             "MA",
			Status:            "On Time",
			Reason:            "",
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2023, 12, 4, 10, 0, 0, 0, time.UTC),
				Valid: true,
			},
			Author: "AXLEAPI",
			Notes:  "On Time Testing bad city and state",
		},
	}

	require.Greater(t, len(history), 8)
	for i, e := range expected {
		// 8 + i instead of i because addtl testing changed list
		t.Log("index: ", i)
		assert.Equal(t, e, history[i])
	}
}
