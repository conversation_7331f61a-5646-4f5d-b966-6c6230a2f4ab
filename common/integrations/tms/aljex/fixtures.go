package aljex

import (
	"context"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

func aljexTestClient(ctx context.Context, appID, password, mfaSecret string) models.Integration {
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		log.Error(ctx, "failed to encrypt test aljex client password", zap.Error(err))
	}

	integration := models.Integration{
		Model: gorm.Model{
			ID: 4,
		},
		Name:              "aljex",
		Type:              "tms",
		ServiceID:         1,
		AppID:             appID,
		Tenant:            "nfi",
		Username:          "axleapi",
		EncryptedPassword: []byte(encryptedPassword),
		TwoFactorSecret:   mfaSecret,
	}

	return integration
}
