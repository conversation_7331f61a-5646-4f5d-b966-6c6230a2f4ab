package aljex

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/s3backup"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

func (a *Aljex) getAPIURL() string {
	return fmt.Sprintf("https://%s.aljex.com/route.php", a.config.Tenant)
}

func (a *Aljex) get(
	ctx context.Context,
	url string,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return a.getWithHeaders(ctx, url, map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}, true, dataType)
}

func (a *Aljex) getWithHeaders(
	ctx context.Context,
	url string,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return a.makeHTTPRequest(ctx, http.MethodGet, url, nil, headers, useCookies, dataType)
}

func (a *Aljex) postForm(
	ctx context.Context,
	url string,
	body io.Reader,
	useCookies bool,
	dataType s3backup.DataType,
	checkRespFunc ...func(htmlBody string) int,
) ([]byte, []*http.Cookie, error) {
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	return a.postWithHeaders(ctx, url, body, headers, useCookies, dataType, checkRespFunc...)
}

func (a *Aljex) postWithHeaders(ctx context.Context,
	url string,
	body io.Reader,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
	checkRespFunc ...func(htmlBody string) int,
) ([]byte, []*http.Cookie, error) {
	return a.makeHTTPRequest(ctx, http.MethodPost, url, body, headers, useCookies, dataType, checkRespFunc...)
}

// `checkRespFunc` allows route to pass in custom error response check function. `htmlBody` is the
// HTML doc in the response, and the returned value `int` is the status code. If the returned status code is >= 300,
// it returns a errtypes.HTTPResponseError
func (a *Aljex) makeHTTPRequest(
	ctx context.Context,
	method string,
	url string,
	body io.Reader,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
	checkRespFunc ...func(htmlBody string) int,
) ([]byte, []*http.Cookie, error) {
	var req *http.Request

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create %s request for Aljex: %w", method, err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	if useCookies {
		log.Info(ctx, "Using cookies for Aljex request", zap.Any("cookies", (a.cookies)))
		for _, cookie := range a.cookies {
			req.AddCookie(cookie)
		}
	}

	if err = a.rateLimitCheck(ctx); err != nil {
		return nil, nil, fmt.Errorf("rate limit check failed: %w", err)
	}

	resp, err := a.httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, a.tms, err)
		return nil, nil, fmt.Errorf("could not send %s request for Aljex: %w", method, err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("error reading response body: %w", err)
	}

	if aws.S3Uploader != nil && dataType != "" {
		if _, err = aws.S3Uploader.TMSResponse(ctx, a.tms, dataType,
			util.APIResponse{Method: req.Method, Status: resp.StatusCode, Body: string(respBody)}); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Warn(ctx, "s3 archive failed", zap.Error(err), zap.String("dataType", string(dataType)))
		}
	}

	if err = a.respToHTTPResponseError(req, resp, respBody, checkRespFunc...); err != nil {
		a.sessionRefreshCheck(ctx, string(respBody))
	}
	httplog.LogHTTPResponseCode(ctx, a.tms, resp.StatusCode)

	log.Debug(ctx, "Aljex response cookies", zap.Any("cookies", resp.Cookies()))

	return respBody, resp.Cookies(), err
}

// Aljex returns 200s with error messages in HTML body, so this helper conforms Aljex responses to more
// standard HTTP behavior.
// Update func with additional cases as they're observed
func (a *Aljex) respToHTTPResponseError(
	req *http.Request,
	resp *http.Response,
	body []byte,
	checkRespFunc ...func(htmlBody string) int,
) error {
	// If Aljex returned a non-2xx, don't change it
	if resp.StatusCode >= 300 {
		return errtypes.NewHTTPResponseError(a.tms, req, resp, body)
	}

	htmlBody := strings.ToLower(string(body))

	if strings.Contains(htmlBody, "your session has timed out due to inactivity") ||
		strings.Contains(htmlBody, "no session credentials found") {

		resp.StatusCode = http.StatusUnauthorized
		return errtypes.NewHTTPResponseError(a.tms, req, resp, body)
	}

	// If provided, use custom error check policy
	if len(checkRespFunc) > 0 && checkRespFunc[0] != nil {
		statusCode := checkRespFunc[0](htmlBody)
		if statusCode >= 300 {
			resp.StatusCode = statusCode
			return errtypes.NewHTTPResponseError(a.tms, req, resp, body)
		}
	}

	if strings.Contains(htmlBody, "invalid pro") || strings.Contains(htmlBody, "not valid") ||
		strings.Contains(htmlBody, "no loads found") ||
		strings.Contains(htmlBody, "view this debit in the invoice file") {
		resp.StatusCode = http.StatusNotFound
		return errtypes.NewHTTPResponseError(a.tms, req, resp, body)
	}

	// NOTE: This message is expected when listing operators
	if strings.Contains(htmlBody, "aljex returned this message") &&
		!strings.Contains(htmlBody, strings.ToLower("<title>smart search - aljex vision</title>")) {

		resp.StatusCode = http.StatusBadRequest
		return errtypes.NewHTTPResponseError(a.tms, req, resp, body)
	}

	return nil

}
