package globaltranztms

import (
	"regexp"
	"strings"
)

const BOLNumberType = "bolNumber"
const PoNumberType = "poNumber"

// For parsing GlobalTranz TMS order into internal load model
const CustomerRefNumberType = "customerRefNumber"
const PickupNumberType = "pickupNumber"
const DeliveryNumberType = "deliveryNumber"

// Available GLobalTranz TMS identifiers -- not used for now
// const QuoteNumberType = "quoteNumber"                              Internal ID = 1
// const ProNumberType = "proNumber"                                  Internal ID = 5
// const CarrierQuoteNumberType = "carrierQuoteNumber"                Internal ID = 9

func MatchPONumbers(input string) []string {
	var result []string
	input = strings.TrimSpace(input)

	regexPatterns := []*regexp.Regexp{
		// PO# or Order# formats (with optional space): PO# 123456 or Order #123456
		regexp.MustCompile(`(?i)(po\s*#\s*|order\s*#\s*)([0-9\-]+)`),

		// Codes like SO00066840, GL 59001, SO-00066840, PD-0140 etc.
		regexp.MustCompile(`\b[A-Z]{1,2}[-\s]?\d{4,}\b`),

		// Generic long numbers (5+ digits), also covers codes with dash at the end
		regexp.MustCompile(`\b\d{5,}(-[A-Za-z0-9]{1,2})?\b`),
	}

	seen := make(map[string]bool)

	for _, re := range regexPatterns {
		matches := re.FindAllStringSubmatch(input, -1)
		for _, match := range matches {
			// Use the last captured group or full match
			candidate := match[len(match)-1]
			if !seen[candidate] {
				seen[candidate] = true
				result = append(result, candidate)
			}
		}
	}

	return result
}
