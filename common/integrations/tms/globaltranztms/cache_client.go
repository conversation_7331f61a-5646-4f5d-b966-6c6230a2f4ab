package globaltranztms

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const globaltranzAuthTokenTTL = 40 * time.Minute

func redisClientKey(serviceID uint, integrationID uint) string {
	return fmt.Sprintf("globaltranztms-service-%d-integration-%d", serviceID, integrationID)
}

type SerializableGlobalTranzClient struct {
	AccessToken string
	Integration models.Integration
	AuthHost    string
	TMSHost     string
}

func (gt *GlobalTranz) marshalGlobalTranzClient() (string, error) {
	sGlobalTranz := SerializableGlobalTranzClient{
		AccessToken: gt.accessToken,
		Integration: gt.tms,
		AuthHost:    gt.authHost,
		TMSHost:     gt.tmsHost,
	}

	bytes, err := json.<PERSON>(sGlobalTranz)
	return string(bytes), err
}

func unmarshalGlobalTranzClient(data string) (*GlobalTranz, error) {
	var sGlobalTranz SerializableGlobalTranzClient
	err := json.Unmarshal([]byte(data), &sGlobalTranz)
	if err != nil {
		return nil, err
	}

	globaltranzClient := &GlobalTranz{
		accessToken: sGlobalTranz.AccessToken,
		tms:         sGlobalTranz.Integration,
		authHost:    sGlobalTranz.AuthHost,
		tmsHost:     sGlobalTranz.TMSHost,
	}

	return globaltranzClient, nil
}

func getRedisClient(ctx context.Context, serviceID uint, integrationID uint) (*GlobalTranz, error) {
	str, err := redis.RDB.Get(ctx, redisClientKey(serviceID, integrationID)).Result()
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get globaltranz client from Redis", zap.Error(err))
	}

	if str != "" {
		client, err := unmarshalGlobalTranzClient(str)
		if err != nil {
			log.Warn(ctx, "failed to unmarshal globaltranz client from Redis", zap.Error(err))
			return nil, err
		}

		if client != nil {
			log.Info(ctx, "re-using existing globaltranz client")
		}

		return client, nil
	}

	return nil, nil
}

func (gt *GlobalTranz) setRedisClient(ctx context.Context) {
	serializedDATClient, err := gt.marshalGlobalTranzClient()
	if err != nil {
		log.Warn(ctx, "failed to json marshal globaltranz client for Redis", zap.Error(err))
	}

	redisKey := redisClientKey(gt.tms.ServiceID, gt.tms.ID)
	err = redis.RDB.Set(ctx, redisKey, serializedDATClient, globaltranzAuthTokenTTL).Err()
	if err != nil {
		log.Warn(ctx, "failed to set globaltranz client in Redis", zap.Error(err))
	}
}
