package globaltranztms

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
)

type (
	OrderDetails struct {
		OrderBK                              int            `json:"orderBK"`
		OverrideIsCarrierBookableLoadLevel   bool           `json:"overrideIsCarrierBookableLoadLevel"`
		Customer                             CustomerInfo   `json:"customer"`
		ServiceTypeID                        int            `json:"serviceTypeId"`
		CreatedDate                          string         `json:"createdDate"`
		QuotedDate                           string         `json:"quotedDate"`
		GeneratedBy                          string         `json:"generatedBy"`
		CreatedBy                            string         `json:"createdBy"`
		RatingMethod                         string         `json:"ratingMethod"`
		ContactName                          string         `json:"contactName"`
		SalesRepName                         string         `json:"salesRepName"`
		SalesRepEmail                        string         `json:"salesRepEmail"`
		SalesRepPhone                        string         `json:"salesRepPhone"`
		SalesRepAgency                       string         `json:"salesRepAgency"`
		CSRName                              string         `json:"csrName"`
		CSRPhone                             *string        `json:"csrPhone"`
		CSREmail                             *string        `json:"csrEmail"`
		QuoteBK                              int            `json:"quoteBK"`
		SpotQuoteNumber                      *string        `json:"spotQuoteNumber"`
		Address                              []Address      `json:"address"`
		CarrierBK                            int            `json:"carrierBK"`
		CarrierName                          string         `json:"carrierName"`
		CarrierURL                           *string        `json:"carrierUrl"`
		Logo                                 *string        `json:"logo"`
		CommodityDescription                 []string       `json:"commodityDescription"`
		HandlingUnitCount                    *int           `json:"handlingUnitCount"`
		Weight                               float64        `json:"weight"`
		WeightUnitID                         int            `json:"weightUnitId"`
		Miles                                float64        `json:"miles"`
		InsuredAmount                        float64        `json:"insuredAmount"`
		IsInsuranceApplied                   bool           `json:"isInsuranceApplied"`
		Identifiers                          []Identifier   `json:"identifiers"`
		Financials                           any            `json:"financials"`
		Items                                []Item         `json:"items"`
		ProcessStatusID                      int            `json:"processStatusId"`
		InvoiceStatusID                      int            `json:"invoiceStatusId"`
		VendorBillStatusID                   int            `json:"vendorBillStatusId"`
		IsVendorBillVisible                  bool           `json:"isVendorBillVisible"`
		EDIStatus                            *string        `json:"ediStatus"`
		StatusID                             int            `json:"statusId"`
		Notes                                []Note         `json:"notes"`
		Documents                            []Document     `json:"documents"`
		TransitDays                          string         `json:"transitDays"`
		Cost                                 CostDetails    `json:"cost"`
		Revenue                              RevenueDetails `json:"revenue"`
		ShipmentDate                         string         `json:"shipmentDate"`
		PickupRemarks                        string         `json:"pickupRemarks"`
		DeliveryRemarks                      string         `json:"deliveryRemarks"`
		IsEDISupport                         bool           `json:"isEdiSupport"`
		IsUpdateTender                       *bool          `json:"isUpdateTender"`
		ShipmentTypeID                       int            `json:"shipmentTypeId"`
		IsOrderBillAttached                  bool           `json:"isOrderBillAttached"`
		GuaranteedLTLTimeFrame               *string        `json:"guaranteedLTLTimeFrame"`
		CarrierServiceTypeName               *string        `json:"carrierServiceTypeName"`
		CarrierType                          int            `json:"carrierType"`
		TotalLinearFeet                      *float64       `json:"totalLinearFeet"`
		PricingType                          int            `json:"pricingType"`
		EstimatedDueDate                     string         `json:"estimatedDueDate"`
		CarrierCode                          *string        `json:"carrierCode"`
		ModeType                             int            `json:"modeType"`
		CustomerInfo                         ContactInfo    `json:"customerInfo"`
		SalesRepInfo                         ContactInfo    `json:"salesRepInfo"`
		QuotedByInfo                         ContactInfo    `json:"quotedByInfo"`
		BookedBy                             ContactInfo    `json:"bookedBy"`
		TLEquipment                          TLEquipment    `json:"tlEquipment"`
		Insurance                            any            `json:"insurance"`
		FalveyInsuranceCost                  float64        `json:"falveyInsurance.insuranceCost"`
		FalveyInsuranceRevenue               float64        `json:"falveyInsurance.insuranceRevenue"`
		Carrier                              *CarrierInfo   `json:"carrier"`
		CargoValue                           float64        `json:"cargoValue"`
		IsCustomerBOLCheckedForRC            bool           `json:"isCustomerBOLCheckedForRC"`
		IsCanGet                             bool           `json:"isCanGet"`
		IsCustomerSCMTag                     bool           `json:"isCustomerSCMTag"`
		IsIMRateComparison                   bool           `json:"isIMRateComparison"`
		DeliveryDate                         string         `json:"deliveryDate"`
		SalesRepID                           int            `json:"salesRepID"`
		IsBrokerAgent                        bool           `json:"isBrokerAgent"`
		LoadID                               int            `json:"loadId"`
		UpdatedDateTime                      string         `json:"updatedDateTime"`
		LoadRequestID                        int            `json:"loadRequestId"`
		DOTNo                                string         `json:"dotNo"`
		TenderID                             int            `json:"tenderId"`
		Problems                             []any          `json:"problems"`
		VisibilityAppName                    *string        `json:"visibilityAppName"`
		EDIStatusDetails                     any            `json:"ediStatusDetails"`
		CanAddAllRiskInsuranceNoteType       bool           `json:"canAddAllRiskInsuranceNoteType"`
		CanAssigntoVolumeCarrier             bool           `json:"canAssigntoVolumeCarrier"`
		CanUserAssignToCarrier               bool           `json:"canUserAssignToCarrier"`
		CanUserCancelLoad                    bool           `json:"canUserCancelLoad"`
		CanUserEditBookedTruckloads          bool           `json:"canUserEditBookedTruckloads"`
		CanUserEditLoadStatus                bool           `json:"canUserEditLoadStatus"`
		CanUserEditLoadZipCodes              bool           `json:"canUserEditLoadZipCodes"`
		CanUserEditOpenLoadCarrier           bool           `json:"canUserEditOpenLoadCarrier"`
		CanUserEditQuotedCosts               bool           `json:"canUserEditQuotedCosts"`
		CanUserEditQuotedFreightAndService   bool           `json:"canUserEditQuotedFreightAndService"`
		CanUserMultiRateQuote                bool           `json:"canUserMultiRateQuote"`
		CanUserRemoveCarrier                 bool           `json:"canUserRemoveCarrier"`
		CanUserViewCarrierContactInformation bool           `json:"canUserViewCarrierContactInformation"`
		CanUserViewTLCosts                   bool           `json:"canUserViewTLCosts"`
		CanUserSetLoadMaxBuy                 bool           `json:"canUserSetLoadMaxBuy"`
		DoesUserHaveFullAccess               bool           `json:"doesUserHaveFullAccess"`
		OldStatus                            int            `json:"oldStatus"`
		AccountExecutiveID                   *int           `json:"accountExecutiveId"`
		AccountExecutiveName                 *string        `json:"accountExecutiveName"`
		SchedulerAssistant                   *string        `json:"schedulerAssistant"`
		CarrierSalesRepID                    *int           `json:"carrierSalesRepId"`
		CarrierSalesRepName                  *string        `json:"carrierSalesRepName"`
		BookedDate                           *string        `json:"bookedDate"`
		DeleteAssetResponseWarnings          any            `json:"deleteAssetResponseWarnings"`
		IsLocked                             bool           `json:"isLocked"`
		IsLockedByCurrent                    bool           `json:"isLockedByCurrent"`
		LockedBy                             string         `json:"lockedBy"`
		UpdateOrderToken                     *string        `json:"updateOrderToken"`
		ShipmentFinancials                   any            `json:"shipmentFinancials"`
		VendorBillID                         *int           `json:"vendorBillId"`
		ShipmentDateReadyTime                *string        `json:"shipmentDateReadyTime"`
		ShipmentDateCloseTime                *string        `json:"shipmentDateCloseTime"`
		Accessorials                         any            `json:"accessorials"`
		IsSCMChecked                         *bool          `json:"isSCMChecked"`
		AccountManagerInfo                   ContactInfo    `json:"accountManagerInfo"`
		AccountManagerID                     int            `json:"accountManagerID"`
		Routes                               []Address      `json:"routes"`
	}

	Identifier struct {
		TypeID int    `json:"typeId"`
		Value  string `json:"value"`
	}

	Address struct {
		ID                            int            `json:"id"`
		Company                       string         `json:"company"`
		AddressTypeID                 int            `json:"addressTypeId"`
		Contact                       Contact        `json:"contact"`
		Address                       Location       `json:"address"`
		AppointmentDate               string         `json:"appointmentDate"`
		DockHours                     DockHours      `json:"dockHours"`
		ActualDateTime                ActualDateTime `json:"actualDateTime"`
		DeliveryDockHours             DockHours      `json:"deliveryDockHours"`
		IsPickupAppointmentRequired   bool           `json:"isPickupAppointmentRequired"`
		IsDeliveryAppointmentRequired bool           `json:"isDeliveryAppointmentRequired"`
		Accessorials                  []any          `json:"accessorials"`
		AppointmentMade               bool           `json:"appointmentMade"`
		AppointmentConfirmationNumber string         `json:"appointmentConfirmationNumber"`
		FreightOpenTime               string         `json:"freightOpenTime"`
		FreightCloseTime              string         `json:"freightCloseTime"`
		FreightDateTime               string         `json:"freightDateTime"`
		StopBK                        int            `json:"stopBk"`
		StopNumber                    int            `json:"stopNumber"`
		StopType                      string         `json:"stopType"`
		ShipmentLocationType          int            `json:"shipmentLocationType"`
		PODSignedBy                   *string        `json:"podSignedBy"`
		IsPhoneRequired               bool           `json:"isPhoneRequired"`
		LateReasonCode                *string        `json:"lateReasonCode"`
		IsAppointmentRescheduled      bool           `json:"isAppointmentRescheduled"`
		RescheduleReasonCode          *string        `json:"rescheduleReasonCode"`
		IsAppointmentModified         bool           `json:"isAppointmentModified"`
		Remarks                       *string        `json:"remarks"`
		TerminalPhone                 *string        `json:"terminalPhone"`
	}

	Contact struct {
		ContactPerson string  `json:"contactPerson"`
		Email         string  `json:"email"`
		Fax           *string `json:"fax"`
		Phone         string  `json:"phone"`
	}

	Location struct {
		Street1   string  `json:"street1"`
		Street2   *string `json:"street2"`
		City      string  `json:"city"`
		State     string  `json:"state"`
		Zip       string  `json:"zip"`
		Country   int     `json:"country"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	}

	DockHours struct {
		From string `json:"from"`
		To   string `json:"to"`
	}

	ActualDateTime struct {
		CheckInDate  *string `json:"checkInDate"`
		CheckInTime  *string `json:"checkInTime"`
		CheckOutDate *string `json:"checkOutDate"`
		CheckOutTime *string `json:"checkOutTime"`
	}

	Item struct {
		ID                     int        `json:"id"`
		UserDescription        string     `json:"userDescription"`
		NMFCNumber             string     `json:"nmfcNumber"`
		FreightClassID         int        `json:"freightClassId"`
		Weight                 float64    `json:"weight"`
		WeightUnitID           int        `json:"weightUnitId"`
		PieceCount             int        `json:"pieceCount"`
		HandlingUnitCount      int        `json:"handlingUnitCount"`
		HandlingUnitTypeID     int        `json:"handlingUnitTypeId"`
		Hazmat                 any        `json:"hazmat"`
		Dimensions             Dimensions `json:"dim"`
		Stackable              bool       `json:"stackable"`
		CommodityDescription   string     `json:"commodityDescription"`
		ProductName            string     `json:"productname"`
		ItemID                 *int       `json:"itemId"`
		ItemIDAndAccessorialID *int       `json:"itemIdAndAccessorialId"`
	}

	Dimensions struct {
		Length    float64 `json:"length"`
		Width     float64 `json:"width"`
		Height    float64 `json:"height"`
		DimUnitID int     `json:"dimUnitId"`
	}

	CostDetails struct {
		Total                  float64    `json:"total"`
		LineItems              []LineItem `json:"lineItems"`
		PriceToBeat            float64    `json:"priceToBeat"`
		TargetMargin           float64    `json:"targetMargin"`
		TargetCost             float64    `json:"targetCost"`
		MaxBuy                 float64    `json:"maxBuy"`
		Margin                 float64    `json:"margin"`
		ItemID                 *int       `json:"itemId"`
		ItemIDAndAccessorialID *int       `json:"itemIdAndAccessorialId"`
	}

	RevenueDetails struct {
		Total     float64    `json:"total"`
		LineItems []LineItem `json:"lineItems"`
	}

	LineItem struct {
		ID                     int     `json:"id"`
		ChargeName             string  `json:"chargeName"`
		Amount                 float64 `json:"amount"`
		AccessorialID          int     `json:"accessorialId"`
		Charge                 float64 `json:"charge"`
		ItemID                 *int    `json:"itemId"`
		ItemIDAndAccessorialID *int    `json:"itemIdAndAccessorialId"`
	}

	CarrierInfo struct {
		CarrierName        string  `json:"carrierName"`
		CarrierQuoteNumber string  `json:"carrierQuoteNumber"`
		MASCode            string  `json:"masCode"`
		MCNumber           string  `json:"mcNumber"`
		ERP                string  `json:"erp"`
		CarrierContact     Contact `json:"carrierContact"`
		DriverName         string  `json:"driverName"`
		DriverPhoneNumber  string  `json:"driverPhoneNumber"`
		DriverID           int     `json:"driverId"`
		TrailerNumber      string  `json:"trailerNumber"`
		TruckNumber        string  `json:"truckNumber"`
		DriverRequired     bool    `json:"driverRequired"`
	}

	ContactInfo struct {
		ID    int     `json:"id"`
		Name  *string `json:"name"`
		Email *string `json:"email"`
		Fax   *string `json:"fax"`
		Phone *string `json:"phone"`
	}

	CustomerInfo struct {
		CustomerBK   int     `json:"customerBK"`
		CustomerName string  `json:"customerName"`
		Email        *string `json:"email"`
		Fax          *string `json:"fax"`
		Phone        *string `json:"phone"`
		Address      *string `json:"address"`
	}

	Note struct {
		UserName      string `json:"userName"`
		Text          string `json:"text"`
		NoteTypeID    int    `json:"noteTypeID"`
		TimeStamp     string `json:"timeStamp"`
		NoteID        int    `json:"noteId"`
		DisplayText   string `json:"displayText"`
		IsDeleted     bool   `json:"isDeleted"`
		IsResolved    bool   `json:"isResolved"`
		CanDeleteNote bool   `json:"canDeleteNote"`
	}

	Document struct {
		DocumentTypeID    int     `json:"documentTypeID"`
		DocumentID        *string `json:"documentID"`
		DocumentName      string  `json:"documentName"`
		IsDownloadable    bool    `json:"isDownloadable"`
		IsSystemGenerated bool    `json:"isSystemGenerated"`
	}

	TLEquipment struct {
		TruckTypeID                int      `json:"truckTypeID"`
		TLServiceTypeID            int      `json:"tlServiceTypeID"`
		EquipmentLength            int      `json:"equipmentLength"`
		PalletExchangeCount        int      `json:"palletExchangeCount"`
		DropTrailerShipperDate     *string  `json:"dropTrailerShipperDate"`
		DropTrailerShipperTime     *string  `json:"dropTrailerShipperTime"`
		DropTrailerConsigneeDate   *string  `json:"dropTrailerConsigneeDate"`
		DropTrailerConsigneeTime   *string  `json:"dropTrailerConsigneeTime"`
		Length                     *float64 `json:"length"`
		Width                      *float64 `json:"width"`
		Height                     *float64 `json:"height"`
		LowTemperature             *float64 `json:"lowTemperature"`
		HighTemperature            *float64 `json:"highTemperature"`
		TrailerDropConsigneeLength int      `json:"trailerDropConsigneeLength"`
		TrailerDropShipperLength   int      `json:"trailerDropShipperLength"`
		TarpSize                   string   `json:"tarpSize"`
		EquipmentAccessorials      any      `json:"equipmentAccessorials"`
		UseIntermodal              bool     `json:"useIntermodal"`
		IsNoncommercialDelivery    bool     `json:"isNoncommercialDelivery"`
		LinearFeet                 int      `json:"linearFeet"`
	}

	OrderDetailsResponse struct {
		DidError         bool         `json:"didError"`
		ErrorMessages    any          `json:"errorMessages"`
		Message          any          `json:"message"`
		Model            OrderDetails `json:"model"`
		ObjectWasCreated bool         `json:"objectWasCreated"`
	}
)

func (c GlobalTranz) GetOrderDetails(ctx context.Context, orderBK string) (orderDetails *OrderDetails, err error) {

	orderBKInt, err := strconv.Atoi(orderBK)
	if err != nil {
		return nil, fmt.Errorf("invalid order number: %w", err)
	}

	query := make(url.Values)
	query.Add("orderBK", fmt.Sprintf("%d", orderBKInt))
	query.Add("modeType", "3")

	var result OrderDetailsResponse
	err = c.doWithRetry(ctx, http.MethodGet, c.tmsHost, GetOrderDetailsPath, query, nil, &result)

	if err != nil {
		return nil, err
	}

	return &result.Model, nil
}
