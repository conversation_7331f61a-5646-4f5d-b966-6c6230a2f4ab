package globaltranztms

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.uber.org/zap"
	"golang.org/x/oauth2"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/httplog"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	API interface {
		GetIntegrationModel() models.Integration
		GetOrderDetails(ctx context.Context, orderBK string) (*OrderDetails, error)
		GetTlOrderBoard(
			ctx context.Context,
			fromDate, toDate time.Time,
			pageNumber, pageSize int,
			statusFilter int,
		) (GetTlOrderBoardResponse, error)
		GetAllTlOrderBoardPages(
			ctx context.Context,
			fromDate, toDate time.Time,
			statusFilter int,
		) ([]TlOrderBoardItem, error)
	}

	GlobalTranz struct {
		accessToken string
		tms         models.Integration
		// List of hosts used throughout auth and fetching data
		authHost string
		tmsHost  string
	}
)

const (
	GetOrderFilterPath    = "api/tms-order/GetTlOrderBoard"
	GetOrderDetailsPath   = "api/tms-order/Order/GetOrderDetails"
	UpdateOrderPath       = "api/tms-order/Order/UpdateOrder"
	GetCarrierHistoryPath = "api/v1/Vendor/History"
	GetLocationSearchPath = "AddressBook/SearchLocation"
)

var globalTranzBackoffSchedule = []time.Duration{
	1 * time.Second,
	3 * time.Second,
	10 * time.Second,
}

func New(ctx context.Context, integration models.Integration) *GlobalTranz {
	// Try to retrieve an existing client
	cachedClient, err := getRedisClient(ctx, integration.ServiceID, integration.ID)
	if err == nil &&
		cachedClient != nil &&
		cachedClient.accessToken != "" &&
		cachedClient.GetIntegrationModel().Username == integration.Username &&
		time.Now().Before(cachedClient.GetIntegrationModel().AccessTokenExpirationDate.Time) {

		log.Info(ctx, "re-using existing globaltranz client")

		// If client is cached, it has already been onboarded
		return cachedClient
	}

	username := integration.Username
	password := integration.EncryptedPassword

	if username == "" || password == nil {
		return nil
	}

	gtClient := GlobalTranz{
		tms:      integration,
		authHost: "is.globaltranz.com",
		tmsHost:  "tms.globaltranz.com",
	}

	tokenData, err := gtClient.Authenticate(ctx, integration.Tenant)
	if err != nil {
		log.Error(ctx, "Could not authenticate GlobalTranz client", zap.Error(err))
		return nil
	}

	gtClient.accessToken = tokenData.AccessToken

	integration.AccessToken = tokenData.AccessToken
	integration.AccessTokenExpirationDate = tokenData.AccessTokenExpirationDate

	log.Info(ctx, "successfully created GlobalTranz client", zap.String("username", username))

	gtClient.setRedisClient(ctx)

	// If integration doesn't exist on DB, it's being onboarded. Return onboarding response
	if integration.ID == 0 {
		return &gtClient
	}

	// Otherwise simply update integration record with new access token
	if errUpdate := integrationDB.Update(ctx, &integration); errUpdate != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to update globaltranz token on integrations table",
			zap.Any("integration", integration),
		)
		return nil
	}

	return &gtClient
}

func (gt GlobalTranz) GetIntegrationModel() models.Integration {
	return gt.tms
}

func (gt GlobalTranz) GetCustomers(context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, util.NotImplemented(models.GlobalTranzTMS, "GetCustomers")
}

func (gt GlobalTranz) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, util.NotImplemented(models.GlobalTranzTMS, "GetUsers")
}

func (gt GlobalTranz) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, util.NotImplemented(models.GlobalTranzTMS, "GetLocations")
}

func (gt GlobalTranz) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.GlobalTranzTMS, "GetCarriers")
}
func (gt GlobalTranz) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return util.NotImplemented(models.Ascend, "PostCheckCall")
}
func (gt GlobalTranz) PostException(context.Context, *models.Load, models.Exception) error {
	return util.NotImplemented(models.GlobalTranzTMS, "PostException")
}

func (gt GlobalTranz) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, util.NotImplemented(models.GlobalTranzTMS, "GetExceptionHistory")
}

func (gt GlobalTranz) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, util.NotImplemented(models.GlobalTranzTMS, "PostNote")
}

func (gt GlobalTranz) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (models.Load, error) {
	return models.Load{}, errtypes.NotImplemented(models.GlobalTranzTMS, "CreateLoad")
}

func (gt GlobalTranz) CreateQuote(_ context.Context, _ models.CreateQuoteBody) (*models.CreateQuoteResponse, error) {
	return &models.CreateQuoteResponse{}, errtypes.NotImplemented(models.GlobalTranzTMS, "CreateQuote")
}

func (gt GlobalTranz) GetCheckCallsHistory(context.Context, uint, string) (checkCalls []models.CheckCall, _ error) {
	return checkCalls, util.NotImplemented(models.Revenova, "GetCheckCallsHistory")
}

func (gt GlobalTranz) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnBoardGlobalTranz", otel.IntegrationAttrs(gt.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || onboardRequest.Password == "" || onboardRequest.Tenant == "" {
		return models.OnboardTMSResponse{}, errors.New("missing GlobalTranz TMS credentials")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, onboardRequest.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		EncryptedPassword: encryptedPassword,
		Username:          onboardRequest.Username,
		Tenant:            onboardRequest.Tenant,
	}, nil
}

func (gt *GlobalTranz) doWithRetry(
	ctx context.Context,
	method,
	host string,
	path string,
	query url.Values,
	reqBody io.Reader,
	out any,
) error {
	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     host,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, reqBody)
	if err != nil {
		return fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}

	req.Header.Add("Authorization", "Bearer "+gt.accessToken)

	if method == http.MethodPost || method == http.MethodPut {
		req.Header.Set("Content-Type", "application/json")
	}

	httpClient := otel.TracingHTTPClient()
	httpTransport := &http.Transport{
		//nolint:gosec // Workaround for GlobalTranz Identity Server certificate issues
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		Proxy:                 http.ProxyFromEnvironment,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	httpClient.Transport = otelhttp.NewTransport(
		httpTransport,
		otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
	)

	var apiResponse util.APIResponse
	var apiError error
	for _, backoff := range globalTranzBackoffSchedule {
		apiResponse, apiError = gt.do(ctx, httpClient, req, out)

		if apiError == nil {
			break
		}

		if apiResponse.Status == 401 {
			log.Info(ctx, "failed to authenticate with GlobalTranz, refreshing token and retrying")

			if err = gt.RefreshToken(ctx); err != nil {
				log.Error(ctx, "failed to refresh GlobalTranz token", zap.Error(err))
			}
		}

		time.Sleep(backoff)
	}

	return apiError
}

func (gt *GlobalTranz) do(
	ctx context.Context,
	httpClient *http.Client,
	req *http.Request,
	out any,
) (util.APIResponse, error) {
	resp, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, gt.tms, err)

		var oauthError *oauth2.RetrieveError
		if errors.As(err, &oauthError) {
			return util.APIResponse{Status: 401, Body: ""},
				fmt.Errorf("failed to send %s %s request: %w", req.Method, req.URL, err)
		}

		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", req.Method, req.URL, err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return util.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", req.URL, err)
	}

	if resp.StatusCode == http.StatusInternalServerError {
		resp.StatusCode = gt.parseErrorStatusCode(string(body))
	}

	httplog.LogHTTPResponseCode(ctx, gt.tms, resp.StatusCode)

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(gt.tms, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for GlobalTranz response body",
				zap.ByteString("body", body))

			return util.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", req.Method, req.URL, err)
		}

		log.Debug(ctx, "received GlobalTranz response",
			zap.String("method", req.Method), zap.String("url", req.URL.String()), zap.Any("body", out))
	}

	return util.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}

// GlobalTranz returns some 4xx errors as 500s with error messages in payload,
// so this helper conforms GlobalTranz status codes to more standard HTTP behavior.
const orderNotFoundPattern = `OrderBk = \d+ missing`

func (gt *GlobalTranz) parseErrorStatusCode(stringifiedResponse string) int {
	orderNotFoundRegex := regexp.MustCompile(orderNotFoundPattern)
	if len(orderNotFoundRegex.FindAllString(stringifiedResponse, -1)) > 0 {
		return http.StatusNotFound
	}

	return http.StatusInternalServerError
}
