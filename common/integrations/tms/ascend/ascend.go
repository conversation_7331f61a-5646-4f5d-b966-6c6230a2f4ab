package ascend

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	httputil "github.com/drumkitai/drumkit/common/util/http"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

const (
	tmsHost     = "www.ascendtms.com"
	tokenHeader = "authtoken"
)

type Ascend struct {
	tms models.Integration
}

func (a Ascend) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	dataType s3backup.DataType,
) error {
	rawQuery := ""
	if queryParams != nil {
		rawQuery = queryParams.Encode()
	}
	headerMap := make(map[string]string)
	headerMap["Cookie"] = fmt.Sprintf("auth=%s", a.tms.AccessToken)
	headerMap[tokenHeader] = a.tms.AccessToken

	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: rawQuery}
	var auth string
	body, _, err := httputil.GetBytesWithToken(ctx, a.tms, addr, headerMap, &auth, dataType)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (a Ascend) postNoAuth(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	dataType s3backup.DataType,
) error {
	return a.post(ctx, path, queryParams, reqBody, dst, nil, dataType)
}

func (a Ascend) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	dataType s3backup.DataType) (err error) {
	rawQuery := ""
	if queryParams != nil {
		rawQuery = queryParams.Encode()
	}
	headerMap := make(map[string]string)
	headerMap["Cookie"] = fmt.Sprintf("auth=%s", a.tms.AccessToken)
	headerMap[tokenHeader] = a.tms.AccessToken
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: rawQuery}
	body, _, err := httputil.PostBytesWithToken(ctx, a.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (a Ascend) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	dataType s3backup.DataType,
) (err error) {
	rawQuery := ""
	if queryParams != nil {
		rawQuery = queryParams.Encode()
	}
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: rawQuery}
	headerMap := make(map[string]string)
	headerMap["Cookie"] = fmt.Sprintf("auth=%s", a.tms.AccessToken)
	headerMap[tokenHeader] = a.tms.AccessToken
	var auth string
	body, _, err := httputil.PutBytesWithToken(ctx, a.tms, addr, reqBody, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func New(ctx context.Context, tms models.Integration) *Ascend {
	log.With(ctx, zap.Uint("axleTMSID", tms.ID), zap.String("tmsName", "ascend"),
		zap.String("host", tmsHost))
	return &Ascend{tms: tms}
}

func (a Ascend) CreateLoad(context.Context, models.Load, *models.TMSUser) (result models.Load, _ error) {
	return result, util.NotImplemented(models.Ascend, "CreateLoad")
}

func (a Ascend) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, util.NotImplemented(models.Ascend, "GetLocations")
}

func (a Ascend) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.Ascend, "GetCarriers")
}

func (a Ascend) GetLoadsByIDType(
	ctx context.Context, id string, idType string) (loads []models.Load, attr models.LoadAttributes, _ error) {

	switch idType {
	case LoadIDType:
		load, attrs, err := a.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return loads, attr, util.NotImplemented(models.Ascend, "GetLoadsByIDType")
	}
}

func (a Ascend) GetCheckCallsHistory(context.Context, uint, string) (checkcalls []models.CheckCall, _ error) {
	return checkcalls, util.NotImplemented(models.Ascend, "GetCheckCallsHistory")
}

func (a Ascend) GetTestLoads() (loadMap map[string]bool) {
	return loadMap
}

func (a Ascend) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return util.NotImplemented(models.Ascend, "PostCheckCall")
}

func (a Ascend) GetCustomers(_ context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, util.NotImplemented(models.Ascend, "GetCustomers")
}

func (a Ascend) CreateQuote(
	_ context.Context,
	_ models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, util.NotImplemented(models.Ascend, "CreateQuote")
}

func (a Ascend) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, util.NotImplemented(models.Ascend, "GetUsers")
}

func (a Ascend) PostException(context.Context, *models.Load, models.Exception) error {
	return util.NotImplemented(models.Ascend, "PostException")
}

func (a Ascend) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, util.NotImplemented(models.Ascend, "PostNote")
}

func (a Ascend) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, util.NotImplemented(models.Ascend, "GetExceptionHistory")
}
