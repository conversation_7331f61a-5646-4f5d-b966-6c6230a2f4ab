package turvo

import (
	"context"
	"fmt"
	"net/url"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

const searchCarriersPath = "/v1/carriers/list"

func (t *Turvo) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, util.NotImplemented(models.Turvo, "GetCarriers")
}

func (t *Turvo) SearchCarriers(
	ctx context.Context,
	name string,
	mcNumber string,
	dotNumber string,
) (*CarrierSearchResponse, error) {
	ctx, span := otel.StartSpan(ctx, "turvo.SearchCarriers", []attribute.KeyValue{
		attribute.String("tms", string(t.tms.Name)),
	})
	defer span.End(nil)

	// Construct query parameters
	queryParams := url.Values{}
	if name != "" {
		queryParams.Add("name[eq]", name)
	}
	if mcNumber != "" {
		queryParams.Add("mcNumber[eq]", mcNumber)
	}
	if dotNumber != "" {
		queryParams.Add("dotNumber[eq]", dotNumber)
	}

	// Make the API call
	var response CarrierSearchResponse
	if err := t.getWithAuth(ctx, searchCarriersPath, queryParams, &response, s3backup.TypeCarriers); err != nil {
		return nil, fmt.Errorf("failed to search carriers: %w", err)
	}

	// Check for errors in the response
	if response.Status == "Error" {
		return nil, fmt.Errorf("failed to search carriers: %s", response.Details.ErrorMessage)
	}

	return &response, nil
}
