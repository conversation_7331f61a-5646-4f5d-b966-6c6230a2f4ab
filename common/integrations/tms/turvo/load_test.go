package turvo

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/gofiber/fiber/v2/log"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

// customId of Turvo test shipment provided via ATSA.
var testID = "31408-01721"

// Test token refresh and get load
func TestLiveGetLoad(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetLoad:  run with LIVE_TEST=true to enable")
		return
	}

	if err := godotenv.Load(); err != nil {
		log.Warnf("unable to load .env file:  %s", err)
	}

	ctx := context.Background()
	tmsDBUpdateFunc = func(_ context.Context, updatedIntegration *models.Integration) error {
		updatedIntegration.UpdatedAt = time.Now()

		return nil
	}

	tms := models.Integration{
		Model: gorm.Model{
			ID:        1,
			UpdatedAt: time.Now().Add(-11 * time.Hour), // force a token refresh by setting to > 10 hrs
		},
		Name:     models.Turvo,
		Type:     models.TMS,
		Username: "<EMAIL>",
		APIKey:   os.Getenv("TURVO_API_KEY"), // Get from 1Pass
	}
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, os.Getenv("TURVO_PASS"), nil) // Get from 1Pass
	require.NoError(t, err)
	tms.EncryptedPassword = []byte(encryptedPassword)

	client := New(ctx, tms)

	actual, _, err := client.GetLoad(ctx, testID)
	require.NoError(t, err)
	assert.NotEmpty(t, actual)

	t.Logf("current load:  %#v\n", actual)

}

// TestMapToValue tests the mapToValue function which is used to map a value to a key in a reverse map
func TestMapToValue(t *testing.T) {
	tests := []struct {
		name     string
		value    string
		m        map[string]string
		expected KeyValuePair
	}{
		{
			name:  "Value exists in map",
			value: "key1",
			m: map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
			expected: KeyValuePair{
				Key:   "value1",
				Value: "key1",
			},
		},
		{
			name:  "Value does not exist in map",
			value: "key3",
			m: map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
			expected: KeyValuePair{},
		},
		{
			name:     "Empty map",
			value:    "key1",
			m:        map[string]string{},
			expected: KeyValuePair{},
		},
		{
			name:  "Empty value",
			value: "",
			m: map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
			expected: KeyValuePair{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mapToValue(tt.value, tt.m)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_parseDistanceValue(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected float32
	}{
		{
			name:     "empty raw message",
			input:    nil,
			expected: 0,
		},
		{
			name:     "float64 value",
			input:    123.45,
			expected: 123.45,
		},
		{
			name:     "integer value",
			input:    42,
			expected: 42.0,
		},
		{
			name:     "int64 value",
			input:    int64(9999),
			expected: 9999.0,
		},
		{
			name:     "valid string number",
			input:    "78.90",
			expected: 78.90,
		},
		{
			name:     "empty string",
			input:    "",
			expected: 0,
		},
		{
			name:     "invalid string",
			input:    "not a number",
			expected: 0,
		},
		{
			name:     "boolean value (invalid type)",
			input:    true,
			expected: 0,
		},
	}

	ctx := context.Background()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var rawValue json.RawMessage
			if tt.input != nil {
				rawBytes, err := json.Marshal(tt.input)
				assert.NoError(t, err)
				rawValue = rawBytes
			}

			result := parseDistanceValue(ctx, rawValue)
			assert.Equal(t, tt.expected, result)
		})
	}
}
