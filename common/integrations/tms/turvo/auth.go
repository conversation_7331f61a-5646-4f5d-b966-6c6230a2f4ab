package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

var (
	tmsDBUpdateFunc = integrationDB.Update
)

func (t *Turvo) authenticate(ctx context.Context, username, password string) (models.OnboardTMSResponse, error) {
	tokenData, err := t.getToken(ctx, username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("getToken failed: %w", err)
	}
	log.Info(ctx, "Successfully authenticated Turvo client")

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		AccessToken:               tokenData.AccessToken,
		AccessTokenExpirationDate: tokenData.AccessTokenExpirationDate,
		EncryptedPassword:         encryptedPassword,
		Username:                  username,
		Tenant:                    tokenData.TenantRef,
	}, err
}

func (t *Turvo) getToken(
	ctx context.Context,
	username, password string,
) (tokenData *TokenData, err error) {
	reqBody := TokenRequestBody{
		GrantType: "password", // default value
		Username:  username,
		Password:  password,
		Scope:     "read+trust+write", // default value
		Type:      "business",         // default value
	}

	queryParams := url.Values{}
	queryParams.Add("client_id", "publicapi")  // default value
	queryParams.Add("client_secret", "secret") // default value

	var result TokenResponse
	err = t.postNoAuth(ctx, "/v1/oauth/token", queryParams, reqBody, &result, s3backup.TypeTokens)
	if err != nil {
		return nil, fmt.Errorf("POST token failed: %w", err)
	}

	return &TokenData{
		AccessToken:               result.AccessToken,
		AccessTokenExpirationDate: time.Now().Add(time.Duration(result.ExpiresIn) * time.Second),
		TenantRef:                 result.TenantRef,
	}, nil
}

func (t *Turvo) RefreshToken(ctx context.Context) error {
	log.Info(ctx, "refreshing Turvo client")

	password, err := crypto.DecryptAESGCM(ctx, string(t.tms.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	tokenData, err := t.getToken(ctx, t.tms.Username, password)
	if err != nil {
		return fmt.Errorf("getToken failed: %w", err)
	}

	t.tms.AccessToken = tokenData.AccessToken
	t.tms.AccessTokenExpirationDate = models.NullTime{
		Time:  tokenData.AccessTokenExpirationDate,
		Valid: true,
	}
	t.tms.Tenant = tokenData.TenantRef

	// Ensure that updated token is persisted in DB
	err = tmsDBUpdateFunc(ctx, &t.tms)
	if err != nil {
		return fmt.Errorf("updateDB error: %w", err)
	}

	return nil
}

func (t *Turvo) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnBoardTurvo", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.APIKey == "" || onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing Turvo API credentials")
	}

	onboardResponse, err := t.authenticate(ctx, onboardRequest.Username, onboardRequest.Password)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	// Controlling Turvo environment
	onboardResponse.AppID = onboardRequest.AppID

	return onboardResponse, err
}
