package turvo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

func (t *Turvo) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (checkcalls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(t.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	load, err := loadDB.GetLoadByID(ctx, loadID)
	if err != nil {
		return checkcalls, fmt.Errorf("get loadDB failed: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")

	var response GetCheckCallResp
	endPoint := fmt.Sprintf("v1/shipments/%s", load.ExternalTMSID)

	err = t.getWithAuth(ctx, endPoint, queryParams, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return checkcalls, err
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return checkcalls, fmt.Errorf("%s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	checkcalls = []models.CheckCall{t.turvoShipmentToCheckCall(response, loadID, freightTrackingID)}

	return checkcalls, nil
}

func (t *Turvo) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	checkcall models.CheckCall,
) (err error) {

	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.SafeIntAttribute("load_id", load.ID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := t.toTurvoCheckCall(checkcall, load)
	if err != nil {
		return fmt.Errorf("error creating turvo check call body: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")
	endPoint := fmt.Sprintf("v1/shipments/status/%s", load.ExternalTMSID)

	var response LoadResponse
	err = t.putWithAuth(ctx, endPoint, queryParams, reqBody, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return fmt.Errorf("posting check call failed: %w", err)
	}

	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return fmt.Errorf("posting check call failed: %s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	return nil
}

func (t *Turvo) toTurvoCheckCall(cc models.CheckCall, load *models.Load) (result UpdateCheckCallsRequest, err error) {
	shipmentID, err := strconv.Atoi(load.ExternalTMSID)
	if err != nil {
		return result, fmt.Errorf("error converting shipmentID to int: %w", err)
	}
	result.ID = shipmentID

	code := getCodeFromStatus(cc.Status)
	if code <= 0 {
		return result, fmt.Errorf("failed to get code for status %s", cc.Status)
	}

	// Turvo automatically geocodes lat & long based on city, state
	result.Status.Location.City = strings.Join([]string{cc.City, cc.State}, ", ")

	result.Status.Code.Key = fmt.Sprint(code)
	result.Status.Code.Value = cc.Status
	// NOTE: If API is called at 2 pm but the check call was at 1 pm, then Turvo displays 1 pm as the "reported date",
	//  but the timeline tab is in order of *when* the API request was made, which in this case is 2 pm.
	result.Status.StatusDate.Date = cc.DateTime.Time
	result.Status.StatusDate.Timezone = cc.Timezone

	result.Status.Notes = cc.Notes
	result.Status.Timezone = cc.Timezone

	// globalShipLocationID is required for updates where the code key is one of the following 4:
	message := strings.ToLower(cc.Status)
	switch message {
	case "at pickup", "picked up":
		pickupID, err := strconv.Atoi(load.Pickup.ExternalTMSID)
		if err != nil {
			return result, fmt.Errorf("error converting pickup ID to int: %w", err)
		}

		result.Status.GlobalShipLocationID = pickupID

	case "at delivery", "delivered":
		deliveryID, err := strconv.Atoi(load.Consignee.ExternalTMSID)
		if err != nil {
			return result, fmt.Errorf("error converting consignee ID to int: %w", err)
		}

		result.Status.GlobalShipLocationID = deliveryID

	}

	return result, nil
}

func (t *Turvo) turvoShipmentToCheckCall(
	response GetCheckCallResp,
	loadID uint,
	freightTrackingID string,
) models.CheckCall {

	status := response.Details.Status

	// City = "City, St" e.g. "Lawton, OK"
	cityState := strings.Split(status.Location.City, ",")

	var city, state string
	if len(cityState) > 0 {
		city = strings.TrimSpace(cityState[0])
	}
	if len(cityState) > 1 {
		state = strings.TrimSpace(cityState[1])
	}

	return models.CheckCall{
		LoadID:            loadID,
		FreightTrackingID: freightTrackingID,
		Status:            status.Code.Value,
		DateTime: models.NullTime{
			Time:  status.StatusDate.Date,
			Valid: true,
		},
		Lat:   status.Location.Lat,
		Lon:   status.Location.Lon,
		City:  city,
		State: state,
		Notes: status.Notes,
	}
}

func getCodeFromStatus(status string) int {
	switch strings.ToLower(status) {
	case "quote active":
		return 2100
	case "tendered":
		return 2101
	case "covered":
		return 2102
	case "dispatched":
		return 2103
	case "at pickup":
		return 2104
	case "en route":
		return 2105
	case "at delivery":
		return 2106
	case "route complete":
		return 2116
	case "delivered":
		return 2107
	case "ready for billing":
		return 2108
	case "processing":
		return 2109
	case "carrier paid":
		return 2110
	case "customer paid":
		return 2111
	case "completed":
		return 2112
	case "canceled":
		return 2113
	case "tender - offered":
		return 2117
	case "tender - accepted":
		return 2118
	case "tender - rejected":
		return 2119
	case "quote inactive":
		return 2114
	case "picked up":
		return 2115
	default:
		return -1 // Unknown status
	}
}

var StatusKeyEnums = []int{
	2100,
	2101,
	2102,
	2103,
	2104,
	2105,
	2106,
	2116,
	2107,
	2108,
	2109,
	2110,
	2111,
	2112,
	2113,
	2117,
	2118,
	2119,
	2114,
	2115,
}

var StatusMessageEnums = []string{
	"Quote active",
	"Tendered",
	"Covered",
	"Dispatched",
	"At pickup",
	"En route",
	"At delivery",
	"Route Complete",
	"Delivered",
	"Ready for billing",
	"Processing",
	"Carrier paid",
	"Customer paid",
	"Completed",
	"Canceled",
	"Tender - offered",
	"Tender - accepted",
	"Tender - rejected",
	"Quote inactive",
	"Picked up",
}
