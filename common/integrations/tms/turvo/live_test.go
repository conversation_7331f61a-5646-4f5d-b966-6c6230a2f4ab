package turvo

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/util/crypto"
)

type envVars struct {
	Username      string `envconfig:"TURVO_USERNAME" required:"true"`
	Password      string `envconfig:"TURVO_PASS" required:"true"`
	APIKey        string `envconfig:"TURVO_API_KEY" required:"true"`
	CustomID      string `envconfig:"CUSTOM_ID" default:"31408-01721"`
	Update        bool   `envconfig:"UPDATE" default:"false"`
	PostCheckCall bool   `envconfig:"POST_CHECK_CALL" default:"false"`
	RedisURL      string `envconfig:"REDIS_URL" required:"true"`
}

var (
	testEnv envVars

	// Only this load may be used for testing updates
	// IMPORTANT: DO NOT MODIFY REAL CUSTOMER DATA.
	testLoads = map[string]bool{
		"31408-01721": true,
	}
)

// ---------------------------------------------------------------------------------------------------------
//
// Tests all Turvo functions. Subtests must be run sequentially and not in parallel.
//
// ---------------------------------------------------------------------------------------------------------
func TestLiveTurvo(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveTurvo: run with LIVE_TEST=true to enable")
		return
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	err := loadEnv(ctx)
	require.NoError(t, err)

	log.Info(ctx, "parsed env", zap.Any("env", testEnv))

	ctx = log.With(ctx, zap.String("load", testEnv.CustomID))

	tmsDBUpdateFunc = func(ctx context.Context, _ *models.Integration) error {
		log.Debug(ctx, "mock TMS update")

		return nil
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, testEnv.Password, nil)
	require.NoError(t, err)

	tms := models.Integration{
		Name:              models.Turvo,
		Type:              models.TMS,
		ServiceID:         1,
		Username:          testEnv.Username,
		EncryptedPassword: []byte(encryptedPassword),
		APIKey:            testEnv.APIKey,
	}

	if !testEnv.Update && !testEnv.PostCheckCall {
		log.Info(ctx, "running in read-only mode")
	} else if !testLoads[testEnv.CustomID] {
		log.Infof(ctx, "running in read-only mode for production load %s", testEnv.CustomID)
	}

	client := New(ctx, tms)
	var load models.Load

	t.Run("GetToken", func(t *testing.T) {
		startTime := time.Now()

		tokenData, err := client.getToken(ctx, tms.Username, testEnv.Password)
		require.NoError(t, err)
		assert.NotEmpty(t, tokenData.AccessToken)
		assert.Equal(t, "K00ED6qc", tokenData.TenantRef)

		log.Info(ctx, "GetToken completed",
			zap.Any("load", load), zap.Duration("duration", time.Since(startTime)))

		tms.AccessToken = tokenData.AccessToken
		tms.Tenant = tokenData.TenantRef
	})

	// Don't continue with remaining tests if GetToken failed
	require.NotEmpty(t, tms.AccessToken)

	t.Run("GetLoadWithTokenRefresh", func(t *testing.T) {
		log.Info(ctx, "calling GetLoad for test CustomID")

		// Force token refresh during GetLoad
		tms.UpdatedAt = time.Now().Add(-20 * time.Hour)
		startTime := time.Now()
		var err error

		load, _, err = client.GetLoad(ctx, testEnv.CustomID)
		require.NoError(t, err)

		log.Info(ctx, "GetLoadGetLoadWithTokenRefresh completed",
			zap.Any("load", load), zap.Duration("duration", time.Since(startTime)))

	})

	if testEnv.PostCheckCall && testLoads[testEnv.CustomID] {
		t.Run("PostCheckCall", func(t *testing.T) {
			log.Info(ctx, "calling PostCheckCall for test CustomID")

			checkCallRequest := models.CheckCall{
				LoadID:            0,
				FreightTrackingID: testEnv.CustomID,
				City:              "Boston",
				State:             "MA",
				Status:            "At pickup", // StatusMessageEnums in ./checkcalls.go
				DateTime: models.NullTime{
					Time:  time.Now(),
					Valid: true,
				},
				Timezone: "America/New_York",
				Notes:    "check call from live test",
				Lat:      0, // Turvo automatically geocodes city, state to get lat, long
				Lon:      0,
			}

			startTime := time.Now()
			err := client.PostCheckCall(ctx, nil, checkCallRequest)
			assert.NoError(t, err)
			log.Info(ctx, "PostCheckCall completed", zap.Duration("duration", time.Since(startTime)))
		})

	}

	// WARNING: never call UpdateLoad for real data
	// NOTE: that because this is a PUT request, empty values will reset the fields.
	// if testEnv.Update && testLoads[testEnv.CustomID] {
	// 	t.Run("UpdateLoad", func(t *testing.T) {
	// 		log.Info(ctx, "calling UpdateLoad for test CustomID")
	// 		if _, _, err := client.UpdateLoad(ctx, testEnv.CustomID, sampleUpdatedLoad); err != nil {
	// 			log.Error(ctx, "UpdateLoad failed", zap.Error(err))
	// 		} else {
	// 			log.Info(ctx, "UpdateLoad completed", zap.Duration("duration", time.Since(startTime)))
	// 		}
	// 	})

	// }

	t.Run("GetLocations", func(t *testing.T) {
		startTime := time.Now()
		locations, err := client.GetLocations(ctx)
		require.NoError(t, err)
		log.Info(ctx, "GetLocations completed", zap.Duration("duration", time.Since(startTime)))
		log.Info(ctx, "GetLocations length", zap.Int("length", len(locations)))
	})

	t.Run("GetLocationsAppEndpoint", func(t *testing.T) {
		startTime := time.Now()
		locations, err := client.GetLocations(ctx, models.WithChangeHostName(true))
		require.NoError(t, err)
		log.Info(ctx, "GetLocationsAppEndpoint completed", zap.Duration("duration", time.Since(startTime)))
		log.Info(ctx, "GetLocationsAppEndpoint length", zap.Int("length", len(locations)))
	})

	t.Run("GetCustomers", func(t *testing.T) {
		startTime := time.Now()
		customers, err := client.GetCustomers(ctx)
		require.NoError(t, err)
		log.Info(ctx, "GetCustomers completed", zap.Duration("duration", time.Since(startTime)))
		log.Info(ctx, "GetCustomers length", zap.Int("length", len(customers)))
	})
}

// TestGetLocations verifies the ability to retrieve location data from the Turvo API.
// It requires LIVE_TEST=true environment variable to run.
//
// The test runs against the dev environment by default but can be switched to prod
// by setting the Turvo host url to the prod host.
// To run test: go test -v ./common/integrations/tms/turvo -run ^TestGetLocations$
func TestGetLocations(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestGetLocationsSandbox: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	// Fill in with real values while testing.
	// NEVER commit with real values.
	client := setUpTurvo(ctx, testEnv.Username, testEnv.Password, testEnv.APIKey)
	require.NotNil(t, client)

	mockRDB, mockRedis := redismock.NewClientMock()
	originalRDB := redis.RDB
	redis.RDB = mockRDB
	defer func() {
		redis.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Set up mock Redis expectations
	stateKey := fmt.Sprintf("integration-id-%d-%s", client.tms.ID, redis.LocationJob)
	mockRedis.ExpectGet(stateKey).RedisNil() // Simulate no previous state
	mockRedis.ExpectDel(stateKey).SetVal(1)

	locations, err := client.GetLocations(ctx)
	require.NoError(t, err)
	require.NotEmpty(t, locations)

	require.NoError(t, mockRedis.ExpectationsWereMet())
}

// TestGetCustomers verifies the ability to retrieve customer data from the Turvo API.
// It requires LIVE_TEST=true environment variable to run.
//
// The test runs against the dev environment by default but can be switched to prod
// by setting the Turvo host url to the prod host.

func TestGetCustomers(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestGetCustomersSandbox: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	// Fill in with real values while testing.
	// NEVER commit with real values.
	client := setUpTurvo(ctx, testEnv.Username, testEnv.Password, testEnv.APIKey)
	require.NotNil(t, client)

	mockRDB, mockRedis := redismock.NewClientMock()
	originalRDB := redis.RDB
	redis.RDB = mockRDB
	defer func() {
		redis.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Set up mock Redis expectations
	stateKey := fmt.Sprintf("integration-id-%d-%s", client.tms.ID, redis.CustomerJob)
	mockRedis.ExpectGet(stateKey).RedisNil() // Simulate no previous state
	mockRedis.ExpectDel(stateKey).SetVal(1)

	customers, err := client.GetCustomers(ctx)
	require.NoError(t, err)
	require.NotEmpty(t, customers)

	require.NoError(t, mockRedis.ExpectationsWereMet())
}

// setUp creates a new Turvo client for testing. It handles authentication and token management.
// To toggle between environments, set the Turvo host url to the prod/dev host.
//
// Note: When debugging pagination issues with locations, add logging in the for loop of
// the GetLocations function to track query parameters and list lengths.
func setUpTurvo(ctx context.Context, username, password, apikey string) *Turvo {
	err := loadEnv(ctx)
	if err != nil {
		return nil
	}

	// you will need to go to the secrets manager and comment out the environment conditional in
	// order to use the hardcoded key - or set the FORCE_GEN_AES_KEY environment variable to true
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return nil
	}

	tms := models.Integration{
		Name:              models.Turvo,
		Type:              models.TMS,
		ServiceID:         1,
		Username:          username,
		EncryptedPassword: []byte(encryptedPassword),
		APIKey:            apikey,
	}

	tmsDBUpdateFunc = func(ctx context.Context, _ *models.Integration) error {
		log.Debug(ctx, "mock TMS update")

		return nil
	}

	// to toggle between dev and prod, set the Turvo host url to the prod/dev host
	client := New(ctx, tms)

	tokenData, err := client.getToken(ctx, tms.Username, password)
	if err != nil {
		return nil
	}

	tms.AccessToken = tokenData.AccessToken
	tms.Tenant = tokenData.TenantRef

	// Don't continue with tests if GetToken failed
	if tms.AccessToken == "" {
		return nil
	}

	return client
}

// loadEnv loads the environment variables from the .env file.
func loadEnv(ctx context.Context) error {
	if err := godotenv.Load(); err != nil {
		log.Warn(ctx, "unable to load .env file", zap.Error(err))
	}

	if err := envconfig.Process("", &testEnv); err != nil {
		return fmt.Errorf("failed to parse env vars: %w", err)
	}

	return nil
}
