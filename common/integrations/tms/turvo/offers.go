package turvo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/common/util/s3backup"
)

const (
	createOfferPath = "/v1/offers"
)

// CreateOffer creates a new offer in Turvo
func (t *Turvo) CreateOffer(ctx context.Context, carrierQuote models.CarrierQuote) error {
	ctx, span := otel.StartSpan(ctx, "turvo.CreateOffer", otel.IntegrationAttrs(t.tms))
	defer span.End(nil)

	// Convert carrier quote to Turvo offer request
	offerReq, err := t.carrierQuoteToTurvoOffer(ctx, carrierQuote)
	if err != nil {
		return fmt.Errorf("failed to convert carrier quote to Turvo offer: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")

	var response OfferResponse
	err = t.postWithAuth(ctx, createOfferPath, queryParams, offerReq, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("failed to create offer: %w", err)
	}

	if response.Status == "Error" {
		return fmt.Errorf("failed to create offer: %s", response.Details.ErrorMessage)
	}

	return nil
}

// carrierQuoteToTurvoOffer converts a carrier quote to a Turvo offer request
func (t *Turvo) carrierQuoteToTurvoOffer(
	ctx context.Context,
	carrierQuote models.CarrierQuote,
) (*OfferRequest, error) {
	// Get carrier details
	carrier, err := t.getCarrierDetails(ctx, carrierQuote.CarrierLocationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get carrier details: %w", err)
	}

	// Get quote request details
	quoteReq, err := t.getQuoteRequestDetails(ctx, carrierQuote.QuoteRequestID)
	if err != nil {
		return nil, fmt.Errorf("failed to get quote request details: %w", err)
	}

	// Create offer request
	offerReq := &OfferRequest{
		OfferType: "CARRIER_OFFER",
		AccountDetails: AccountDetails{
			// Turvo only supports 32-bit IDs, so we need to mask the ID
			ID: func() int {
				id, err := strconv.Atoi(carrier.ExternalTMSID)
				if err != nil {
					return 0
				}
				return id
			}(),
			Type:      "CARRIER",
			Name:      carrier.Name,
			McNumber:  carrier.ExternalTMSID,
			DotNumber: carrier.DOTNumber,
			Sharing: Sharing{
				IsShareable: true,
			},
		},
		ContextData: ContextData{
			ContextID:   fmt.Sprintf("%d", quoteReq.ID),
			ContextType: "shipment",
			CustomID:    quoteReq.SourceExternalID,
			TransportationType: TransportationType{
				Mode: Mode{
					Key:   "24105", // TL
					Value: "TL",
				},
				Type: Type{
					Key:   "24304", // Any
					Value: "Any",
				},
			},
			Origin: OfferLocation{
				Name: quoteReq.AppliedRequest.PickupLocation.City,
				Address: OfferAddress{
					City: OfferCity{
						Name: quoteReq.AppliedRequest.PickupLocation.City,
					},
					State: OfferState{
						Name: quoteReq.AppliedRequest.PickupLocation.State,
					},
					Country: OfferCountry{
						Name: "United States",
						Code: "US",
					},
				},
			},
			Destination: OfferLocation{
				Name: quoteReq.AppliedRequest.DeliveryLocation.City,
				Address: OfferAddress{
					City: OfferCity{
						Name: quoteReq.AppliedRequest.DeliveryLocation.City,
					},
					State: OfferState{
						Name: quoteReq.AppliedRequest.DeliveryLocation.State,
					},
					Country: OfferCountry{
						Name: "United States",
						Code: "US",
					},
				},
			},
			PickupDate: DateTime{
				Date:     quoteReq.AppliedRequest.PickupDate.Time.Format(time.RFC3339),
				Timezone: "America/Chicago",
				HasTime:  false,
			},
			DeliveryDate: DateTime{
				Date:     quoteReq.AppliedRequest.DeliveryDate.Time.Format(time.RFC3339),
				Timezone: "America/Los_Angeles",
				HasTime:  false,
			},
		},
		Currency: Currency{
			Key:   "1550",
			Value: "USD",
		},
		Uncounterable: false,
		ExpiryDate:    time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		Quotes: []Quote{
			{
				OwnerRequest: OwnerRequest{
					Amount: float64(carrierQuote.SuggestedQuote.TotalCost),
					LineItems: []OfferLineItem{
						{
							Price: float64(carrierQuote.SuggestedQuote.TotalCost),
							Type: LineItemType{
								Key:   "1600",
								Value: "Freight - flat",
							},
							Qty:    1,
							Amount: float64(carrierQuote.SuggestedQuote.TotalCost),
							RateQualifier: RateQualifier{
								Type: Type{
									Key:   "30001",
									Value: "Flat",
								},
							},
						},
					},
				},
			},
		},
		Comments:          carrierQuote.SuggestedQuote.Notes,
		RequestSourceType: "PUBLIC_API",
	}

	return offerReq, nil
}

// getCarrierDetails gets carrier details from Turvo
func (t *Turvo) getCarrierDetails(context.Context, uint) (*models.TMSCarrier, error) {
	// TODO: Implement carrier details retrieval from Turvo
	return nil, fmt.Errorf("not implemented")
}

// getQuoteRequestDetails gets quote request details
func (t *Turvo) getQuoteRequestDetails(context.Context, uint) (*models.QuoteRequest, error) {
	// TODO: Implement quote request details retrieval
	return nil, fmt.Errorf("not implemented")
}
