package api

import (
	"context"
	"os"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func RunServer(ctx context.Context, app *fiber.App, opts ...Option) {
	options := &Options{
		Port: "5000",
	}

	for _, opt := range opts {
		opt(options)
	}

	// set different port if it's an azure function app
	if val, ok := os.LookupEnv("FUNCTIONS_CUSTOMHANDLER_PORT"); ok {
		options.Port = val
	}

	err := app.Listen(":" + options.Port)
	log.Fatal(ctx, "fiber app terminated", zap.Error(err)) //nolint:revive // fatal is fine here, unreachable
	// NOTE: Have all logs flushed before the application exits.
	//
	// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
	// the buffer will not be flushed when the application exits.
	log.Flush(ctx)

}
