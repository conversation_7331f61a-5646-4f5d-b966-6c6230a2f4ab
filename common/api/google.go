package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/otel"
)

var oauthConfGl *oauth2.Config

func InitializeOAuthGoogle(clientID, secret string) {
	oauthConfGl = &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: secret,
		Endpoint:     google.Endpoint,
		RedirectURL:  "postmessage",
	}
}

type GoogleAuthCodeRequest struct {
	Code             string
	HostedDomain     string `json:"hd"`
	State            string
	Scope            string
	ErrorDescription string
}

type GoogleAuthResponse struct {
	AccessToken  string
	RefreshToken string
	ExpTime      time.Time
	UserInfo     GoogleUserInfoResponse
}

type GoogleUserInfoResponse struct {
	LocalID       string `json:"localId"`
	Email         string `json:"email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"` // Picture URL
	VerifiedEmail bool   `json:"verified_email"`
}

func CallBackFromGoogle(ctx context.Context, r GoogleAuthCodeRequest) (result GoogleAuthResponse, err error) {
	log.Info(ctx, "Callback-gl..")

	state := r.State
	log.Info(ctx, state)
	code := r.Code
	log.Info(ctx, code)

	if code == "" {
		log.WarnNoSentry(ctx, "Code not found, error:", zap.Any("error", r.ErrorDescription))

		if r.ErrorDescription == "user_denied" {
			log.WarnNoSentry(ctx, "User has denied Permission..")
		}

		return GoogleAuthResponse{}, errors.New("auth failed")
	}

	token, err := oauthConfGl.Exchange(ctx, code)
	if err != nil {
		log.ErrorNoSentry(ctx, "oauthConfGl.Exchange() failed with "+err.Error()+"\n")

		return GoogleAuthResponse{}, errors.New("exchange failed")
	}

	log.Info(ctx, "TOKEN>> Expiration Time>> "+token.Expiry.String())

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		"https://www.googleapis.com/oauth2/v2/userinfo?access_token="+token.AccessToken,
		nil,
	)
	if err != nil {
		log.ErrorNoSentry(ctx, "Get: "+err.Error()+"\n")

		return GoogleAuthResponse{}, errors.New("redirect failed")
	}

	req.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return GoogleAuthResponse{}, fmt.Errorf("failed to send GET request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return GoogleAuthResponse{}, fmt.Errorf("failed to read response body: %w", err)
	}

	var userInfo GoogleUserInfoResponse
	if err = json.Unmarshal(body, &userInfo); err != nil {
		log.ErrorNoSentry(ctx, "ReadAll: "+err.Error()+"\n")
		// http.Redirect(w, r, "/", http.StatusTemporaryRedirect)
		return GoogleAuthResponse{}, errors.New("reading failed")
	}

	userInfo.Email = strings.ToLower(userInfo.Email)

	return GoogleAuthResponse{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		ExpTime:      token.Expiry,
		UserInfo:     userInfo,
	}, nil
}
