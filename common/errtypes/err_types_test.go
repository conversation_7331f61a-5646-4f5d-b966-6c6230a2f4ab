package errtypes

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestUserFacingError(t *testing.T) {
	t.Run("basic error wrapping", func(t *testing.T) {
		baseErr := errors.New("something went wrong")
		ufErr := NewUserFacingError(baseErr)

		assert.Equal(t, "Something went wrong", ufErr.Error())
		assert.ErrorIs(t, ufErr, baseErr)
	})

	t.Run("http response error with user facing string", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      400,
			ResponseBody:    []byte("Invalid load data"),
		}

		ufErr := NewUserFacingError(httpErr)
		expected := "<PERSON><PERSON><PERSON> returned an error: Invalid load data"
		assert.Equal(t, expected, ufErr.Error())
	})

	t.Run("wrap new user facing error", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      400,
			ResponseBody:    []byte("duplicate location name"),
		}

		wrappedErr := WrapNewUserFacingError("error creating pickup location", httpErr)
		expected := "Mcleod returned an error: error creating pickup location: duplicate location name"
		assert.Equal(t, expected, wrappedErr.Error())
	})

	t.Run("wrap plain error", func(t *testing.T) {
		baseErr := errors.New("invalid data")
		wrappedErr := WrapNewUserFacingError("failed to process", baseErr)
		assert.Equal(t, "Failed to process: invalid data", wrappedErr.Error())
	})

	t.Run("http response error with empty body", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      404,
			ResponseBody:    []byte{},
		}

		ufErr := NewUserFacingError(httpErr)
		expected := "Mcleod returned an error: Not Found"
		assert.Equal(t, expected, ufErr.Error())
	})

	t.Run("multiple wrapping levels", func(t *testing.T) {
		baseErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			ResponseBody:    []byte("invalid credentials"),
		}

		wrappedOnce := WrapNewUserFacingError("auth failed", baseErr)
		wrappedTwice := WrapNewUserFacingError("operation failed", wrappedOnce)

		expected := "Mcleod returned an error: operation failed: auth failed: invalid credentials"
		assert.Equal(t, expected, wrappedTwice.Error())
	})
}
