// Package log wraps zap.Logger with support for context and Sentry.
package log

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime"
	"syscall"

	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/uptrace/opentelemetry-go-extra/otelzap"
	"go.opentelemetry.io/otel/attribute"
	oteltrace "go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// New returns context with a new zap logger.
func New(ctx context.Context, logCfg Config, level zapcore.Level, baseFields ...zap.Field) context.Context {
	// If we're running in a Lambda function, add the request ID to the base fields
	if lc, ok := lambdacontext.FromContext(ctx); ok {
		lambdaField := zap.String("lambdaRequestId", lc.AwsRequestID)
		baseFields = append(baseFields, lambdaField)

		span := oteltrace.SpanFromContext(ctx)
		if span != nil && span.IsRecording() {
			addFieldAsAttribute(span, lambdaField)

		}
	}

	return addToContext(ctx, newCoreLogger(logCfg, level, baseFields...))
}

// BaseFields returns the set of base fields currently in the logger context.
func BaseFields(ctx context.Context) []zap.Field {
	return fromContext(ctx).baseFields
}

func ZapLogger(ctx context.Context) *otelzap.Logger {
	return fromContext(ctx).zlog
}

// With creates a new logger with additional base fields, and adds the fields as attributes to the current span.
func With(ctx context.Context, fields ...zap.Field) context.Context {
	span := oteltrace.SpanFromContext(ctx)
	if span != nil && span.IsRecording() {
		for _, field := range fields {
			addFieldAsAttribute(span, field)
		}
	}

	return addToContext(ctx, fromContext(ctx).With(fields...))
}

func Debug(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.DebugLevel, false, msg, fields...)
}

// Debugf logs a formatted informational message with optional additional fields.
func Debugf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.DebugLevel, false, formattedMsg, fields...)
}

func Info(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.InfoLevel, false, msg, fields...)
}

// Infof logs a formatted informational message with optional additional fields.
func Infof(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.InfoLevel, false, formattedMsg, fields...)
}

func Warn(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.WarnLevel, true, msg, fields...)
}

// Warnf logs a formatted warn message with optional additional fields.
func Warnf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.WarnLevel, false, formattedMsg, fields...)
}

func WarnNoSentry(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.WarnLevel, false, msg, fields...)
}

func Error(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.ErrorLevel, true, msg, fields...)
}

// Errorf logs a formatted warn message with optional additional fields.
func Errorf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.WarnLevel, true, formattedMsg, fields...)
}

func ErrorNoSentry(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.ErrorLevel, false, msg, fields...)
}

// DPanic panics if the logger is in development mode.
// This is useful for catching errors that are recoverable, but shouldn't ever happen.
func DPanic(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.DPanicLevel, true, msg, fields...)
}

func Panic(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.PanicLevel, true, msg, fields...)
}

func Fatal(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.FatalLevel, true, msg, fields...)
}

func Flush(ctx context.Context) {
	core := fromContext(ctx)
	defer func() {
		// Wrapper around core.zlog.Sync() that ignores EINVAL errors.
		// See: https://github.com/uber-go/zap/issues/1093#issuecomment-1120667285
		if syncErr := core.zlog.Sync(); syncErr != nil {
			if !errors.Is(syncErr, syscall.EINVAL) && !errors.Is(syncErr, syscall.ENOTTY) {
				Warn(ctx, "Error flushing buffered log entries", zap.Error(syncErr))
			}
		}
	}()
}

func log(ctx context.Context, level zapcore.Level, withSentry bool, msg string, fields ...zap.Field) {
	core := fromContext(ctx)

	if level < core.zlog.Level() {
		// optimization: if the logger is not enabled at this level, stop now
		return
	}

	// The application code is two stack frames above this one
	// E.g. main.go > zap.go (log.Error) > zap.go (this function)
	caller := zapcore.NewEntryCaller(runtime.Caller(2))

	allFields := append(
		[]zap.Field{zap.String("caller", caller.TrimmedPath())},
		core.Merge(fields...)...,
	)

	if withSentry {
		sentryCapture(ctx, level, msg, allFields...)
	}

	switch level {
	case zapcore.DebugLevel:
		core.zlog.DebugContext(ctx, msg, allFields...)
	case zapcore.InfoLevel:
		core.zlog.InfoContext(ctx, msg, allFields...)
	case zapcore.WarnLevel:
		core.zlog.WarnContext(ctx, msg, allFields...)
	case zapcore.ErrorLevel:
		core.zlog.ErrorContext(ctx, msg, allFields...)
	case zapcore.DPanicLevel:
		core.zlog.DPanicContext(ctx, msg, allFields...)
	case zapcore.PanicLevel:
		core.zlog.PanicContext(ctx, msg, allFields...)
	case zapcore.FatalLevel:
		core.zlog.FatalContext(ctx, msg, allFields...)
	default:
		panic("invalid zap level: " + level.String())
	}
}

// addFieldAsAttribute adds a Zap field as a trace attribute.
func addFieldAsAttribute(span oteltrace.Span, field zap.Field) {
	switch field.Type {
	case zapcore.StringType:
		span.SetAttributes(attribute.String(field.Key, field.String))

	case zapcore.BoolType:
		span.SetAttributes(attribute.Bool(field.Key, field.Integer == 1))

	case zapcore.Int64Type, zapcore.Int32Type, zapcore.Int16Type, zapcore.Int8Type, zapcore.Uint64Type,
		zapcore.Uint32Type, zapcore.Uint16Type, zapcore.Uint8Type:

		span.SetAttributes(attribute.Int(field.Key, int(field.Integer)))

	case zapcore.Float64Type, zapcore.Float32Type:
		span.SetAttributes(attribute.Float64(field.Key, field.Interface.(float64)))

	case zapcore.DurationType:
		span.SetAttributes(attribute.Int64(field.Key, field.Integer))

	default:
		v, err := json.Marshal(field.Interface)
		if err == nil {
			span.SetAttributes(attribute.String(field.Key, string(v)))
		}
	}
}
