# Zap Logger
We use [zap](https://pkg.go.dev/go.uber.org/zap) for fast, customizable, structured, leveled logging.

This package wraps the raw `*zap.Logger` with context and Sentry integration: messages at WARN level or higher
are sent to Sentry automatically.

Complete example:

```go
package main

import (
	"context"
	"errors"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
)

func main() {
	os.Setenv("APP_ENV", "dev")
	os.Setenv("DEBUG", "false")
	os.Setenv("SENTRY_DSN", "YOUR_DSN_HERE") // replace with your own value

	sentry.Initialize()
	ctx := context.Background()
	defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)

	ctx = log.NewFromEnv(ctx)

	// debug logs only show up if DEBUG=1 or DEBUG=true
	log.Debug(ctx, "debug msg")

	// instead of % formatters, add strongly typed log fields
	log.Info(ctx, "info msg", zap.Time("t", time.Now()), zap.Int("answerToLife", 42))

	// warnings are logged and sent to Sentry
	log.Warn(ctx, "warn msg", zap.Strings("users", []string{"austin", "boogs", "kayky", "sophie"}))

	// errors are logged and sent to Sentry
	log.Error(ctx, "err msg", zap.Error(errors.New("something went wrong")))

	// warnings and errors can also be logged without the Sentry integration
	log.WarnNoSentry(ctx, "warn without sentry")
	log.ErrorNoSentry(ctx, "error without sentry")

	// add base fields to be included in every subsequent log message
	ctx = log.With(ctx, zap.String("base", "field"))

	// use zap.Any for objects like structs and maps
	log.Warn(ctx, "warn 2", zap.Any("obj", map[string]int{"a": 1, "b": 2, "c": 3}))
}
```

Dev output:
```
16:54:36	INFO	info msg	{"caller": "logtest/main.go:30", "t": "16:54:36", "answerToLife": 42}
16:54:36	WARN	warn msg	{"caller": "logtest/main.go:33", "users": ["austin", "boogs", "kayky", "sophie"]}
16:54:36	ERROR	err msg	{"caller": "logtest/main.go:36", "error": "something went wrong"}
16:54:36	WARN	warn without sentry	{"caller": "logtest/main.go:39"}
16:54:36	ERROR	error without sentry	{"caller": "logtest/main.go:40"}
16:54:36	WARN	warn 2	{"caller": "logtest/main.go:46", "base": "field", "obj": {"a":1,"b":2,"c":3}}
```

Prod output:
```
{"level":"info","ts":1701986056.9189389,"msg":"info msg","caller":"logtest/main.go:30","t":1701986056.918924,"answerToLife":42}
{"level":"warn","ts":1701986056.919347,"msg":"warn msg","caller":"logtest/main.go:33","users":["austin","boogs","kayky","sophie"]}
{"level":"error","ts":1701986056.919382,"msg":"err msg","caller":"logtest/main.go:36","error":"something went wrong"}
{"level":"warn","ts":1701986056.919389,"msg":"warn without sentry","caller":"logtest/main.go:39"}
{"level":"error","ts":1701986056.919395,"msg":"error without sentry","caller":"logtest/main.go:40"}
{"level":"warn","ts":1701986056.919429,"msg":"warn 2","caller":"logtest/main.go:46","base":"field","obj":{"a":1,"b":2,"c":3}}
```
