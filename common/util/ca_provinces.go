package util

import (
	"fmt"
	"strings"
)

// Map of Canadian province and territory abbreviations to full names
var provinceAbbreviations = map[string]string{
	"AB": "Alberta",
	"BC": "British Columbia",
	"MB": "Manitoba",
	"NB": "New Brunswick",
	"NL": "Newfoundland and Labrador",
	"NS": "Nova Scotia",
	"NT": "Northwest Territories",
	"NU": "Nunavut",
	"ON": "Ontario",
	"PE": "Prince Edward Island",
	"QC": "Quebec",
	"SK": "Saskatchewan",
	"YT": "Yukon",
}

// Function that maps province abbreviation to full name
func GetCanadaProvinceFullName(abbreviation string) (string, error) {
	fullName, exists := provinceAbbreviations[strings.ToUpper(abbreviation)]
	if !exists {
		return "", fmt.Errorf("unknown Province")
	}
	return fullName, nil
}

// Function that maps full province name to abbreviation
func GetCanadaProvinceAbbreviation(fullName string) (string, error) {
	for abbreviation, name := range provinceAbbreviations {
		if strings.EqualFold(name, fullName) {
			return abbreviation, nil
		}
	}
	return "", fmt.<PERSON>("unknown Province")
}
