package s3backup

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"regexp"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/util"
	"github.com/drumkitai/drumkit/common/util/jsoncfg"
)

const (
	// For public-facing assets like user-uploaded images
	DrumkitPublicBucket = "drumkit-public"
)

// Archiver uploads JSON email backups among other things to an S3 bucket
type Archiver interface {
	Gmail(context.Context, *models.IngestedEmail, string) (string, error)
	Outlook(context.Context, *models.IngestedEmail, string) (string, error)
	Attachment(
		ctx context.Context,
		mailClient models.IntegrationName,
		account, msgID, attachmentName string,
		data []byte) (string, error)

	// TODO: Deprecate all of these individual integration funcs and use universal API response upload func
	//nolint:lll
	LaneRatePrediction(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	//nolint:lll
	NetworkLaneRatePrediction(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	QuickQuote(ctx context.Context, account, laneRatePredictionID string, apiResponse util.APIResponse) (string, error)

	// Truckstop
	PostedRate(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	PostedTrendline(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	BookedRate(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	BookedTrendline(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)
	BookedHistory(ctx context.Context, serviceID uint, userEmail string, apiResponse util.APIResponse) (string, error)

	TMSResponse(ctx context.Context, tms models.Integration, dataType DataType, apiResponse util.APIResponse,
	) (string, error)

	// For public-facing assets like user-uploaded images
	PublicUserAsset(
		ctx context.Context,
		user models.User,
		env, subfolder, fileExtension string,
		data []byte,
	) (string, error)
}

type S3API interface {
	Upload(context.Context, *s3.PutObjectInput, ...func(*manager.Uploader)) (*manager.UploadOutput, error)
}

// S3Client satisfies the Archiver interface
type S3Client struct {
	manager    S3API
	bucketName string
}

func New(ctx context.Context, bucketName string) (Archiver, error) {
	if bucketName == "" {
		return nil, errors.New("missing bucket name")
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"), config.WithRetryMaxAttempts(7))
	if err != nil {
		return nil, err
	}

	return &S3Client{bucketName: bucketName, manager: manager.NewUploader(s3.NewFromConfig(cfg))}, nil
}

func (client *S3Client) Gmail(ctx context.Context, msg *models.IngestedEmail, account string) (string, error) {
	body, err := jsoncfg.HumanReadableConfig.Marshal(msg)
	if err != nil {
		return "", fmt.Errorf("json marshal of %s failed: %w", msg.ExternalID, err)
	}

	return client.upload(ctx, body, fmt.Sprintf("gmail/%s/%s.json", account, msg.ExternalID))
}

func (client *S3Client) Outlook(ctx context.Context, msg *models.IngestedEmail, account string) (string, error) {
	body, err := jsoncfg.HumanReadableConfig.Marshal(msg)
	if err != nil {
		return "", fmt.Errorf("json marshal of %s failed: %w", msg.ExternalID, err)
	}

	return client.upload(ctx, body, fmt.Sprintf("outlook/%s/%s.json", account, msg.ExternalID))
}

func (client *S3Client) Attachment(
	ctx context.Context, mailClient models.IntegrationName, account, msgID, attachmentName string, data []byte,
) (string, error) {
	key := fmt.Sprintf("%s/%s/%s/%s", string(mailClient), account, msgID, SanitizeFileName(attachmentName))
	return client.upload(ctx, data, key)
}

func (client *S3Client) LaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of lane rate prediction %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/greenscreens/lane-rate-prediction/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) NetworkLaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of network rate rate prediction %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/greenscreens/network-lane-rate-prediction/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) QuickQuote(
	ctx context.Context,
	account,
	laneRatePredictionID string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of quick quote %s failed: %w", account, err)
	}

	layout := "2006-01-02"
	timestamp := time.Now().Format(layout)

	return client.upload(ctx, body, fmt.Sprintf("greenscreens/%s/%s/status-%d/quick-quote-%s.json",
		account, laneRatePredictionID, apiResp.Status, timestamp))
}

func (client *S3Client) PostedRate(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of posted rate %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/truckstop/posted-rate/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) PostedTrendline(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of posted trendline %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/truckstop/posted-trendline/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) BookedRate(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of booked rate %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/truckstop/booked-rate/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) BookedTrendline(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of booked trendline %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/truckstop/booked-trendline/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) BookedHistory(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	apiResp util.APIResponse,
) (string, error) {

	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResp.Body)
	if err != nil {
		return "", fmt.Errorf("json marshal of Booked History %s failed: %w", userEmail, err)
	}

	timestamp := time.Now().Format(time.RFC3339)

	return client.upload(ctx, body,
		fmt.Sprintf("service-%d/truckstop/booked-history/status-%d/%s-%s.json",
			serviceID, apiResp.Status, timestamp, userEmail))
}

func (client *S3Client) TMSResponse(
	ctx context.Context,
	tms models.Integration,
	dataType DataType,
	apiResponse util.APIResponse,
) (string, error) {
	body, err := jsoncfg.HumanReadableConfig.Marshal(apiResponse.Body)
	if err != nil {
		return "", err
	}

	timestamp := time.Now().Format(time.RFC3339Nano)

	// todo : need to confirm key
	return client.upload(ctx, body, fmt.Sprintf("service-%d/%s-%d/%s/%s/status-%d/%s.txt",
		tms.ServiceID, tms.Name, tms.ID, dataType, apiResponse.Method, apiResponse.Status, timestamp))
}

func (client *S3Client) PublicUserAsset(
	ctx context.Context,
	user models.User,
	env, subfolder, fileExtension string,
	data []byte,
) (string, error) {
	var key string
	if subfolder != "" {
		key = fmt.Sprintf("user-assets/%s/%s/%s/%s.%s",
			env, subfolder, user.EmailAddress, uuid.New().String(), fileExtension)
	} else {
		key = fmt.Sprintf("user-assets/%s/%s/%s.%s", env, user.EmailAddress, uuid.New().String(), fileExtension)
	}

	input := &s3.PutObjectInput{Bucket: &client.bucketName, Key: &key, Body: bytes.NewReader(data)}
	result, err := client.manager.Upload(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to upload s3://%s/%s: %w", client.bucketName, key, err)
	}

	log.Info(ctx, "s3 upload finished", zap.String("location", result.Location))

	// Return the public-facing object URL, not S3 console URL
	return result.Location, nil
}

func (client *S3Client) upload(ctx context.Context, body []byte, key string) (string, error) {
	input := &s3.PutObjectInput{Bucket: &client.bucketName, Key: &key, Body: bytes.NewReader(body)}
	result, err := client.manager.Upload(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to upload s3://%s/%s: %w", client.bucketName, key, err)
	}

	log.Info(ctx, "s3 upload finished", zap.String("location", result.Location))

	// Return S3 console URL, not the public-facing object URL
	return fmt.Sprintf("https://s3.console.aws.amazon.com/s3/object/%s?region=us-east-1&prefix=%s",
		client.bucketName, key), nil
}

// sanitizeFileName replaces any characters not allowed in S3 keys with a hyphen
// https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html
func SanitizeFileName(fileName string) string {
	// Allowed characters: alphanumeric, !, -, _, ., *, ', ( )
	// All others will be replaced by a hyphen (-)
	regex := regexp.MustCompile(`[^a-zA-Z0-9!_\-.*'()]`)
	return regex.ReplaceAllString(fileName, "-")
}
