package util

import (
	"errors"
	"regexp"
	"strings"
	"time"
)

func ParseDatetime(timestamp string) (time.Time, error) {
	if layout := rfc3339Layout(timestamp); layout != "" {
		return time.Parse(layout, timestamp)
	}

	spaceIndex := strings.LastIndex(timestamp, " ")

	if timestamp == "" {
		return time.Time{}, errors.New("timestamp is an empty string")
	}

	// Examples: "Apr-04-2023, 02:51 PM EDT" or "Apr-04-2023, 06:51 PM UTC"
	//
	// This format is only used by Azuga, and they usually write the trailing timezone as "UTC".
	// But just to be safe, if the timezone is not "UTC" we explicitly match to US timezones.
	if strings.Contains(timestamp, ",") {
		loc, err := Timezone(timestamp[spaceIndex+1:])
		if err != nil {
			return time.Time{}, err
		}

		return time.ParseInLocation("Jan-02-2006, 03:04 PM MST", timestamp, loc)
	}

	switch strings.Index(timestamp, "/") {
	case -1:
		// Zonar: "2007-05-23 14:14 PDT"
		loc, err := Timezone(timestamp[spaceIndex+1:])
		if err != nil {
			return time.Time{}, err
		}

		return time.ParseInLocation("2006-01-02 15:04 MST", timestamp, loc)

	case 2:
		// Webfleet: "31/03/2022" or "31/03/2022 11:15:59"
		// NOTE: if a future TSP needs the more traditional MM/DD/YYYY format, it will be impossible to distinguish
		// these cases from the timestamp alone.
		if spaceIndex == -1 {
			return time.Parse("02/01/2006", timestamp)
		}
		return time.Parse("02/01/2006 15:04:05", timestamp)

	default:
		// Example: "2018-01-16T17:03:53-06 America/Chicago"
		//
		// Because the UTC offset is already included, we don't bother parsing the tz name
		return time.Parse("2006-01-02T15:04:05-07", timestamp[:spaceIndex])
	}
}

func rfc3339Layout(timestamp string) string {
	groups := rfc3339Pattern.FindStringSubmatch(timestamp)
	if len(groups) < 7 {
		return ""
	}

	// groups[0] is the entire timestamp string

	var sb strings.Builder
	sb.WriteString(time.DateOnly) // groups[1] is the date, which is always required

	if t := groups[2]; t != "" {
		sb.WriteString(t) // date-time separator: "T" or " "
	}

	if minute := groups[3]; minute != "" {
		sb.WriteString("15:04")
	}

	if second := groups[4]; second != "" {
		sb.WriteString(":05")
	}

	if subsecond := groups[5]; subsecond != "" {
		sb.WriteString(".999999999")
	}

	if offset := groups[6]; offset != "" {
		// Example format strings for various offset values
		//    "Z"       --> "Z"
		//    "-04:00"  --> "Z07:00"
		//    " +10:00" --> " Z07:00" (leading space)
		//    "-0400"   --> "-0700"
		//    " +1200"  --> " -0700" (leading space)
		if offset == "Z" {
			sb.WriteString("Z")
		} else {
			if strings.HasPrefix(offset, " ") {
				sb.WriteString(" ")
			}

			if strings.Contains(offset, ":") {
				sb.WriteString("Z07:00")
			} else {
				sb.WriteString("-0700")
			}
		}
	}

	return sb.String()
}

// Timezone converts a 3-4 letter abbreviation (e.g. "MST" or "UTC") to an explicit time.Location object.
//
// This is only necessary for timezone formats that include a tz abbreviation instead of a UTC offset.
func Timezone(zone string) (*time.Location, error) {
	name := zone

	switch zone {
	case "HST":
		name = "Pacific/Honolulu"
	case "AKST", "AKDT":
		name = "America/Anchorage"
	case "PST", "PDT":
		name = "America/Los_Angeles"
	case "MST", "MDT":
		name = "America/Denver"
	case "CST", "CDT":
		name = "America/Chicago"
	case "EST", "EDT":
		name = "America/New_York"
	}

	// NOTE: "UTC" is natively understood by time.LoadLocation
	return time.LoadLocation(name)
}

// Most timestamp variants are a subset of RFC3339Nano: "2006-01-02T15:04:05.999999999Z07:00"
var rfc3339Pattern = regexp.MustCompile(
	`^(?P<date>\d{4}-\d{2}-\d{2})` + // date (required)
		`(?P<t>[T ])?` + // "T" or " " date-time separator
		`(?P<minute>\d{2}:\d{2})?` + // hour:minute
		`(?P<second>:\d{2})?` + // :second
		`(?P<subsecond>\.\d+)?` + // .subseconds
		`(?P<offset>(?:Z| ?(?:\-|\+)\d{2}:?\d{2}))?$`, // offset: "Z" or "+/-07:00" or "+/-0700" or " +/-0700"
)
