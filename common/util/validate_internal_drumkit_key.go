package util

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/env"
)

const (
	missingKey = "missing internal drumkit key"
	invalidKey = "invalid internal drumkit key"
)

// ValidateInternalDrumkitKey checks that the internal Drumkit key is present and valid.
// This is used for protected routes that are meant for internal use only. See shared 1Password for key.
func ValidateInternalDrumkitKey(ctx context.Context, internalKey string) (bool, error) {
	if internalKey == "" {
		log.Warn(ctx, missingKey)
		return false, errors.New(missingKey)
	}

	if internalKey != env.Vars.InternalDrumkitKey {
		log.Warn(ctx, invalidKey)
		return false, errors.New(invalidKey)
	}

	return true, nil
}
