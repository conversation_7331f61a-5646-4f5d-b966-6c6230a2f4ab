package util

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Map of USA state abbreviations to full state names
var usaStateAbbreviations = map[string]string{
	"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "CA": "California",
	"CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "FL": "Florida", "GA": "Georgia",
	"HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa",
	"KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MD": "Maryland",
	"MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri",
	"MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey",
	"NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "OH": "Ohio",
	"OK": "Oklahoma", "OR": "Oregon", "PA": "Pennsylvania", "RI": "Rhode Island", "SC": "South Carolina",
	"SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont",
	"VA": "Virginia", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming",
}

var canadaProvinceAbbreviations = map[string]string{
	"AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick",
	"NL": "Newfoundland and Labrador", "NT": "Northwest Territories", "NS": "Nova Scotia",
	"NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec",
	"SK": "Saskatchewan", "YT": "Yukon",
}

var usaStateNamesToAbbreviations = map[string]string{
	"alabama": "AL", "alaska": "AK", "arizona": "AZ", "arkansas": "AR", "california": "CA",
	"colorado": "CO", "connecticut": "CT", "delaware": "DE", "florida": "FL", "georgia": "GA",
	"hawaii": "HI", "idaho": "ID", "illinois": "IL", "indiana": "IN", "iowa": "IA",
	"kansas": "KS", "kentucky": "KY", "louisiana": "LA", "maine": "ME", "maryland": "MD",
	"massachusetts": "MA", "michigan": "MI", "minnesota": "MN", "mississippi": "MS", "missouri": "MO",
	"montana": "MT", "nebraska": "NE", "nevada": "NV", "new hampshire": "NH", "new jersey": "NJ",
	"new mexico": "NM", "new york": "NY", "north carolina": "NC", "north dakota": "ND", "ohio": "OH",
	"oklahoma": "OK", "oregon": "OR", "pennsylvania": "PA", "rhode island": "RI", "south carolina": "SC",
	"south dakota": "SD", "tennessee": "TN", "texas": "TX", "utah": "UT", "vermont": "VT",
	"virginia": "VA", "washington": "WA", "west virginia": "WV", "wisconsin": "WI", "wyoming": "WY",
}

var canadaProvinceNamesToAbbreviations = map[string]string{
	"alberta": "AB", "british columbia": "BC", "manitoba": "MB", "new brunswick": "NB",
	"newfoundland and labrador": "NL", "northwest territories": "NT", "nova scotia": "NS", "nunavut": "NU",
	"ontario": "ON", "prince edward island": "PE", "quebec": "QC", "saskatchewan": "SK", "yukon": "YT",
}

// Function that maps state/province abbreviation to full name. Supports USA and Canada.
func GetStateFullName(ctx context.Context, abbreviation string) string {
	abbreviation = strings.ToUpper(abbreviation)
	fullName, exists := usaStateAbbreviations[abbreviation]
	if exists {
		return fullName
	}
	fullName, exists = canadaProvinceAbbreviations[abbreviation]
	if exists {
		return fullName
	}
	log.Warn(ctx, "Unknown state abbreviation", zap.String("abbreviation", abbreviation))
	return "Unknown State"
}

// Function that maps full state/province name to abbreviation. Supports USA and Canada.
func GetStateAbbreviation(ctx context.Context, fullName string) string {
	fullName = strings.ToLower(fullName)
	abbreviation, exists := usaStateNamesToAbbreviations[fullName]
	if exists {
		return abbreviation
	}
	abbreviation, exists = canadaProvinceNamesToAbbreviations[fullName]
	if exists {
		return abbreviation
	}

	log.Warn(ctx, "Unknown state name", zap.String("fullName", fullName))
	return "Unknown State"
}
