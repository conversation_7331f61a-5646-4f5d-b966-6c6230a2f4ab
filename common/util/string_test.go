package util

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tiktoken-go/tokenizer"
)

// TestTruncateUserPrompt tests the truncateUserPrompt function
func TestTruncateUserPrompt(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Short string (no truncation needed)",
			input:    "This is a short prompt that doesn't need truncation",
			expected: "This is a short prompt that doesn't need truncation",
		},
		{
			name:     "String with exactly 1500 tokens",
			input:    generateStringWithTokens(1500),
			expected: generateStringWithTokens(1500),
		},
		{
			name:     "String exceeding 1500 tokens",
			input:    generateStringWithTokens(2000),
			expected: generateStringWithTokens(1500) + "... [truncated]",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := TokenTruncater(test.input)

			// For the exact token tests, we need to verify differently
			// since the encoding/decoding might not be exact
			if test.name == "String with exactly 1500 tokens" ||
				test.name == "String exceeding 1500 tokens" {
				// Verify the result doesn't exceed 1500 tokens + truncation suffix
				enc, err := tokenizer.Get(tokenizer.Cl100kBase)
				if err != nil {
					t.Fatalf("Failed to get tokenizer: %v", err)
				}

				//nolint:errcheck
				ids, _, _ := enc.Encode(result)

				if test.name == "String with exactly 1500 tokens" {
					assert.LessOrEqual(t, len(ids), 1500, "Result should not exceed 1500 tokens")
					assert.Equal(t, test.input, result, "Input with exactly 1500 tokens should not be truncated")
				} else {
					// If the input was truncated, we expect the suffix and the token count to be ≤ 1500 + suffix tokens
					assert.Contains(
						t,
						result,
						"... [truncated]",
						"Truncated result should contain the truncation suffix",
					)
					assert.LessOrEqual(
						t,
						len(ids), 1515, "Truncated result should be close to 1500 tokens plus suffix")
				}
			} else {
				assert.Equal(t, test.expected, result)
			}
		})
	}
}

// TestTruncateUserPromptWithFailedTokenizer tests the fallback to approximateTruncation when tokenizer fails
func TestTruncateUserPromptWithFailedTokenizer(t *testing.T) {
	// Create a very long input that will require truncation
	longText := generateLongString(10000)

	// Get the result from truncateUserPrompt
	result := TokenTruncater(longText)

	// Check that the result was truncated (contains the truncation marker)
	assert.Contains(t, result, "... [truncated]")

	// Verify the result doesn't exceed expected length
	// With the tokenizer, should be around 1500 tokens
	// With approximateTruncation fallback, we observed it's around 9560 chars
	assert.True(t,
		len(result) <= 10000,
		"Result should be truncated from the original 10000 chars, got %d chars",
		len(result),
	)
}

// TestApproximateTruncation tests the approximateTruncation function
func TestApproximateTruncation(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Short string (no truncation needed)",
			input:    "This is a short prompt that doesn't need truncation",
			expected: "This is a short prompt that doesn't need truncation",
		},
		{
			name:     "Long string requiring truncation",
			input:    generateLongString(7000),                     // Well over 1500 tokens at ~4 chars per token
			expected: generateLongString(5960) + "... [truncated]", // (1500-10)*4 = 5960 chars
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := approximateTokenTruncater(test.input, 1500)
			assert.Equal(t, test.expected, result)
		})
	}
}

// Helper functions for tests

// generateStringWithTokens creates a string with approximately the specified number of tokens
func generateStringWithTokens(tokenCount int) string {
	// Get the tokenizer
	enc, err := tokenizer.Get(tokenizer.Cl100kBase)
	if err != nil {
		return generateLongString(tokenCount * 4) // fallback
	}

	// Generate a base string that's likely to have more tokens than we need
	baseString := generateLongString(tokenCount * 5)

	// Encode the string
	ids, _, err := enc.Encode(baseString)
	if err != nil {
		return generateLongString(tokenCount * 4) // fallback
	}

	// Truncate to desired token count
	if len(ids) <= tokenCount {
		return baseString
	}

	truncatedIDs := ids[:tokenCount]
	truncatedText, err := enc.Decode(truncatedIDs)
	if err != nil {
		return generateLongString(tokenCount * 4) // fallback
	}

	return truncatedText
}

// generateLongString generates a string with the specified length
func generateLongString(length int) string {
	words := []string{
		"lorem", "ipsum", "dolor", "sit", "amet", "consectetur",
		"adipiscing", "elit", "sed", "do", "eiusmod", "tempor",
		"incididunt", "ut", "labore", "et", "dolore", "magna", "aliqua",
	}

	var result strings.Builder
	wordCount := len(words)

	for i := 0; result.Len() < length; i++ {
		result.WriteString(words[i%wordCount])
		result.WriteString(" ")
	}

	s := result.String()
	if len(s) > length {
		return s[:length]
	}
	return s
}
