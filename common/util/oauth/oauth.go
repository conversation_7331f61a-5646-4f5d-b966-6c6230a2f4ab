package oauth

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"
	"golang.org/x/oauth2"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/common/util/crypto"
	"github.com/drumkitai/drumkit/common/util/otel"
)

// Can be replaced for unit tests
var (
	UpdateUserFunc       = userDB.Update
	UpdateOnPremUserFunc = onpremuser.Update
)

type (
	Options struct {
		EncryptionKey *[]byte
	}

	Option func(*Options)

	cachingTokenSource struct {
		base          oauth2.TokenSource
		user          models.UserAccessor
		encryptionKey *[]byte
	}
)

func WithEncryptionKey(key *[]byte) Option {
	return func(o *Options) {
		o.EncryptionKey = key
	}
}

func (c *cachingTokenSource) Token() (*oauth2.Token, error) {

	if time.Until(c.user.GetTokenExpiry()) > 5*time.Minute {
		// Get a clean context for decryption
		ctx := context.Background()

		// Try to use existing token instead of fetching a new one
		accessToken, aErr := crypto.DecryptAESGCM(ctx, c.user.GetEncryptedAccessToken(), c.encryptionKey)
		refreshToken, rErr := crypto.DecryptAESGCM(ctx, c.user.GetEncryptedRefreshToken(), c.encryptionKey)

		if aErr == nil && rErr == nil {
			return &oauth2.Token{
				AccessToken:  accessToken,
				RefreshToken: refreshToken,
				Expiry:       c.user.GetTokenExpiry(),
			}, nil
		}
	}

	tok, err := c.base.Token()
	if err != nil {
		return nil, err
	}

	ctx := log.With(
		context.Background(),
		zap.Uint("userID", c.user.GetID()),
	)

	if s, ok := c.user.(models.ServiceIDAccessor); ok {
		ctx = log.With(
			ctx,
			zap.Uint("serviceID", s.GetServiceID()),
		)
	}

	encryptedAccessToken, err := crypto.EncryptAESGCM(ctx, tok.AccessToken, c.encryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt access token for %s: %w", c.user.GetEmailAddress(), err)
	}

	encryptedRefreshToken, err := crypto.EncryptAESGCM(ctx, tok.RefreshToken, c.encryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt refresh token for %s: %w", c.user.GetEmailAddress(), err)
	}

	c.user.SetEncryptedAccessToken(encryptedAccessToken)
	c.user.SetEncryptedRefreshToken(encryptedRefreshToken)
	c.user.SetTokenExpiry(tok.Expiry)

	// During the signup flow, this will run before the user has actually been created in the DB.
	// So if the ID is 0, don't update the DB with the token just yet (CreateUser will be called shortly).
	if c.user.GetID() > 0 {
		var err error

		switch user := c.user.(type) {
		case *models.User:
			err = UpdateUserFunc(ctx, *user)
		case *models.OnPremUser:
			err = UpdateOnPremUserFunc(ctx, *user)
		default:
			return nil, fmt.Errorf("unknown user type")
		}
		if err != nil {
			return nil, err
		}
	}

	return tok, nil
}

func NewCachingTokenSource[T models.UserAccessor](
	ctx context.Context,
	user T,
	config *oauth2.Config,
	opts ...Option,
) (oauth2.TokenSource, error) {

	options := &Options{
		EncryptionKey: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	accessToken, err := crypto.DecryptAESGCM(ctx, user.GetEncryptedAccessToken(), options.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt access token for %s: %w", user.GetEmailAddress(), err)
	}

	refreshToken, err := crypto.DecryptAESGCM(ctx, user.GetEncryptedRefreshToken(), options.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt refresh token for %s: %w", user.GetEmailAddress(), err)
	}

	orig := config.TokenSource(ctx, &oauth2.Token{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		Expiry:       user.GetTokenExpiry(),
	})

	return oauth2.ReuseTokenSource(
		nil,
		&cachingTokenSource{base: orig, user: user, encryptionKey: options.EncryptionKey},
	), nil
}

// Wraps oauth2 client with tracing
// FIXME: Traces oauth refresh requests correctly, but Gmail API traces are not attached to parent trace
// (Outlook API traces are fine)
func NewTracingClient(ctx context.Context, ts oauth2.TokenSource) *http.Client {
	httpClient := otel.TracingHTTPClient()
	httpClient.Timeout = 10 * time.Second
	ctx = context.WithValue(ctx, oauth2.HTTPClient, httpClient)

	return oauth2.NewClient(ctx, ts)
}
