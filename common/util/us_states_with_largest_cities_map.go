package util

type StateCity struct {
	State       string
	LargestCity string
}

var ListOfUSAStatesWithLargestCities = []StateCity{
	{State: "AL", LargestCity: "Birmingham"},
	{State: "AK", LargestCity: "Anchorage"},
	{State: "AZ", LargestCity: "Phoenix"},
	{State: "AR", LargestCity: "Little Rock"},
	{State: "CA", LargestCity: "Los Angeles"},
	{State: "CO", LargestCity: "Denver"},
	{State: "CT", LargestCity: "Bridgeport"},
	{State: "DE", LargestCity: "Wilmington"},
	{State: "FL", LargestCity: "Jacksonville"},
	{State: "GA", LargestCity: "Atlanta"},
	{State: "HI", LargestCity: "Honolulu"},
	{State: "ID", LargestCity: "Boise"},
	{State: "IL", LargestCity: "Chicago"},
	{State: "IN", LargestCity: "Indianapolis"},
	{State: "IA", LargestCity: "Des Moines"},
	{State: "KS", LargestCity: "Wichita"},
	{State: "KY", LargestCity: "Louisville"},
	{State: "LA", LargestCity: "New Orleans"},
	{State: "ME", LargestCity: "Portland"},
	{State: "MD", LargestCity: "Baltimore"},
	{State: "MA", LargestCity: "Boston"},
	{State: "MI", LargestCity: "Detroit"},
	{State: "MN", LargestCity: "Minneapolis"},
	{State: "MS", LargestCity: "Jackson"},
	{State: "MO", LargestCity: "Saint Louis"},
	{State: "MT", LargestCity: "Billings"},
	{State: "NE", LargestCity: "Omaha"},
	{State: "NV", LargestCity: "Las Vegas"},
	{State: "NH", LargestCity: "Manchester"},
	{State: "NJ", LargestCity: "Newark"},
	{State: "NM", LargestCity: "Albuquerque"},
	{State: "NY", LargestCity: "New York"},
	{State: "NC", LargestCity: "Charlotte"},
	{State: "ND", LargestCity: "Bismarck"},
	{State: "OH", LargestCity: "Cleveland"},
	{State: "OK", LargestCity: "Oklahoma City"},
	{State: "OR", LargestCity: "Portland"},
	{State: "PA", LargestCity: "Philadelphia"},
	{State: "RI", LargestCity: "Providence"},
	{State: "SC", LargestCity: "Columbia"},
	{State: "SD", LargestCity: "Pierre"},
	{State: "TN", LargestCity: "Nashville"},
	{State: "TX", LargestCity: "Houston"},
	{State: "UT", LargestCity: "Salt Lake City"},
	{State: "VT", LargestCity: "Montpelier"},
	{State: "VA", LargestCity: "Richmond"},
	{State: "WA", LargestCity: "Seattle"},
	{State: "WV", LargestCity: "Charleston"},
	{State: "WI", LargestCity: "Milwaukee"},
	{State: "WY", LargestCity: "Cheyenne"},
	{State: "DC", LargestCity: "Washington"},
}
