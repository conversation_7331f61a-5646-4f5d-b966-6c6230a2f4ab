package braintrustutil

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/braintrustdata/braintrust-go"
	"github.com/braintrustdata/braintrust-go/shared"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	"github.com/drumkitai/drumkit/fn/api/env"
)

// updateQuoteWithBraintrustLogs submits the quote request to Braintrust for analysis
// and updates the quote request DB record with the Braintrust log ID.
// Even though we're using regex instead of LLM, we still want to log this for evaluation
// and to track potential deprecations over time.
func UpdateQuoteRequestWithBraintrustLogs(
	ctx context.Context,
	quoteRequest models.QuoteRequest,
	htmlSnippet string,
) error {

	// We don't care about customerID as that varies by DB environment so we temporarily remove it
	quoteRequest.SuggestedRequest.CustomerID = 0
	jsonPayload, err := json.Marshal(quoteRequest.SuggestedRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal suggested quote request: %w", err)
	}

	braintrustProjectDetails := braintrustsdk.CreateProjectDetails(braintrustsdk.QRPSExtractData, false)

	user, err := rds.GetUserByID(ctx, quoteRequest.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	braintrustInput := braintrustsdk.BraintrustLogInput{
		ID:               uuid.NewString(),
		ProjectDetails:   braintrustProjectDetails,
		PortalCategory:   quoteRequest.SourceCategory,
		PortalName:       quoteRequest.Source,
		PortalExternalID: quoteRequest.SourceExternalID,
		PortalURL:        quoteRequest.SourceURL,
		Email: models.Email{
			ServiceID: quoteRequest.ServiceID,
			UserID:    quoteRequest.UserID,
			Account:   user.EmailAddress,
		},
		UserPrompt: htmlSnippet,
	}

	braintrustErr := braintrustsdk.SubmitLog(
		ctx,
		braintrustInput.ProjectDetails,
		GetBraintrustLogForQuoteScraper(ctx, braintrustInput, string(jsonPayload)),
	)
	if braintrustErr != nil {
		if env.Vars.AppEnv == "prod" {
			log.WarnNoSentry(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
		} else {
			log.Debug(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
		}

		return fmt.Errorf("error submitting log to braintrust: %w", braintrustErr)
	}
	braintrustLogID := braintrustInput.ID
	// Update the quote request with the Braintrust log ID
	quoteRequest.BraintrustLogIDs = []models.LogRecord{{
		ID:              braintrustLogID,
		ProjectStepName: string(braintrustsdk.QRPSExtractData),
	}}

	if err := quoteRequestDB.UpdateQuoteRequestBraintrustLogID(ctx, &quoteRequest); err != nil {
		log.Error(ctx, "failed to update quote request with Braintrust log ID",
			zap.Error(err),
			zap.Uint("quoteRequestID", quoteRequest.ID),
			zap.String("braintrustLogID", braintrustLogID),
		)

		return fmt.Errorf("failed to update quote request with Braintrust log ID: %w", err)
	}

	log.Info(ctx, "successfully submitted to Braintrust",
		zap.Uint("quoteRequestID", quoteRequest.ID),
		zap.String("braintrustLogID", braintrustLogID),
	)

	return nil
}

// GetBraintrustLogForQuoteScraper returns a Braintrust log input for the quote scraper.
func GetBraintrustLogForQuoteScraper(
	ctx context.Context,
	logInput braintrustsdk.BraintrustLogInput,
	output string,
) braintrust.InsertProjectLogsEventParam {

	env := os.Getenv("APP_ENV")
	service, err := rds.GetServiceByID(ctx, logInput.Email.ServiceID)
	if err != nil {
		log.WarnNoSentry(ctx,
			"error getting service for braintrust log",
			zap.Error(err),
			zap.Uint("serviceID", logInput.Email.ServiceID),
		)
	}

	return braintrust.InsertProjectLogsEventParam{
		ID: braintrust.String(logInput.ID),
		Input: braintrust.Raw[any](
			map[string]any{
				"user_prompt":      logInput.UserPrompt,
				"developer_prompt": logInput.DeveloperPrompt,
			},
		),
		Output: braintrust.Raw[any](output),
		Tags:   braintrust.Raw[[]string]([]string{"Portal HTML"}),
		Metadata: braintrust.Raw[shared.InsertProjectLogsEventMetadataParam](
			shared.InsertProjectLogsEventMetadataParam{
				ExtraFields: map[string]any{
					"environment": env,
					// Portal fields
					"portal_name":        logInput.PortalName,
					"portal_external_id": logInput.PortalExternalID,
					"portal_url":         logInput.PortalURL,

					// User and Service fields
					"user_id":               logInput.Email.UserID,
					"service_id":            logInput.Email.ServiceID,
					"service_name":          service.Name,
					"service_email_domains": service.EmailDomains,
				},
			},
		),
		SpanAttributes: braintrust.Raw[shared.SpanAttributesParam](
			shared.SpanAttributesParam{
				Name: braintrust.String(string(logInput.ProjectDetails.StepName)),
			},
		),
	}
}
