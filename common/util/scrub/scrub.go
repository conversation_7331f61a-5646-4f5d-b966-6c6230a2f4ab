// Adapted from Oracle
package scrub

import (
	"fmt"
	"net/url"
	"strings"
)

var sensitiveQueryParams = map[string]bool{
	"accesstoken":        true,
	"access_token":       true,
	"apikey":             true,
	"api_key":            true,
	"auth":               true,
	"auth_key":           true,
	"authenticationkey":  true,
	"authentication_key": true,
	"authkey":            true,
	"clientid":           true,
	"client_id":          true,
	"clientsecret":       true,
	"client_secret":      true,
	"key":                true,
	"password":           true,
	"pwd":                true,
	"refreshtoken":       true,
	"refresh_token":      true,
	"token":              true,
	"sessiontoken":       true,
	"session_token":      true,
	"userpassword":       true,
	"user_password":      true,
}

// URL redacts any sensitive query parameters
func URL(u *url.URL) *url.URL {
	// Scrub any sensitive query parameters
	scrubbedQuery := make(url.Values, len(u.Query()))

	for key, vals := range u.Query() {
		if sensitiveQueryParams[strings.ToLower(key)] {
			scrubbedVals := make([]string, 0, len(vals))
			for _, v := range vals {
				scrubbedVals = append(scrubbedVals, fmt.Sprintf("redacted-%d", len(v)))
			}
			scrubbedQuery[key] = scrubbedVals
		} else {
			scrubbedQuery[key] = vals
		}
	}

	return &url.URL{
		Scheme:   u.Scheme,
		Host:     u.Host,
		Path:     u.Path,
		RawQuery: scrubbedQuery.Encode(),
	}
}
