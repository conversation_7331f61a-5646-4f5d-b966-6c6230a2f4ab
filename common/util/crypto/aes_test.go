package crypto

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 32-byte key
const testKey = "abcdefghijklmnopqrstuvwxyz123456"

func TestEncryptDecrypt(t *testing.T) {
	t.Parallel()

	// Shorter strings
	testSingle(t, "")
	testSingle(t, "axle")
	testSingle(t, "0123456789abcdef")
	testSingle(t, "AIDAYEISFP2TTUTQ7PANO")

	// Longer strings (>= key length)
	testSingle(t, testKey)
	testSingle(t, testKey+testKey+testKey)
	testSingle(t, testKey+testKey+testKey+testKey+"abc")
}

func testSingle(t *testing.T, plaintext string) {
	key := []byte(testKey)

	ciphertext, err := EncryptAESGCM(context.Background(), plaintext, &key)
	require.NoError(t, err)

	assert.NotEqual(t, ciphertext, plaintext)
	assert.Greater(t, len(ciphertext), len(plaintext))

	decrypted, err := DecryptAESGCM(context.Background(), ciphertext, &key)
	require.NoError(t, err)

	assert.Equal(t, plaintext, decrypted)
}

func TestEncryptErrors(t *testing.T) {
	t.Parallel()

	key := []byte("axle")

	_, err := EncryptAESGCM(context.Background(), "axle", &key)
	assert.EqualError(t, err, "key must be 32 bytes, found 4")
}

// The ciphertext should be different every time
func TestEncryptNonce(t *testing.T) {
	t.Parallel()

	key := []byte(testKey)

	cipher1, err := EncryptAESGCM(context.Background(), "axle", &key)
	require.NoError(t, err)

	cipher2, err := EncryptAESGCM(context.Background(), "axle", &key)
	require.NoError(t, err)

	assert.NotEqual(t, cipher1, cipher2)
}

func TestDecryptErrors(t *testing.T) {
	t.Parallel()

	shortKey := []byte("axle")
	key := []byte(testKey)

	_, err := DecryptAESGCM(context.Background(), "", &shortKey)
	assert.EqualError(t, err, "key must be 32 bytes, found 4")

	_, err = DecryptAESGCM(context.Background(), "!", &key)
	assert.EqualError(t, err, "base64 decoding failed: illegal base64 data at input byte 0")

	_, err = DecryptAESGCM(context.Background(), "axle", &key)
	assert.EqualError(t, err, "ciphertext is too short (3 bytes)")

	_, err = DecryptAESGCM(context.Background(), testKey+testKey+testKey, &key)
	assert.EqualError(t, err, "decryption failed: cipher: message authentication failed")
}
