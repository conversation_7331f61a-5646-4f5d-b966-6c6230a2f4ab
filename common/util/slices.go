package util

import (
	"regexp"
	"sort"
	"strings"
)

func SlicesContainSameElements(slice1, slice2 []string) bool {
	if len(slice1) != len(slice2) {
		return false
	}

	elementCount := make(map[string]int)

	for _, elem := range slice1 {
		elementCount[elem]++
	}

	for _, elem := range slice2 {
		if _, exists := elementCount[elem]; !exists || elementCount[elem] == 0 {
			return false
		}
		elementCount[elem]--
	}

	for _, count := range elementCount {
		if count != 0 {
			return false
		}
	}

	return true
}

// IsStringInArray checks if the given string is present as a *full word* in the array.
// It does not support partial matches.
func IsStringInArray(array []string, item string) bool {
	// Create a regex to match the exact word with word boundaries
	pattern := `\b` + regexp.QuoteMeta(strings.ToLower(item)) + `\b`
	re := regexp.MustCompile(pattern)

	for _, v := range array {
		if re.MatchString(strings.ToLower(v)) {
			return true
		}
	}
	return false
}

func IsStringFullMatch(textToCheck, substring string) bool {
	// Create a regex to match the exact word with word boundaries
	pattern := `\b` + regexp.QuoteMeta(strings.ToLower(substring)) + `\b`
	re := regexp.MustCompile(pattern)

	return re.MatchString(strings.ToLower(textToCheck))
}

// IsArrayElmInString checks if any of the substrings in the array are present in the given string.
func IsArrayElmInString(s string, array []string) bool {
	for _, substring := range array {
		if strings.Contains(s, substring) {
			return true
		}
	}
	return false
}

// GetMapKeys returns a sorted slice of strings containing all keys from the input map.
func getMapKeys(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys) // Sort for consistent ordering
	return keys
}

// FindMatchingKey searches for a key in the map that matches the given string and
// returns the first matching key found, or an empty string if no match is found.
// This is useful for matching LLM output to a value in a map. Currently, it's used
// for load building with the Turvo reverse maps defined in common/integrations/tms/turvo/enums.go.
func FindMatchingKey(s string, m map[string]string) string {
	if strings.TrimSpace(s) == "" {
		return ""
	}

	for _, key := range getMapKeys(m) {
		if strings.Contains(strings.ToLower(key), strings.ToLower(s)) ||
			strings.Contains(strings.ToLower(s), strings.ToLower(key)) {

			return key
		}
	}
	return ""
}
