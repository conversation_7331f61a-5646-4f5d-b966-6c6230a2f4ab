package perms

import (
	"context"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

var devDomains = map[string]bool{
	"axleapi.com":            true,
	"drumkit.ai":             true,
	"78n517.onmicrosoft.com": true,
}

// Ensures that dev accounts can't modify production loads, and users can only modify loads belonging to their service.
func HasLoadReadWritePermissions(c *fiber.Ctx, load models.Load, tmsClient tms.Interface) bool {
	userServiceID := middleware.ServiceIDFromContext(c)
	claims := middleware.ClaimsFromContext(c)
	IsInternalEmail := IsInternalEmail(c.UserContext(), claims.Email)
	isMSAdmin := IsMSAdmin(claims.Email)

	// We're good if we're dev and this is a test load
	if (IsInternalEmail || isMSAdmin) &&
		(tmsClient.GetTestLoads()[load.FreightTrackingID] || tmsClient.GetTestLoads()["*"]) {
		return true
	}

	// We're good if it's a production user and the load belongs to their service
	if load.ServiceID == userServiceID && !IsInternalEmail && !isMSAdmin {
		return true
	}

	// We've failed all conditions for WRITE
	// We may have READ permissions only though
	return false
}

func HasLoadReadPermissions(c *fiber.Ctx, load models.Load) bool {
	userServiceID := middleware.ServiceIDFromContext(c)

	// We're good if it's our own service id...
	if load.ServiceID == userServiceID {
		return true
	}

	// We're good if we're dev
	claims := middleware.ClaimsFromContext(c)
	IsInternalEmail := IsInternalEmail(c.UserContext(), claims.Email)
	isMSAdmin := IsMSAdmin(claims.Email)

	return IsInternalEmail || isMSAdmin
}

func IsInternalEmail(ctx context.Context, email string) bool {
	email = strings.ToLower(email)
	claimsDomain := strings.Split(email, "@")
	if len(claimsDomain) != 2 {
		log.ErrorNoSentry(ctx, "misformatted email in claims", zap.String("claimsEmail", email))
		// Default to true to limit permissions
		return true
	}

	// Even when customers create accounts for us, (i.e. <EMAIL>, drumkit@fetchfreight),
	// that account doesn't have permission to modify production loads. We'll cross that bridge if that changes
	return strings.Contains(claimsDomain[0], "axle") || strings.Contains(claimsDomain[0], "beacon") ||
		strings.Contains(claimsDomain[0], "drumkit") ||
		devDomains[claimsDomain[1]]
}

// For Outlook approval process, e.g. <EMAIL>
func IsMSAdmin(email string) bool {
	return strings.HasPrefix(email, "admin") && strings.HasSuffix(email, "onmicrosoft.com")
}
