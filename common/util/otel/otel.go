package otel

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	axiotel "github.com/axiomhq/axiom-go/axiom/otel"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	oteltrace "go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type MetaSpan struct {
	span       oteltrace.Span
	name       string
	attributes []attribute.KeyValue
}

var tracer = otel.Tracer(os.Getenv("TRACER"))

func CreateTracerProvider(ctx context.Context, dataset string) func() error {
	stop, err := axiotel.InitTracing(ctx, dataset, "axiom-otel", "v0.0.1")
	if err != nil {
		log.Error(ctx, "Failed to create Axiom Otel Tracing", zap.Error(err))
	}

	return stop
}

// StartSpan creates a new tracing span and Sentry scope with the given span and key/value attributes.
//
// The returned context has a new tracer span which can be accessed via oteltrace.SpanFromContext(ctx)
//
// The caller is responsible for deferring a call to end the span. For example:
//
//	    func GetStuff(ctx context.Context) (_ string, err error) {
//		    ctx, span := StartSpan(ctx, "getStuff", nil)
//		    defer func() { span.End(err) }()
//		    return getStuffHelper(ctx)
//		}
func StartSpan(ctx context.Context, name string, attrs []attribute.KeyValue) (context.Context, MetaSpan) {
	ctx, span := tracer.Start(ctx, name, oteltrace.WithAttributes(attrs...))

	span.SetAttributes(attribute.String("span_name", name))
	for _, attr := range attrs {
		if val := attr.Value.Emit(); val != "" {
			span.AddEvent(name, oteltrace.WithAttributes(attr))
		}
	}

	return ctx, MetaSpan{
		span:       span,
		name:       name,
		attributes: attrs,
	}
}

// SafeIntAttribute creates an int attribute from a uint value without overflow
func SafeIntAttribute(key string, val uint) attribute.KeyValue {
	// The maximum value that can be stored in an int
	const maxInt = int(^uint(0) >> 1)

	// Check if value exceeds max int
	if val > uint(maxInt) {
		// If it would overflow, use String to avoid any overflow issues
		return attribute.String(key, fmt.Sprintf("%d", val))
	}

	return attribute.Int(key, int(val))
}

func IntegrationAttrs(integration models.Integration) []attribute.KeyValue {
	return []attribute.KeyValue{
		SafeIntAttribute("integration_id", integration.ID),
		SafeIntAttribute("service_id", integration.ServiceID),
		attribute.String("integration_name", string(integration.Name)),
		attribute.String("integration_type", string(integration.Type)),
	}
}

func LogWithIntegrationAttrs(
	ctx context.Context,
	integration models.Integration,
) (context.Context, []attribute.KeyValue) {
	ctx = log.With(ctx,
		zap.Uint("integrationID", integration.ID),
		zap.String("integrationName", fmt.Sprint(integration.Name)),
		zap.String("integrationType", fmt.Sprint(integration.Type)),
		zap.Uint("serviceID", integration.ServiceID),
	)

	return ctx, IntegrationAttrs(integration)
}

func LoadAttrs(load models.Load) []attribute.KeyValue {
	return []attribute.KeyValue{
		SafeIntAttribute("load_id", load.ID),
		attribute.String("freight_tracking_id", load.FreightTrackingID),
		attribute.String("external_tms_id", load.ExternalTMSID),
	}
}

func EmailAttrs(email *models.Email) []attribute.KeyValue {
	if email == nil {
		return nil
	}
	return []attribute.KeyValue{
		attribute.String("email_id", fmt.Sprintf("%d", email.ID)),
		attribute.String("external_id", fmt.Sprintf("%d", email.UserID)),
		attribute.String("threadID", email.ThreadID),
	}
}

// End must be deferred after every call to StartSpan.
//
// Non-nil errors are reported in the trace, in Sentry, and printed to stdout.
// To ensure the final error is returned, use a named err return value.
//
//	func GetStuff(ctx context.Context) (err error) {
//		ctx, span := StartSpan(ctx, "getStuff", nil)
//
//		// INCORRECT - err is evaluated immediately, always nil
//		defer span.End(err)
//
//		// CORRECT - err is evaluated at the end, using the named return
//		defer func() { span.End(err) }()
//	}
func (ms MetaSpan) End(err error) {
	defer ms.span.End()

	if err != nil {
		ms.span.RecordError(err)
		ms.span.SetStatus(codes.Error, err.Error())
	}

}

// CustomSpanNameFormatter formats the span name to include the HTTP method and URL without query parameters.
func CustomSpanNameFormatter(_ string, r *http.Request) string {
	method := r.Method
	u := *r.URL
	u.RawQuery = "" // Remove query parameters which may contain sensitive info

	return fmt.Sprintf("%s %s", method, u.String())
}

func TracingHTTPClient(timeout ...time.Duration) *http.Client {
	t := 30 * time.Second
	if len(timeout) > 0 {
		t = timeout[0]
	}

	client := &http.Client{
		Timeout: t,
		Transport: otelhttp.NewTransport(http.DefaultTransport,
			otelhttp.WithSpanNameFormatter(CustomSpanNameFormatter)),
	}

	return client
}
