package util

import "github.com/tiktoken-go/tokenizer"

// TokenTruncater truncates input to a specified number of tokens - default is 1,500
func TokenTruncater(input string, tokenCap ...int) string {
	var truncateCap int
	if len(tokenCap) > 0 {
		truncateCap = tokenCap[0]
	} else {
		truncateCap = 1500
	}

	enc, err := tokenizer.Get(tokenizer.Cl100kBase)
	if err != nil {
		return approximateTokenTruncater(input, truncateCap)
	}

	ids, _, err := enc.Encode(input)
	if err != nil {
		return approximateTokenTruncater(input, truncateCap)
	}

	// Only truncate if needed
	if len(ids) <= truncateCap {
		return input
	}

	truncatedText, err := enc.Decode(ids[0:truncateCap])
	if err != nil {
		return approximateTokenTruncater(input, truncateCap)
	}

	return truncatedText + "... [truncated]"
}

// approximateTokenTruncater truncates the user prompt to a specified number of tokens
func approximateTokenTruncater(input string, truncateCap int) string {
	// Truncate email body if needed to keep total prompt under truncateCap tokens
	// Simple token estimation: ~4 chars per token as a rough approximation

	// Reserve tokens for formatting
	maxBodyTokens := truncateCap - 10 // "Body: " + newline etc.

	estimatedTokens := len(input) / 4

	if maxBodyTokens > 0 && estimatedTokens > maxBodyTokens {
		// Truncate the body to fit within the token limit
		charLimit := maxBodyTokens * 4
		if charLimit < len(input) {
			input = input[:charLimit] + "... [truncated]"
		}
	}

	return input
}
