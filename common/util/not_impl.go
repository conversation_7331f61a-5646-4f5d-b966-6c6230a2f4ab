package util

import "github.com/drumkitai/drumkit/common/models"

type NotImplementedError struct {
	tmsName  models.IntegrationName
	Function string
}

func (e NotImplementedError) Error() string {
	return e.Function + " is not available for " + string(e.tmsName)
}

func NotImplemented(tms models.IntegrationName, function string) error {
	return NotImplementedError{tmsName: tms, Function: function}
}
