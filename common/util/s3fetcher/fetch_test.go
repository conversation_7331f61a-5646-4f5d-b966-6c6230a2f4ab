package s3fetcher

import (
	"bytes"
	"context"
	"errors"
	"io"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// MockS3API is a mock implementation of the S3API interface for testing.
type MockS3API struct {
	GetObjectFunc func(ctx context.Context,
		params *s3.GetObjectInput,
		optFns ...func(*s3.Options),
	) (*s3.GetObjectOutput, error)
}

// GetObject mocks the S3 GetObject method.
func (m *MockS3API) GetObject(ctx context.Context,
	params *s3.GetObjectInput,
	optFns ...func(*s3.Options),
) (*s3.GetObjectOutput, error) {

	return m.GetObjectFunc(ctx, params, optFns...)
}

// errorReadCloser is a helper that implements io.ReadCloser and simulates a read error.
type errorReadCloser struct {
	err error
}

// Read simulates a read error.
func (e *errorReadCloser) Read(_ []byte) (n int, err error) {
	return 0, e.err
}

// Close is a no-op for errorReadCloser.
func (e *errorReadCloser) Close() error {
	return nil
}

func TestNew(t *testing.T) {
	ctx := context.Background()

	t.Run("missing bucket name", func(t *testing.T) {
		_, err := New(ctx, "")
		if err == nil {
			t.Errorf("expected error, got nil")
		}
	})

	t.Run("valid bucket name", func(t *testing.T) {
		fetcher, err := New(ctx, "my-bucket")
		if err != nil {
			t.Errorf("unexpected error: %v", err)
		}
		if fetcher == nil {
			t.Errorf("expected fetcher, got nil")
		}
	})
}

func TestGetAttachment(t *testing.T) {
	ctx := context.Background()
	bucketName := "test-bucket"
	key := "test-key"
	testData := []byte("test data")

	t.Run("successful get", func(t *testing.T) {
		mockS3 := &MockS3API{
			GetObjectFunc: func(_ context.Context,
				params *s3.GetObjectInput,
				_ ...func(*s3.Options),
			) (*s3.GetObjectOutput, error) {

				if *params.Bucket != bucketName {
					t.Errorf("expected bucket %s, got %s", bucketName, *params.Bucket)
				}
				if *params.Key != key {
					t.Errorf("expected key %s, got %s", key, *params.Key)
				}
				return &s3.GetObjectOutput{
					Body: io.NopCloser(bytes.NewReader(testData)),
				}, nil
			},
		}

		client := &S3Client{
			manager:    mockS3,
			bucketName: bucketName,
		}

		data, err := client.GetAttachment(ctx, key)
		if err != nil {
			t.Errorf("unexpected error: %v", err)
		}
		if !bytes.Equal(data, testData) {
			t.Errorf("expected data %v, got %v", testData, data)
		}
	})

	t.Run("GetObject returns error", func(t *testing.T) {
		expectedErr := errors.New("GetObject error")
		mockS3 := &MockS3API{
			GetObjectFunc: func(_ context.Context,
				_ *s3.GetObjectInput,
				_ ...func(*s3.Options),
			) (*s3.GetObjectOutput, error) {

				return nil, expectedErr
			},
		}

		client := &S3Client{
			manager:    mockS3,
			bucketName: bucketName,
		}

		_, err := client.GetAttachment(ctx, key)
		if err == nil {
			t.Errorf("expected error, got nil")
		}
		if !errors.Is(err, expectedErr) {
			t.Errorf("expected error %v, got %v", expectedErr, err)
		}
	})

	t.Run("ReadAll returns error", func(t *testing.T) {
		expectedErr := errors.New("ReadAll error")
		mockS3 := &MockS3API{
			GetObjectFunc: func(_ context.Context,
				_ *s3.GetObjectInput,
				_ ...func(*s3.Options),
			) (*s3.GetObjectOutput, error) {

				return &s3.GetObjectOutput{
					Body: &errorReadCloser{err: expectedErr},
				}, nil
			},
		}

		client := &S3Client{
			manager:    mockS3,
			bucketName: bucketName,
		}

		_, err := client.GetAttachment(ctx, key)
		if err == nil {
			t.Errorf("expected error, got nil")
		}
		if !errors.Is(err, expectedErr) {
			t.Errorf("expected error %v, got %v", expectedErr, err)
		}
	})

	t.Run("empty key", func(t *testing.T) {
		mockS3 := &MockS3API{
			GetObjectFunc: func(_ context.Context,
				params *s3.GetObjectInput,
				_ ...func(*s3.Options),
			) (*s3.GetObjectOutput, error) {

				if params.Key == nil || *params.Key == "" {
					return nil, errors.New("invalid key")
				}
				return &s3.GetObjectOutput{
					Body: io.NopCloser(bytes.NewReader([]byte{})),
				}, nil
			},
		}

		client := &S3Client{
			manager:    mockS3,
			bucketName: bucketName,
		}

		_, err := client.GetAttachment(ctx, "")
		if err == nil {
			t.Errorf("expected error for empty key, got nil")
		}
	})
}
