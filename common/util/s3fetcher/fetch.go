package s3fetcher

import (
	"context"
	"errors"
	"fmt"
	"io"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// Fetcher retrieves objects from an S3 bucket
type Fetcher interface {
	GetAttachment(ctx context.Context, key string) ([]byte, error)
}

type S3API interface {
	GetObject(ctx context.Context, params *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error)
}

// S3Client satisfies the Fetcher interface
type S3Client struct {
	manager    S3API
	bucketName string
}

func New(ctx context.Context, bucketName string) (Fetcher, error) {
	if bucketName == "" {
		return nil, errors.New("missing bucket name")
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"), config.WithRetryMaxAttempts(7))
	if err != nil {
		return nil, err
	}

	return &S3Client{bucketName: bucketName, manager: s3.NewFromConfig(cfg)}, nil
}

func (client *S3Client) GetAttachment(ctx context.Context, key string) ([]byte, error) {
	input := &s3.GetObjectInput{
		Bucket: &client.bucketName,
		Key:    &key,
	}

	result, err := client.manager.GetObject(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to download s3://%s/%s: %w", client.bucketName, key, err)
	}
	defer result.Body.Close()

	body, err := io.ReadAll(result.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read S3 object body: %w", err)
	}

	return body, nil
}
