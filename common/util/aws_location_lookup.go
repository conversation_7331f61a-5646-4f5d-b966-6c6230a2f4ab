package util

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/location"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/util/httplog"
)

func redisLocationLookupKey(ctx context.Context, city string, state string, zipcode string) string {
	if len(state) > 2 {
		state = GetStateAbbreviation(ctx, state)
	}

	// Preventing mismatches such as "Opa Locka" and "Opa-Locka". This works because this function is
	// used both for getting keys to check with as well as getting the keys being set.
	city = strings.ReplaceAll(city, " ", "-")

	if zipcode == "" {
		return fmt.Sprintf("location-lookup-%s-%s", strings.ToLower(city), strings.ToLower(state))
	}

	return fmt.Sprintf(
		"location-lookup-%s-%s-%s",
		strings.ToLower(city),
		strings.ToLower(state),
		strings.ToLower(zipcode),
	)
}

// This function allows both lookups by city and state or zipcode, caching results in Redis through
// multiple keys so future lookups with limited information can always access it.
// https://docs.aws.amazon.com/location/latest/developerguide/search-place-index-geocoding.html
func AwsLocationLookup(
	ctx context.Context,
	city string,
	state string,
	zipcode string,
) (res *location.SearchPlaceIndexForTextOutput, err error) {
	// Only used for response logging for internal metrics
	integration := models.Integration{Type: "utility", Name: "aws-location"}

	redisKey := redisLocationLookupKey(ctx, city, state, zipcode)

	cachedLocationObj, err := redis.RDB.Get(ctx, redisKey).Result()
	if err == nil && cachedLocationObj != "" {
		cachedLocation := &location.SearchPlaceIndexForTextOutput{}
		if err = json.Unmarshal([]byte(cachedLocationObj), cachedLocation); err != nil {
			log.Warn(ctx, "failed to unmarshal AWS Location from Redis", zap.Error(err))
			return nil, err
		}

		cachedLocationCountry := cachedLocation.Results[0].Place.Country
		cachedLocationZip := cachedLocation.Results[0].Place.PostalCode
		if cachedLocationZip != nil && cachedLocationCountry != nil && *cachedLocationCountry == "USA" {
			formattedZip := formatUSZipcode(*cachedLocation.Results[0].Place.PostalCode)
			cachedLocation.Results[0].Place.PostalCode = &formattedZip
		}

		log.Debug(
			ctx,
			"found location in Redis",
			zap.String("redisKey", redisKey),
			zap.String("city", city),
			zap.String("state", state),
			zap.String("zipcode", zipcode),
		)
		return cachedLocation, nil
	}

	// clean up empty/bugged key
	if delErr := redis.RDB.Del(ctx, redisKey).Err(); delErr != nil {
		log.Error(
			ctx,
			"failed to clean up empty/bugged redis key",
			zap.String("redisKey", redisKey),
			zap.Error(delErr),
		)
	}

	log.Info(
		ctx,
		"location not found on redis, proceeding to lookup with AWS",
		zap.Any("redisKey", redisKey),
	)

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return nil, err
	}

	locationClient := location.NewFromConfig(cfg)

	locationPlaceIndex := "drumkit.place.here"
	locationLanguage := "en"

	// We can rely on AWS to match results even when city/state or zipcode is not provided.
	var lookupString string
	if zipcode == "" {
		lookupString = fmt.Sprintf("%s, %s", city, state)
	} else {
		if len(zipcode) == 6 {
			// put a space between the third and fourth digit since this is a Canadian zipcode
			lookupString = fmt.Sprintf("%s %s", zipcode[:3], zipcode[3:])
		} else {
			lookupString = zipcode
		}
	}

	log.Debug(ctx, "AWS Locations lookup string", zap.String("lookupString", lookupString))

	locationQuery := location.SearchPlaceIndexForTextInput{
		IndexName:       &locationPlaceIndex,
		Text:            &lookupString,
		FilterCountries: []string{"USA", "CAN"},
		Language:        &locationLanguage,
	}

	locationResult, err := locationClient.SearchPlaceIndexForText(ctx, &locationQuery)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, integration, err)
		return nil, err
	}

	if locationResult == nil || len(locationResult.Results) == 0 || locationResult.Results[0].PlaceId == nil {
		err404 := errtypes.EntityNotFoundError(integration, lookupString, "locationQuery")

		httplog.LogHTTPRequestFailed(ctx, integration, err404)

		return nil, fmt.Errorf("no results were found")
	}

	locationZip := locationResult.Results[0].Place.PostalCode
	locationCountry := locationResult.Results[0].Place.Country
	if locationZip != nil && locationCountry != nil && *locationCountry == "USA" {
		formattedZip := formatUSZipcode(*locationZip)
		locationResult.Results[0].Place.PostalCode = &formattedZip
	}

	httplog.LogHTTPResponseCode(ctx, integration, http.StatusOK)
	log.Info(
		ctx,
		"location lookup executed through AWS client",
		zap.Any("location summary", locationResult.Summary),
		zap.Any("location results", locationResult.Results),
	)

	locationSerializedBytes, err := json.Marshal(*locationResult)
	if err != nil {
		log.Warn(ctx, "failed to marshal AWS Location lookup", zap.Error(err))

		return nil, err
	}

	// We cache the location through different keys so lookups with only zipcode,
	// city/state or both always have a match.
	// We're allowed to store AWS Location results since our place index has an explicit intended use of "storage".
	locationPlace := locationResult.Results[0].Place

	postalCode := ""
	if locationPlace.PostalCode != nil {
		postalCode = *locationPlace.PostalCode
	}

	municipality := ""
	if locationPlace.Municipality != nil {
		municipality = *locationPlace.Municipality
	}

	region := ""
	if locationPlace.Region != nil {
		region = *locationPlace.Region
	}

	zipcodeKey := redisLocationLookupKey(ctx, "", "", postalCode)
	cityStateKey := redisLocationLookupKey(ctx, municipality, region, "")
	fullAddressKey := redisLocationLookupKey(ctx, municipality, region, postalCode)

	if err := redis.RDB.Set(ctx, zipcodeKey, string(locationSerializedBytes), 0).Err(); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", zipcodeKey),
		)
	} else {
		log.Info(ctx, "location stored successfully on redis", zap.Any("redisKey", zipcodeKey))
	}

	if err := redis.RDB.Set(ctx, cityStateKey, string(locationSerializedBytes), 0).Err(); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", cityStateKey),
		)
	} else {
		log.Info(ctx, "location stored successfully on redis", zap.Any("redisKey", cityStateKey))
	}

	if err := redis.RDB.Set(ctx, fullAddressKey, string(locationSerializedBytes), 0).Err(); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", fullAddressKey),
		)
	} else {
		log.Info(ctx, "location lookup stored successfully on redis", zap.Any("redisKey", fullAddressKey))
	}

	return locationResult, nil
}

// formatUSZipcode takes a zipcode string and returns a formatted 5-digit US zipcode if valid
// Returns the original string if it's not a valid US zipcode format
func formatUSZipcode(zipcode string) string {
	// Regular expression for US zipcode format xxxxx-xxxx
	usZipRegex := regexp.MustCompile(`^\d{5}-\d{4}$`)

	if usZipRegex.MatchString(zipcode) {
		// Extract just the first 5 digits
		return zipcode[:5]
	}

	return zipcode
}
