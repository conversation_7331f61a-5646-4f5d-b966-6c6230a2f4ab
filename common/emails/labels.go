package emails

import (
	"context"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/models"
)

// CategorySchema defines the top-level email categories for the first classification step
//
//nolint:lll
type CategorySchema struct {
	TrackAndTrace      bool   `json:"track_and_trace" jsonschema:"enum=true,enum=false" jsonschema_description:"Emails about tracking shipments, driver updates, delivery confirmations, carrier info, etc."`
	Scheduling         bool   `json:"scheduling" jsonschema:"enum=true,enum=false" jsonschema_description:"Emails about appointment scheduling or confirmation for pickups/deliveries"`
	Quoting            bool   `json:"quoting" jsonschema:"enum=true,enum=false" jsonschema_description:"Emails about pricing, quotes, bidding or rate information for shipping services"`
	CapacityManagement bool   `json:"capacity_management" jsonschema:"enum=true,enum=false" jsonschema_description:"Emails about available trucks, capacity, or equipment"`
	LoadBuilding       bool   `json:"load_building" jsonschema:"enum=true,enum=false" jsonschema_description:"Emails about creating new loads, orders, or shipments"`
	Reasoning          string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
}

// Specific label schemas for each category
//
//nolint:lll
type (
	TrackAndTrace struct {
		CheckCall            bool   `json:"check_call" jsonschema:"enum=true,enum=false" jsonschema_description:"Updates about load status (loaded/unloaded, arrival/departure at locations)"`
		PickupConfirmation   bool   `json:"pickup_confirmation" jsonschema:"enum=true,enum=false" jsonschema_description:"Confirms a pickup has occurred"`
		DriverInfo           bool   `json:"driver_info" jsonschema:"enum=true,enum=false" jsonschema_description:"Contains specific driver information (name, contact, license, etc.)"`
		TrackingETA          bool   `json:"tracking_eta" jsonschema:"enum=true,enum=false" jsonschema_description:"Contains specific tracking numbers or estimated arrival times"`
		DeliveryConfirmation bool   `json:"delivery_confirmation" jsonschema:"enum=true,enum=false" jsonschema_description:"Email confirms a delivery has occurred or been completed"`
		CarrierInfo          bool   `json:"carrier_info" jsonschema:"enum=true,enum=false" jsonschema_description:"Contains specific carrier information (truck/trailer details, carrier company info)"`
		Reasoning            string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
	}
	Scheduling struct {
		AppointmentScheduling bool   `json:"appointment_scheduling" jsonschema:"enum=true,enum=false" jsonschema_description:"Discusses scheduling a pickup or delivery appointment (not yet confirmed)"`
		AppointmentConfirmed  bool   `json:"appointment_confirmed" jsonschema:"enum=true,enum=false" jsonschema_description:"Confirms a specific appointment date/time is set (must include date)"`
		Reasoning             string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
	}
	Quoting struct {
		CarrierQuote bool   `json:"carrier_quote" jsonschema:"enum=true,enum=false" jsonschema_description:"Contains specific pricing for shipping services (must include dollar amounts)"`
		QuoteRequest bool   `json:"quote_request" jsonschema:"enum=true,enum=false" jsonschema_description:"Explicitly requests pricing for shipping services"`
		Reasoning    string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
	}
	CapacityManagement struct {
		TruckList bool   `json:"truck_list" jsonschema:"enum=true,enum=false" jsonschema_description:"Lists available trucks, often with dates, locations, or capacity information"`
		Reasoning string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
	}
	LoadBuilding struct {
		LoadBuilding bool   `json:"load_building" jsonschema:"enum=true,enum=false" jsonschema_description:"About creating/building a new load or order with specific details"`
		Reasoning    string `json:"reasoning" jsonschema_description:"Provide your reasoning for the classification"`
	}
)

// Category and label definitions
type (
	EmailCategory string
	EmailLabel    string
)

const (
	TrackAndTraceCategory      EmailCategory = "track_and_trace"
	SchedulingCategory         EmailCategory = "scheduling"
	QuotingCategory            EmailCategory = "quoting"
	CapacityManagementCategory EmailCategory = "capacity_management"
	LoadBuildingCategory       EmailCategory = "load_building"

	DeliveryConfirmationLabel  EmailLabel = "delivery confirmation"
	DriverInfoLabel            EmailLabel = "driver info"
	TrackingETALabel           EmailLabel = "tracking/eta"
	AppointmentSchedulingLabel EmailLabel = "appointment scheduling"
	PickupConfirmationLabel    EmailLabel = "pickup confirmation"
	AppointmentConfirmedLabel  EmailLabel = "appointment confirmed"
	CheckCallLabel             EmailLabel = "check call"
	CarrierQuoteResponseLabel  EmailLabel = "carrier quote response"
	QuoteRequestLabel          EmailLabel = "quote request"
	CarrierInfoLabel           EmailLabel = "carrier info"
	TruckListLabel             EmailLabel = "truck list"
	// NOTE: Because we have to be very permissive to capture load building emails as they can be in
	// any form, actions.go/runLoadBuildingExtract has a feedback loop where if no load building suggestion is found,
	// the label is removed to minimize false positives
	LoadBuildingLabel EmailLabel = "load building"
)

// Define a type for our category handler
type categoryHandler struct {
	extract func(
		ctx context.Context,
		openaiService openai.Service,
		email models.IngestedEmail,
		category EmailCategory,
		previousResponseID,
		promptTemplate string,
	) ([]string, string, error)
}

// Map of category names to their handler functions
var categoryHandlers = map[EmailCategory]categoryHandler{
	TrackAndTraceCategory: {
		extract: func(
			ctx context.Context,
			openaiService openai.Service,
			email models.IngestedEmail,
			category EmailCategory,
			previousResponseID,
			promptTemplate string,
		) ([]string, string, error) {

			return getLabelsForSpecificCategory(ctx, openaiService, email, category, previousResponseID, promptTemplate,
				func(r TrackAndTrace) map[EmailLabel]bool {
					return map[EmailLabel]bool{
						CarrierInfoLabel:          r.CarrierInfo,
						DeliveryConfirmationLabel: r.DeliveryConfirmation,
						DriverInfoLabel:           r.DriverInfo,
						TrackingETALabel:          r.TrackingETA,
						PickupConfirmationLabel:   r.PickupConfirmation,
						CheckCallLabel:            r.CheckCall,
					}
				})
		},
	},
	SchedulingCategory: {
		extract: func(
			ctx context.Context,
			openaiService openai.Service,
			email models.IngestedEmail,
			category EmailCategory,
			previousResponseID,
			promptTemplate string,
		) ([]string, string, error) {

			return getLabelsForSpecificCategory(ctx, openaiService, email, category, previousResponseID, promptTemplate,
				func(r Scheduling) map[EmailLabel]bool {
					return map[EmailLabel]bool{
						AppointmentSchedulingLabel: r.AppointmentScheduling,
						AppointmentConfirmedLabel:  r.AppointmentConfirmed,
					}
				})
		},
	},
	CapacityManagementCategory: {
		extract: func(
			ctx context.Context,
			openaiService openai.Service,
			email models.IngestedEmail,
			category EmailCategory,
			previousResponseID,
			promptTemplate string,
		) ([]string, string, error) {

			return getLabelsForSpecificCategory(ctx, openaiService, email, category, previousResponseID, promptTemplate,
				func(r CapacityManagement) map[EmailLabel]bool {
					return map[EmailLabel]bool{
						TruckListLabel: r.TruckList,
					}
				})
		},
	},
	LoadBuildingCategory: {
		extract: func(
			ctx context.Context,
			openaiService openai.Service,
			email models.IngestedEmail,
			category EmailCategory,
			previousResponseID,
			promptTemplate string,
		) ([]string, string, error) {

			return getLabelsForSpecificCategory(ctx, openaiService, email, category, previousResponseID, promptTemplate,
				func(r LoadBuilding) map[EmailLabel]bool {
					return map[EmailLabel]bool{
						LoadBuildingLabel: r.LoadBuilding,
					}
				})
		},
	},
}

// Prompt templates for each category
//
//nolint:lll
var (
	categoryClassificationPrompt = `
						You are an expert logistics email analyzer. Your task is to identify which freight logistics labels apply to this email.
						For each label, determine if it applies to this email. Only select labels that are clearly applicable.

						Here are the labels with detailed explanations and examples:

						1. TRACK AND TRACE: Emails about tracking shipments, driver updates, delivery confirmations, carrier info, and status updates
						   Examples:
						   - "Delivered - Load #PBC123" (Confirming delivery completion with POD)
						   - "Driver Details for Load #REF456" (Providing driver name, phone, etc.)
						   - "Update for Load #GHI789 - ETA 4:00 PM" (Providing tracking or ETA information)
						   - "Pickup Confirmed - Load #JKL012" (Confirming pickup has occurred)
						   - "Check Call - Load #MNO345 - Loaded and Departing" (Status updates about loading/unloading)
						   - "Carrier Info - PO #73829 - FastFreight Transport LLC" (Providing carrier information)

						2. SCHEDULING: Emails strictly about scheduling or confirming appointments for pickups or deliveries
						   Examples:
						   - "Appointment Request - Load #PQR678" (Requesting to set up delivery time)
						   - "Appointment Confirmed - Load #STU901 - April 11th at 10:00 AM" (Confirming specific appointment)

						3. QUOTING: Emails about pricing, quotes, or rates for shipping services that either:
									- Ask for shipping rates, or
									- Provide enough shipment detail (pickup/drop-off and equipment type) to calculate a rate, as these are often requests, even if words "quote" or "rate" are not used. Unless clearly meant for another purpose, treat said emails as Quoting.
								Examples:
									- "Rate Quote: Chicago to Memphis" (Providing specific pricing information)
									- "Request for Quote Mauser Galena Park" (Explicitly asking for pricing)
									- "Anniston, AL to Temple, TX 774 mi" (Implicitly requesting rates by informing lane)
									- "Van, Pickup: Flower Mound TX Jacksonville FL" (Implicitly requesting rates by informing lane and equipment)
									- "Dry Van, 44622 - 30102" (Implicitly requesting rates by informing lane postal codes and equipment)

						4. CAPACITY MANAGEMENT: Emails about available trucks, carriers, or equipment capacity
						   Examples:
						   - "Available Trucks - April 7th - Midwest Region" (Listing available equipment)
						   - "Truck Capacity for Next Week" (Information about available trucks/trailers)

						5. LOAD BUILDING: Emails that include a Bill Of Lading (BOL) for a new shipment or enough information to create a new shipment
						   Examples:
						   - "New Shipment Details - Origin: Atlanta, GA - Destination: Dallas, TX" (Creating new load)
						   - "Please create load for PO #12345" (Requesting creation of new shipment with details)
						   - "Bill of Lading #12345678" (Indicating a bill of lading is referenced)
						   - "Release - one van for pickup tomorrow"
						    	Order Date: 08/21/2024
						    	Due Date: 09/30/2024
						    	Ship From: Kronospan PB, LLC, Eastaboga, AL
						    	Ship To: MJB Anniston, Anniston, AL
						    	Customer PO: 5019494
						    	Product: 585 PC – 5/8" x 49" x 97" PBD Raw (Kronospan-Eastaboga)
						    	Weight: 41,842.73 lbs
						    	Total Pallets: 13
						    	Carrier Note: Load must be 100% tarped before departure
						   - "BOL attached" (Indicating a bill of lading is attached)
						   - "See load details below" (Indicating a bill of lading is included in the email body)

						For this email, think step-by-step:
						1. Analyze the email subject for keywords related to each label
						2. Analyze the attachments for information matching the labels above
						3. Examine the email body for information matching the labels above
						4. For each label, answer: Does this email clearly relate to this label?

						Only set true for labels that definitely match the email content. If uncertain, set false.
						Be strict in your classification to avoid false positives.`

	categorySpecificLabelPrompts = map[EmailCategory]string{
		TrackAndTraceCategory: `
							You are an expert logistics email analyzer. Your task is to identify specific track and trace labels that apply to this email.
							Only confirm labels that are clearly and definitively applicable to this email's content.

							Here is a list of track and trace labels and their definitions:
							- carrier info: Contains specific carrier information (truck/trailer details, carrier company info)
							  Example:
							  Subject: Carrier information for load #LTL53792

							  Hello,

							  Please find the carrier information for your load #LTL53792:

							  Carrier Name: FastFreight Transport LLC
							  MC#: 123456
							  DOT#: 7891011
							  Insurance Certificate: Attached
							  Truck #: T-789
							  Trailer #: TR-456
							  Equipment Type: 53' dry van

							  Driver Name: John Smith
							  Driver Phone: ************
							  Driver License #: **********

							  Please let me know if you need any additional information.

							  Best regards,
							  Carrier Relations

							- delivery confirmation: Email confirms a delivery has occurred or been completed
							  Example:
							  Subject: DELIVERED: Load #FTL22478 - Smith Manufacturing

							  Hello,

							  This is to confirm that load #FTL22478 for Smith Manufacturing has been delivered.

							  Delivery completed on: July 12, 2023 at 14:35 EST
							  POD Status: Signed by receiver
							  Receiver Name: Alice Johnson
							  All items delivered in good condition.

							  POD has been attached to this email.

							  Thank you for your business!

							  Best regards,
							  Delivery Management Team

							- driver info: Contains specific driver information (name, contact, license, etc.)
							  Example:
							  Subject: Driver information for tomorrow's pickup - Load #BK4501

							  Hello,

							  For tomorrow's pickup at your facility (Load #BK4501), our driver's information is as follows:

							  Driver Name: Michael Rodriguez
							  Cell Phone: ************
							  CDL #: *********
							  Photo ID: Attached

							  The driver is expected to arrive between 9:00-10:00 AM.
							  Please have the shipping documents ready.

							  Thank you,
							  Dispatch Team

							- tracking/eta: Contains specific tracking numbers or estimated arrival times
							  Example:
							  Subject: Tracking update for shipment #CS78923

							  Hello,

							  Here is the tracking update for your shipment #CS78923:

							  Current Status: In Transit
							  Current Location: Columbus, OH
							  Tracking Number: 1ZTFE456789102
							  Last Update: July 14, 2023 - 16:25 EST

							  ETA at destination: July 15, 2023 between 13:00-15:00 EST

							  The driver has reported good weather conditions and no delays expected.

							  Regards,
							  Tracking Department

							- pickup confirmation: Confirms a pickup has occurred
							  Example:
							  Subject: Pickup Confirmation - Order #ORD-98765

							  Hello,

							  This email confirms that your shipment (Order #ORD-98765) has been successfully picked up.

							  Pickup completed on: July 11, 2023 at 11:20 CST
							  Location: Your Dallas Warehouse
							  Bill of Lading #: BOL-123456
							  Items: 4 pallets (3,200 lbs)
							  All items in good condition.

							  The shipment is now in transit to the destination.

							  Best regards,
							  Operations Team

							- check call: Updates about load status (loaded/unloaded, arrival/departure at locations)
							  Example:
							  Subject: Check Call Update - Shipment #TR-56789

							  Hello,

							  Our driver has provided the following check call update for shipment #TR-56789:

							  Current Status: Loaded, in transit
							  Current Location: 20 miles east of Denver, CO
							  Weather conditions: Clear
							  Traffic: Light
							  ETA to next stop: 2 hours (estimated arrival at 15:30 MST)

							  Driver reports no issues with the freight or vehicle.

							  Next update will be provided upon arrival at the next checkpoint.

							  Regards,
							  Dispatch Team

							Only set true for labels that definitively match the email content. If uncertain, set false.`,

		SchedulingCategory: `
							You are an expert logistics email analyzer. Your task is to identify specific scheduling labels that apply to this email.
							Only confirm labels that are clearly and definitively applicable to this email's content.

							Here is a list of scheduling labels and their definitions:
							- appointment scheduling: Discusses scheduling a pickup or delivery appointment (not yet confirmed)
							  Example:
							  Subject: Request for delivery appointment - PO #45621

							  Hello,

							  We need to schedule a delivery appointment for the following shipment:

							  PO #: 45621
							  Shipper: ABC Manufacturing
							  Consignee: XYZ Distribution Center
							  Freight: 8 pallets, 6,500 lbs
							  Dimensions: 48"x40"x60" each

							  Preferred delivery dates:
							  - July 18, 2023 (8:00 AM - 2:00 PM)
							  - July 19, 2023 (8:00 AM - 2:00 PM)
							  - July 20, 2023 (8:00 AM - 2:00 PM)

							  Please confirm which date and time works best for your receiving department.

							  Thank you,
							  Scheduling Team

							- appointment confirmed: Confirms a specific appointment date/time is set (must include date)
							  Example:
							  Subject: Appointment Confirmed: Delivery on 07/22/2023

							  Hello,

							  Your delivery appointment has been confirmed:

							  Load #: LTL-87654
							  Appointment Date: 07/22/2023
							  Appointment Time: 10:00 AM EST
							  Facility: Central Distribution Center
							  Address: 1234 Warehouse Ave, Chicago, IL 60007
							  Reference #: PO-56789
							  Dock Door: #14

							  Please ensure the driver arrives 15 minutes before the appointment time and has all necessary paperwork.

							  Let us know if you need to reschedule at least 24 hours in advance.

							  Regards,
							  Appointment Desk

							Only set true for labels that definitively match the email content. If uncertain, set false.`,

		CapacityManagementCategory: `
							You are an expert logistics email analyzer. Your task is to identify if this email contains truck list information.
							Only confirm if it is clearly and definitively applicable to this email's content.

							Truck list definition:
							- truck list: Lists available trucks, often with dates, locations, or capacity information
							  Example:
							  Subject: Available trucks for week of 07/24 - Northeast Region

							  Hello partners,

							  Here is our list of available trucks for the week of 07/24/2023:

							  Monday, 07/24:
							  - 3x 53' Dry Vans in Boston, MA
							  - 2x Reefers in Albany, NY
							  - 1x Flatbed in Trenton, NJ

							  Tuesday, 07/25:
							  - 5x 53' Dry Vans in Philadelphia, PA
							  - 3x Reefers in Hartford, CT
							  - 2x Flatbeds in Pittsburgh, PA

							  Wednesday, 07/26:
							  - 4x 53' Dry Vans in Newark, NJ
							  - 2x Reefers in Providence, RI
							  - 3x Flatbeds in Harrisburg, PA

							  All equipment is available for long-haul or regional routes. Please contact our dispatch team to secure capacity.

							  Regards,
							  Fleet Management

							Only set true if it definitively matches the email content. If uncertain, set false.`,

		LoadBuildingCategory: `
							You are an expert logistics email analyzer. Your task is to identify if this email contains load building information.
							Only confirm if it is clearly and definitively applicable to this email's content.

							Load building definition:
							- load building: About creating/building a new load or order with specific details
							  Example 1:
							  Subject: New shipment details - please create order

							  Hello,

							  Please create a new load with the following details:

							  Customer: Acme Tools Inc.
							  Customer PO#: PO-78901
							  BOL#: BOL123456

							  Origin:
							  Acme Manufacturing
							  123 Factory Lane
							  Detroit, MI 48201
							  Contact: John Doe (313-555-1234)
							  Pickup Date: 07/28/2023, 08:00-12:00

							  Destination:
							  Acme Distribution Center
							  456 Warehouse Blvd
							  Cleveland, OH 44115
							  Contact: Jane Smith (216-555-5678)
							  Delivery Date: 07/29/2023, 09:00-15:00

							  Freight Details:
							  - 10 pallets of machinery parts
							  - Total weight: 12,500 lbs
							  - Dimensions: 48"x40"x60" each
							  - Hazmat: No
							  - Temperature requirements: None
							  - Special instructions: Liftgate needed at delivery

							  Equipment needed: 53' Dry Van

							  Please confirm once the load is created in the system.

							  Thank you,
							  Logistics Coordinator

							  Example 2:
							  Subject: Create load for Release #93986

							  Release #: 93986

							  PICKUP AT:
							  Warehouse: CBC WAREHOUSE, LLC
							  Address: 986 B BOURNE AVENUE
							  SAVANNAH, GA 31408
							  912-964-1441
							  ATTN: Bill Barbee

							  SHIP TO:
							  ESCO INDUSTRIES
							  2001 LASSO LANE
							  LAKELAND, FL 33801
							  MITCH ROGERS
							  863-666-3696

							  CARGO DETAILS:
							  - 12 CRATES of 3.4 48X96 MERANTI (DBB/CC/OVL and DUTIL)
							  - Reference: 20841SASA-1
							  - Vessel: CMA CGM CHRISTOPHE COLOMB 1TU7
							  - Customer P.O.: 18324
							  - TOTAL CRATES AND PIECES: 12
							  - TOTAL WEIGHT: 3120 lbs

							  RECEIVING HOURS: MON-THURS 7:00 AM TO 3:30 PM, FRIDAY 7:00 AM TO 10:30 AM
							  NO APPT NECESSARY, CAN ACCOMMODATE FLATS AND VANS.

							  PICKUP REQUIREMENTS:
							  - 24 HOUR NOTICE REQUIRED NO SAME DAY APPTS
							  - CARGO MUST BE 100% TARPED BEFORE SHIPPING

							  Dispatch Date: 9/10/2024

							  Please create this load in the system as soon as possible.

							  Thank you,
							  Operations Team

							Only set true if it definitively matches the email content. If uncertain, set false.`,
	}
)

// ReasoningProvider is an interface for types that provide (LLM) reasoning in their output struct.
// Use/extend this interface when working with generic functions.
type ReasoningProvider interface {
	GetReasoning() string
}

// Methods to implement ReasoningProvider interface
func (t TrackAndTrace) GetReasoning() string {
	return t.Reasoning
}

func (s Scheduling) GetReasoning() string {
	return s.Reasoning
}

func (q Quoting) GetReasoning() string {
	return q.Reasoning
}

func (c CapacityManagement) GetReasoning() string {
	return c.Reasoning
}

func (l LoadBuilding) GetReasoning() string {
	return l.Reasoning
}
