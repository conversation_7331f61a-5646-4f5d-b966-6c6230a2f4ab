package emails

import (
	"context"
	"errors"
	"regexp"
	"slices"
	"strings"
	"unicode"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	genemailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	"github.com/drumkitai/drumkit/common/util/otel"
)

type (
	WordPresenceParam struct {
		// Groups of words, where each group is a list of synonyms
		WordGroups [][]string
		// Number of word groups that must be matched. 0 < n <= len(WordGroups).
		// If invalid number, function defaults to len(WordGroups)
		ConfidenceThreshold uint
	}
)

var (
	//nolint:lll
	genericLabelPatterns = map[string]*regexp.Regexp{
		string(DeliveryConfirmationLabel):  regexp.MustCompile(`(?i)delivery.*confirmed|confirmed.*delivery|confirm.*delivered|confirm*.driver`),
		string(DriverInfoLabel):            regexp.MustCompile(`(?i)driver.*name|driver.*info|license.*number`),
		string(TrackingETALabel):           regexp.MustCompile(`(?i)tracking\s*number\s*:\s*\w+|eta\s+(?:\d{1,2}:\d{2}|\d{1,2}[ap]?m?-?\d{0,2}:[ap]m?|\d+\s*min)`),
		string(AppointmentSchedulingLabel): regexp.MustCompile(`(?i)(appointment|Pick Up|P(/?)U Appt|P(-|/)?U\s+Request|pick(-|/|\s+)?up\s+(appt|appointment)|\bPU\b)`),
		string(PickupConfirmationLabel):    regexp.MustCompile(`(?i)pickup.*confirmed|confirmed.*pickup`),
		string(AppointmentConfirmedLabel):  regexp.MustCompile(`(?i)((pickup|pick-up|pick up|appointment).*confirmed|confirmed.*(pickup|pick-up|pick up|appointment))(?s:.)*\s(([0-9]{1,2}\/[0-9]{1,2})|([0-9]{1,2}-[0-9]{1,2}))`),
		string(CheckCallLabel): regexp.MustCompile(`(?i)(load(ed|ing)|unload(ed|ing)|(?:at)\s*(?:the\s+)?(pick(?:-|\/|\s+)?up|shipper|warehouse|delivery|consignee|drop(?:-|\/|\s+)?off)|pick(?:ed|ing)(?:-|\s+)?up|dropp(?:ed|ing)(?:-|\s+)?off|` +
			`deliver(?:ed|ing)|check(?:ed|ing)?(?:-|\s*)?(in|out)|(in|out)\s*:\s*\d+|arrived\s*(at|:)|current\s+location\s*[^?]|(location|transit)\s+update)`),
		// NOTE: Label limited to emails within automated carrier network quoting (see categorizeByRegex())
		string(CarrierQuoteResponseLabel): regexp.MustCompile(`(?i)(?:USD|\$|dollars?)?\s*(\d{3,}\s{0,2}(?:,\s*\d{3})*(?:\.\d{1,2})?)\s*(?:USD|\$|dollars?)?`),
	}

	// Go regex doesn't support the forward look (?=) to search for multiple words presence in any order,
	// so we use a custom fn for those cases
	multipleWordPresencePatterns = map[string]WordPresenceParam{
		string(CarrierInfoLabel): {
			WordGroups: [][]string{
				{"driver", "drivers"},
				{"truck", "trk"},
				{"trailer", "trl", "trailers", "trlr"}},
			ConfidenceThreshold: 3,
		},
		string(QuoteRequestLabel): {
			WordGroups: [][]string{
				// Rate terms
				{"rate", "rates", "quote", "quotes", "price", "prices", "cost", "costs", "estimate", "estimates",
					"rfq", "rfqs", "spot", "load", "shipment", "fuel", "surcharge", "surcharges"},

				// Transport type
				{"truck", "trucks", "hotshot", "hotshots", "hot", "shot", "flatbed", "flatbeds", "flat", "tarp",
					"tarps", "tarped", "van", "vans", "reefer", "reef", "box"},

				// Key shipment specs
				{"weight", "lbs", "kg", "pounds", "pallet", "pallets", "crate", "crates",
					"box", "boxes", "tarp", "tarped"},

				{"pickup", "delivery", "deliver", "pickup", "pickups", "pick-up", "delivery",
					"deliveries", "dropoff", "drop-off", "from"},

				// Verb/Interrogation
				{"spot", "request", "requests", "confirm", "give", "provide", "need", "want", "tell", "send",
					"get", "would", "can", "could", "please", "plz", "much", "on", "advise"},
			},
			ConfidenceThreshold: 2,
		},
		// In addition to this word presence test, all emails with attachments initially labelled as load building,
		// then we perform checks to verify label
		string(LoadBuildingLabel): {
			WordGroups: [][]string{
				{"release", "releases", "orders", "order", "shipment", "shipping", "load", "loads", "rate", "rates",
					"attached", "confirm", "please", "send", "shipper", "consignee", "delivery", "deliver", "pickup",
					"driver", "weight", "tender", "tendered", "booking"},
			},
			ConfidenceThreshold: 1,
		},
		string(TruckListLabel): {
			WordGroups: [][]string{
				{
					"available", "availability", "schedule", "schedules", "open", "opening", "openings", "empty",
					"left", "remaining", "returning", "return", "returns",
					"list", "lists", "listed", "backhaul", "backhauls", "back-haul", "back-hauls",
					"today", "tomorrow", "truck", "trucks", "driver", "drivers", "van", "vans", "reefer", "reefers",
					"flatbed", "flatbeds", "capacity", "capacities"},
			},
			ConfidenceThreshold: 1,
		},
	}
)

func ClassifyWithRegex(
	ctx context.Context,
	msg models.IngestedEmail,
	options *Options,
) ([]string, models.ClassificationApproach, string, string) {

	labels, sure := categorizeByRegex(ctx, msg, options)
	if sure {
		return labels, models.RegexApproach, "", ""
	}

	return labels, models.RegexApproach, "", ""
}

func categorizeByRegex(ctx context.Context, msg models.IngestedEmail, options *Options) ([]string, bool) {
	ctx, metaSpan := otel.StartSpan(ctx, "categorizeByRegex", nil)
	defer func() { metaSpan.End(nil) }()

	matchedLabels := labelEmail(ctx, msg, options)
	// If no other categories matched, categorize as "other".
	if len(matchedLabels) == 0 {
		return []string{"other"}, false
	}

	// If labelled quote request, verify it's not just the user asking carriers for rates in carrier network flow
	// Prevents FE from defaulting to Quote View
	if slices.Contains(matchedLabels, string(QuoteRequestLabel)) {
		// When the service is nil, Drumkit is running in an on-premise environment that does not include the
		// GeneratedEmails table, allowing us to skip the logic in this branch.
		if options.Service != nil {
			dbEmail, err := genemailDB.GetEmailAndQuoteRequestByThreadID(ctx, msg.ThreadID)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					log.WarnNoSentry(
						ctx,
						"error getting email and quote requests from DB",
						zap.Error(err),
					)

					return matchedLabels, true
				}
			}

			if len(dbEmail.QuoteRequests) > 0 {
				log.Debug(ctx, "thread is user requesting quote from carrier network, removing 'quote request' label")

				matchedLabels = slices.DeleteFunc(matchedLabels, func(s string) bool {
					return s == string(QuoteRequestLabel)
				})
			}
		}
	}

	// If there are loads associated with the email, remove the "load building" label
	// Quote request emails may mention shipment values; these should not be interpreted as quote emails
	// (see TestQuoteRequestLabeling - OK 3 for example)
	if slices.Contains(matchedLabels, string(QuoteRequestLabel)) {
		matchedLabels = slices.DeleteFunc(matchedLabels, func(s string) bool {
			res := s == string(CarrierQuoteResponseLabel)
			if res {
				log.Debug(ctx, "removing 'carrier quote response' label from quote request email")
			}

			return res
		})
	}

	// If carrier quote email, verify it's part of carrier quoting flow
	if slices.Contains(matchedLabels, string(CarrierQuoteResponseLabel)) {
		// When the service is nil, Drumkit is running in an on-premise environment that does not include the
		// GeneratedEmails table, allowing us to skip the logic in this branch.
		if options.Service != nil {
			dbEmail, err := genemailDB.GetEmailAndQuoteRequestByThreadID(ctx, msg.ThreadID)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.WarnNoSentry(ctx, "error getting email and quote requests from DB", zap.Error(err))

				return matchedLabels, true
			}

			if (err != nil && errors.Is(err, gorm.ErrRecordNotFound)) || len(dbEmail.QuoteRequests) == 0 {
				log.Debug(ctx, "thread is not part of carrier quoting flow, removing 'carrier quote response' label")

				matchedLabels = slices.DeleteFunc(matchedLabels, func(s string) bool {
					return s == string(CarrierQuoteResponseLabel)
				})
			}
		}
	}

	return matchedLabels, true
}

func labelEmail(ctx context.Context, msg models.IngestedEmail, options *Options) []string {
	content := msg.Subject + "\n" + msg.Body
	patterns := getRegexPatterns(ctx, msg)
	if patterns == nil {
		return nil
	}

	var result []string

	// Check regex patterns
	for label, pattern := range patterns {
		if pattern.MatchString(content) {
			result = append(result, label)
		}
	}

	// Check multiple word presence
	for label, wordList := range multipleWordPresencePatterns {
		present := checkMultipleWordPresence(content, wordList)

		log.Debug(
			ctx,
			"word presence test",
			zap.Bool("present", present),
			zap.String("label", label),
			zap.Any("list", wordList),
		)

		if present {
			result = appendUnique(result, label)
		}
	}

	// Add LoadBuilding and QuoteRequest labels if PDFs exist and the corresponding service feature is enabled
	if (options.Service != nil &&
		(options.Service.IsLoadBuildingEnabled || options.Service.IsQuickQuoteEnabled)) &&
		msg.HasPDFs {

		result = appendUnique(result, string(LoadBuildingLabel))
		result = appendUnique(result, string(QuoteRequestLabel))
	}

	// Check for truck list patterns
	if isTruckListEmail(content) {
		result = appendUnique(result, string(TruckListLabel))
	}

	// Reduce quote request false positives
	if shouldRemoveQuoteRequestLabel(options) {
		result = removeLabel(result, QuoteRequestLabel)
	}

	// Handle special case for Trident Transport
	if shouldRemoveLoadBuilding(ctx, msg, options) {
		result = removeLabel(result, LoadBuildingLabel)
	}

	return result
}

// Get regex patterns to use for classification, which depend on the account
func getRegexPatterns(ctx context.Context, msg models.IngestedEmail) map[string]*regexp.Regexp {
	domain := strings.ToLower(strings.Split(msg.Account, "@")[1])

	//nolint:lll
	truckListPattern := `(?i)(available|list(ed)?|open|back.*haul)\s*(trucks?|drivers?|vans?|reefers?|capacity|trucking)?`

	switch domain {
	case "redwoodlogistics.com", "drumkit.ai", "axleapi.com", "78n517.onmicrosoft.com":
		genericLabelPatterns[string(TruckListLabel)] = regexp.MustCompile(truckListPattern)
		return genericLabelPatterns

	case "nfiindustries.com", "absolutetranz.com", "wickerparklogistics.com", "ncmcfreight.com", "tridenttransport.com":
		return genericLabelPatterns

	default:
		log.WarnNoSentry(ctx, "using generic email labels for unknown domain", zap.String("domain", domain))
		return genericLabelPatterns
	}
}

func isTruckListEmail(content string) bool {
	// Match `city, state` patterns
	// Must start with a letter, be at least 2 characters long, and can contain punctuation
	// but not numbers or special chars. State is 2-letter abbreviation
	cityStatePattern := regexp.MustCompile(`(?mi)^.*([A-Za-z][A-Za-z\s'.-]{1,})[,\-]\s*([A-Za-z]{2})$`)

	// Match standalone dates like 7/11, 07/11, 7-11, or 7.11 at the start of lines
	mmddString := `\d{1,2}[/\-\.]\d{1,2}`
	datePattern := regexp.MustCompile(`(?m)^\s*\d{1,2}[/\-\.]\d{1,2}\s*(?:\n|$)`)

	weekdayList := `Mon(day)?|Tue(sday)?|Wed(nesday)?|Thu(rs)?(day)?|Fri(day)?|Sat(urday)?|Sun(day)?`
	//nolint:lll
	monthList := `Jan(uary)?|Feb(ruary)?|Mar(ch)?|Apr(il)?|May|Jun(e)?|Jul(y)?|Aug(ust)?|Sep(tember)?|Oct(ober)?|Nov(ember)?|Dec(ember)?`

	// Match dates at start of lines, optionally followed by a weekday OR
	// Match weekdays at start of lines, optionally followed by a month AND date
	weekdayPattern := regexp.MustCompile(`(?mi)^\s*(?:` + weekdayList + `)\s+(` + mmddString + `)?|` +
		`(` + mmddString + `)?\s+(` + weekdayList + `)|(` + monthList + `\s+\d{1,2})\s+(` + weekdayList + `)?|((` +
		weekdayList + `)?\s+(` + monthList + `)\s+\d{1,2})$`)
	// Match HTML table tag
	hasTable := strings.Contains(content, "<table>")

	dates := datePattern.FindAllString(content, -1)
	weekdayMatches := weekdayPattern.FindAllString(content, -1)
	cityStateMatches := cityStatePattern.FindAllString(content, -1)

	// Must have at least 2 matches of either pattern
	return len(dates) >= 2 || len(weekdayMatches) >= 2 || len(cityStateMatches) >= 2 || hasTable
}

func appendUnique(slice []string, item string) []string {
	if !slices.Contains(slice, item) {
		return append(slice, item)
	}

	return slice
}

func shouldRemoveLoadBuilding(ctx context.Context, msg models.IngestedEmail, options *Options) bool {
	// These labels trigger SidebarView logic in the FE, so remove them if the service is not enabled
	if options != nil && (options.Service == nil || !options.Service.IsLoadBuildingEnabled) {
		log.Info(ctx, "load building disabled, removing label")
		return true
	}

	domain := strings.ToLower(strings.Split(msg.Account, "@")[1])
	if !strings.Contains(domain, "tridenttransport") {
		return false
	}

	subject := strings.ToLower(msg.Subject)

	// False positives. Rate confirmations are sent to carriers for *existing* loads and contain attachments
	// that look like a load building email. EDI load tender alerts are loads sent directly to the TMS and
	// contain no info in the email body.
	shouldRemove := strings.Contains(subject, "rate confirmation for order") ||
		strings.Contains(subject, "edi load tender alert") ||
		// Automated emails from Mcleod alerting of order status update (delivered, voided) and contain
		// PDF of *existing* load
		strings.Contains(subject, "order status alert")

	if shouldRemove {
		log.Info(ctx, "rate confirmation or EDI load tender alert, removing load building label")
	}

	return shouldRemove
}

func shouldRemoveQuoteRequestLabel(options *Options) bool {
	return (options != nil && len(options.EmailLoads) > 0) || (options != nil && options.Service != nil &&
		// These labels trigger SidebarView logic in the FE, so remove them if the service is not enabled
		!options.Service.IsQuickQuoteEnabled && !options.Service.IsCarrierNetworkQuotingEnabled)
}

func checkMultipleWordPresence(text string, param WordPresenceParam) bool {

	// Convert the body of text to lowercase for case-insensitive comparison
	text = strings.ToLower(text)

	// Create a map to store the presence of words in the body of text
	wordMap := make(map[string]bool)

	// Add words from the body of text to the map
	words := strings.FieldsFunc(text, func(r rune) bool {
		// Split words on whitespace and punctuation
		return !unicode.IsLetter(r) && !unicode.IsNumber(r)
	})

	for _, word := range words {
		wordMap[word] = true
	}

	// Check if target words are present in the map
	var groupsMatched uint
	for _, group := range param.WordGroups {
		for _, target := range group {
			if wordMap[strings.ToLower(target)] {
				groupsMatched++
				break
			}
		}

	}

	threshold := param.ConfidenceThreshold
	// If invalid threshold, default to matching all word groups
	if length := uint(len(param.WordGroups)); threshold > length || threshold <= 0 {
		threshold = length
	}

	return groupsMatched >= threshold

}

func removeLabel(labels []string, labelToRemove EmailLabel) []string {
	return slices.DeleteFunc(labels, func(s string) bool {
		return s == string(labelToRemove)
	})
}
