package emails

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/models"
)

func TestGetMcleodIDsFromEmail(t *testing.T) {
	tmsIntegration := models.Integration{Name: models.McleodEnterprise}

	t.Run("Extract load IDs correctly", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "0792351 for your request",
			Body:    "Please refer to 0792351 for details.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: mcleodenterprise.LoadIDType, ID: "0792351"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "0792351"},
		}

		require.Len(t, result, 2)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID})
		assert.Equal(t, tmsIntegration, result[0].integration)
		assert.Equal(t, tmsIntegration, result[1].integration)
	})

	t.Run("Remove URLs for RefNumberIDType", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "Random email",
			Body:    "Visit https://example.com/REF-12345 for details.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 0)
	})

	t.Run("Skip IDs without numbers for RefNumberIDType", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "REFERENCE",
			Body:    "The ID is REFERENCE.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 0)
	})

	t.Run("Handle mixed ID types", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "LOAD-54321 and Movement 654321",
			Body:    "Additional info: REF-09876 and https://example.com/URLREF-3532.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: mcleodenterprise.RefNumberIDType, ID: "LOAD-54321"},
			{IDType: mcleodenterprise.MovementIDType, ID: "654321"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "654321"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "REF-09876"},
		}

		require.Len(t, result, 4)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID,
			result[2].FreightID, result[3].FreightID})
	})

	t.Run("Deduplicate IDs", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "LOAD-11111 and LOAD-11111",
			Body:    "Repeated IDs: LOAD-11111.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 1)
		assert.Equal(t, "LOAD-11111", result[0].FreightID.ID)
	})
}
