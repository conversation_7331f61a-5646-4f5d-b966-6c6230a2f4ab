package emails

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/api/googleapi"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	rulesDB "github.com/drumkitai/drumkit/common/rds/rules"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/util/otel"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

var ForwardedMessageTTL = 72 * time.Hour

// getRedisKeyForwardedThread returns the Redis key for a thread that has forwarded at least one message.
func getRedisKeyForwardedThread(ruleID uint, threadID string) string {
	return fmt.Sprintf("forwarded-rule-%d-thread-%s", ruleID, threadID)
}

// getRedisKeyForwardedMessage returns the Redis key for a message that is either
// in the process of being forwarded or has been forwarded to prevent duplicate
// forwarding.
func getRedisKeyForwardedMessage(ruleID uint, msgExternalID string) string {
	return fmt.Sprintf("forwarded-rule-%d-external-id-%s", ruleID, msgExternalID)
}

func getRedisKeyForwardedRFC(ruleID uint, genEmailRFC string) string {
	return fmt.Sprintf("forwarded-rule-%d-generated-rfc-id-%s", ruleID, genEmailRFC)
}

// processForwardingRules checks if an email should be forwarded based on its labels and categories
// and forwards it to the appropriate recipients if needed.
func processForwardingRules(ctx context.Context, email models.Email, service *models.Service) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "processForwardingRules", nil)
	defer func() { metaSpan.End(nil) }()

	if service == nil || !service.IsEmailForwardingEnabled {
		return nil
	}

	rules, err := rulesDB.GetForwardingRulesByUserAndService(ctx, email.UserID, service.ID)
	if err != nil {
		return fmt.Errorf("error getting email forwarding rules: %w", err)
	}

	if len(rules) == 0 {
		log.Info(ctx, "no forwarding rules found for service", zap.Uint("serviceID", service.ID))
		return nil
	}

	var emailLabels []string
	if email.Labels != "" {
		emailLabels = strings.Split(email.Labels, ",")
	}

	// Check each rule
	for _, rule := range rules {
		ctx = log.With(ctx, zap.Uint("ruleID", rule.ID), zap.String("ruleDescription", rule.Description))

		shouldForward := false

		if rule.UserID != nil && *rule.UserID != email.UserID {
			log.Info(ctx, "rule is not applicable to this user", zap.Uint("ruleID", rule.ID))
			continue
		}

		// Check if thread has been forwarded by this rule before
		isForwarded, err := wasThreadForwarded(ctx, email.ThreadID, rule.ID)
		if err != nil {
			log.Error(ctx, "error checking if thread was forwarded", zap.Error(err))
		} else if isForwarded && rule.ForwardSubsequentEmailsInThread {
			log.Info(ctx, "thread was previously forwarded, forwarding email", zap.String("threadID", email.ThreadID))

			err := forwardEmail(ctx, email, &rule, false)
			if err != nil {
				log.Error(
					ctx,
					"critical: error forwarding subsequent email in thread",
					zap.Error(err),
					zap.String("threadToForward", email.ThreadID),
				)
			}

			continue
		}

		// Categories take precedence over labels
		if len(rule.EmailCategories) > 0 {
			for _, ruleCategory := range rule.EmailCategories {
				if ruleCategory == "all" {
					shouldForward = true
					break
				}
				categoryLabels := GetAllLabelsForCategory(EmailCategory(ruleCategory))
				if categoryLabels == nil {
					log.Warn(ctx, "no labels found for category in rule", zap.String("ruleCategory", ruleCategory))
					continue
				}

				// Check if any of the email's labels match the category's labels
				for _, label := range emailLabels {
					if slices.Contains(categoryLabels, EmailLabel(label)) {
						shouldForward = true
						break
					}
				}
			}
		} else if len(rule.EmailLabels) > 0 {
			// Check if any of the email's labels match the rule's labels
			for _, label := range emailLabels {
				if label == "all" {
					shouldForward = true
					break
				}
				if slices.Contains(rule.EmailLabels, label) {
					shouldForward = true
					break
				}
			}
		}

		if shouldForward {
			err := forwardEmail(ctx, email, &rule, true)
			if err != nil {
				log.Error(ctx, "error forwarding email", zap.Error(err))
				continue // Continue with other rules even if one fails
			}

		}
	}

	return nil
}

// forwardEmail checks if an email should be forwarded and forwards it if needed to the specified recipients.
func forwardEmail(
	ctx context.Context,
	email models.Email,
	rule *models.EmailForwardingRule,
	addFwdPrefix bool,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "forwardEmail", nil)
	defer func() { metaSpan.End(err) }()

	skip, err := skipDuplicateForward(ctx, email.ExternalID, rule.ID)
	if err != nil {
		log.Error(ctx, "error checking if message was previously forwarded, could lead to duplicates", zap.Error(err))
	} else if skip {
		log.Info(ctx, "message was previously forwarded, skipping")
		return nil
	}

	skip, err = skipSelfForward(ctx, email.UserID, email.RFCMessageID, rule.ID)
	if err != nil {
		log.Error(ctx, "error checking if message was self-forwarded, could lead to duplicates", zap.Error(err))
	} else if skip {
		log.Info(ctx, "message was self-forwarded, skipping")
		return nil
	}

	log.Info(ctx, "forwarding email based on rule")

	// Prevent duplicate forwarding by locking message in Redis
	redisErr := redis.SetKeyWithRetries(
		ctx,
		getRedisKeyForwardedMessage(rule.ID, email.ExternalID),
		"true",
		ForwardedMessageTTL,
	)
	if redisErr != nil {
		log.Error(ctx, "error setting redis for forwarded message, could lead to duplicates", zap.Error(redisErr))
	}

	user, err := rds.GetUserByID(ctx, email.UserID)
	if err != nil {
		return fmt.Errorf("error getting user: %w", err)
	}

	if user.EmailProvider != models.GmailEmailProvider {
		return fmt.Errorf("forwarding not yet supported for non-Gmail email providers")
	}

	client, err := gmailclient.New(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, &user)
	if err != nil {
		return fmt.Errorf("error creating Gmail client: %w", err)
	}

	// Initialize generated email for forwarding
	genEmail := &models.GeneratedEmail{
		ServiceID:                  user.ServiceID,
		UserID:                     user.ID,
		Recipients:                 rule.Recipients,
		CC:                         rule.CCRecipients,
		Sender:                     user.EmailAddress,
		TriggeredByRuleID:          &rule.ID,
		TriggeredByUserID:          user.ID,
		IsForward:                  true,
		ForwardedMessageID:         &email.ID,
		ForwardedMessageExternalID: email.ExternalID,
		ForwardedMessageThreadID:   email.ThreadID,
	}

	var forwardErr error
	var allErrs error
	backoffs := []time.Duration{time.Second, 3 * time.Second, 10 * time.Second}
	for i := 0; i < 3; i++ {
		_, forwardErr = client.ForwardMessage(
			ctx,
			email.ExternalID,
			genEmail,
			rule.UseSenderSignature,
			addFwdPrefix,
		)
		if forwardErr == nil {
			break
		}

		var httpErr *googleapi.Error
		if errors.As(forwardErr, &httpErr) && httpErr.Code >= 500 {
			allErrs = errors.Join(allErrs, forwardErr)
			if i < 2 {
				time.Sleep(backoffs[i])
			}
			continue
		}
		// Not a retryable error, break
		allErrs = errors.Join(allErrs, forwardErr)
		break
	}

	if forwardErr != nil {
		// Insert in DB for later retry
		log.Error(ctx, "critical: error forwarding message after retries", zap.Error(allErrs))
		genEmail.ScheduleSend = models.NullTime{Time: time.Now(), Valid: true}
		genEmail.Status = models.FailedStatus

		redisErr := redis.DeleteKey(ctx, getRedisKeyForwardedMessage(rule.ID, email.ExternalID))
		if redisErr != nil {
			log.Warn(ctx, "error deleting redis for failed forward", zap.Error(redisErr))
		}

		dbErr := genEmailDB.BatchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{genEmail})
		if dbErr != nil {
			log.Error(ctx, "error inserting forward email into DB", zap.Error(dbErr))
		}

		return fmt.Errorf("error forwarding message after retries: %w", allErrs)
	}

	log.Info(
		ctx,
		"email successfully forwarded based on rule",
		zap.Strings("recipients", rule.Recipients),
		zap.Strings("ccRecipients", rule.CCRecipients),
	)

	err = genEmailDB.BatchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{genEmail})
	if err != nil {
		log.Error(ctx, "error inserting forward email into DB", zap.Error(err))
	}

	// Cache the forwarded message ID in Redis for later check
	err = redis.RDB.Set(
		ctx,
		getRedisKeyForwardedMessage(rule.ID, email.ExternalID),
		genEmail.ExternalID,
		ForwardedMessageTTL,
	).Err()
	if err != nil {
		log.Warn(ctx, "error caching forwarded message ID in redis", zap.Error(err))
	}

	// Cache the forwarded thread ID in Redis for rule.ForwardSubsequentEmailsInThread
	err = redis.RDB.Set(
		ctx,
		getRedisKeyForwardedThread(rule.ID, email.ThreadID),
		time.Now().Unix(),
		ForwardedMessageTTL,
	).Err()
	if err != nil {
		log.Warn(ctx, "error caching forwarded thread ID in redis", zap.Error(err))
	}

	// Cache generated RFC for `skipSelfForward` logic
	err = redis.RDB.Set(
		ctx,
		getRedisKeyForwardedRFC(rule.ID, genEmail.RFCMessageID),
		genEmail.ExternalID,
		ForwardedMessageTTL,
	).Err()
	if err != nil {
		log.Warn(ctx, "error caching forwarded RFC in redis", zap.Error(err))
	}

	return nil
}

// skipDuplicateForwarding checks if a message has been forwarded before
// by first checking Redis and then RDS as a fallback
func skipDuplicateForward(ctx context.Context, msgExternalID string, ruleID uint) (bool, error) {
	_, redisErr := redis.RDB.Get(ctx, getRedisKeyForwardedMessage(ruleID, msgExternalID)).Result()
	if redisErr == nil {
		return true, nil
	}

	if !errors.Is(redisErr, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for forwarded message, falling back to RDS", zap.Error(redisErr))
	}

	// Fallback to RDS
	genEmail, err := genEmailDB.GetByForwardedMessageID(ctx, msgExternalID, ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("error checking RDS for forwarded message: %w", err)
	}

	return genEmail != nil && genEmail.ID != 0, nil
}

// skipSelfForward checks if the email is one the user already forwarded to him/herself
// and therefore should not duplicate forwarding. This is particularly helpful for when processing outgoing emails
// is fully supported in Drumkit pipeline.
func skipSelfForward(ctx context.Context, userID uint, msgRFCID string, ruleID uint) (bool, error) {
	_, redisErr := redis.RDB.Get(ctx, getRedisKeyForwardedRFC(ruleID, msgRFCID)).Result()
	if redisErr == nil {
		return true, nil
	}

	if !errors.Is(redisErr, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for self-forwarded message, falling back to RDS", zap.Error(redisErr))
	}

	// Fallback to RDS
	_, err := genEmailDB.GetForwardByGeneratedRFCID(ctx, userID, msgRFCID, ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("error checking if message was self-forwarded: %w", err)
	}

	return true, nil
}

// wasThreadForwarded checks if a thread has been forwarded before by this rule.
// If yes and rule.ForwardSubsequentEmailsInThread = true, then email is forwarded regardless of labeling
func wasThreadForwarded(ctx context.Context, threadID string, ruleID uint) (bool, error) {
	if threadID == "" {
		return false, fmt.Errorf("unexpected empty threadID")
	}

	// Check Redis first
	_, err := redis.RDB.Get(ctx, getRedisKeyForwardedThread(ruleID, threadID)).Result()
	if err == nil {
		return true, nil
	}
	if !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for forwarded thread", zap.Error(err))
	}

	// Fallback to RDS
	emails, err := genEmailDB.GetByForwardedThreadID(ctx, threadID, ruleID)
	if err != nil {
		return false, fmt.Errorf("error checking RDS for forwarded thread: %w", err)
	}

	return len(emails) > 0, nil
}
