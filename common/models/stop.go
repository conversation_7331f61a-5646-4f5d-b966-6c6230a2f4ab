package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

type Stops []Stop

// Stop represents a location where a truck needs to stop for pickup or delivery
type Stop struct {
	gorm.Model
	LoadID *uint `gorm:"index;default:null" json:"loadID"`
	Load   *Load `json:"-"`
	// NOTE: StopType validation will be added once we better understand how various integrations model their stops
	StopType   string `gorm:"index" json:"stopType"`
	StopNumber int    `gorm:"index" json:"stopNumber"` // Order of stops, starting from 0

	// Quote-specific fields
	QuoteID *uint       `gorm:"index;default:null" json:"quoteID"`
	Quote   *QuickQuote `json:"-"`
	// NOTE: Order will be deprecated and removed in favor of StopNumber.
	// It exists for backwards compatibility with the quote system.
	// Use StopNumber for all new code.
	Order   int     `json:"order"`
	Address Address `gorm:"embedded" json:"address"`

	// Common fields from Pickup/Consignee
	ExternalTMSID string `json:"externalTMSID"`
	Contact       string `json:"contact"`
	Phone         string `json:"phone"`
	Email         string `json:"email"`

	// Note this is different from ExternalTMSID as that's the UUID for the *location*.
	// ExternalTMSStopID is the UUID for this stop in the array of stops for this shipment, primarily for Relay.
	ExternalTMSStopID string   `json:"externalTMSStopID"`
	BusinessHours     string   `json:"businessHours"`
	RefNumber         string   `json:"refNumber"`
	ReadyTime         NullTime `json:"readyTime"`   // Optional, for pickup stops
	MustDeliver       NullTime `json:"mustDeliver"` // Optional, for dropoff stops

	// Appointment fields
	ApptRequired    bool     `json:"apptRequired"`
	ApptType        string   `json:"apptType"` // e.g. appointment, FCFS, drop trailer
	ApptStartTime   NullTime `json:"apptStartTime"`
	ApptEndTime     NullTime `json:"apptEndTime"`
	ActualStartTime NullTime `json:"actualStartTime"`
	ActualEndTime   NullTime `json:"actualEndTime"`
	// Different from ApptTimes; this is when carrier is on the road and ETAs need to be adjusted (applicable
	// primarily to Aljex and Relay)
	ExpectedStartTime NullTime `json:"expectedStartTime"`
	ApptNote          string   `json:"apptNote"`
	Timezone          string   `json:"timezone"` // IANA Timezone

	// TMS-specific fields
	// McLeod Enterprise
	AdditionalReferences AdditionalReferences `gorm:"type:JSONB" json:"additionalReferences"`
}

var (
	_ sql.Scanner   = &AdditionalReferences{}
	_ driver.Valuer = &AdditionalReferences{}
)

func (a *AdditionalReferences) Scan(value any) error {
	if value == nil {
		*a = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for AdditionalReferences: %T", value)
	}
	var additionalReferences AdditionalReferences
	if err := json.Unmarshal(val, &additionalReferences); err != nil {
		return err
	}
	*a = additionalReferences
	return nil
}

func (a AdditionalReferences) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}

	return json.Marshal(a)
}

type AdditionalReferences []AdditionalReference

type AdditionalReference struct {
	Qualifier          string  `json:"qualifier"`
	Number             string  `json:"number"`
	Weight             float32 `json:"weight"`
	Pieces             int     `json:"pieces"`
	ShouldSendToDriver bool    `json:"shouldSendToDriver"`
}

type Address struct {
	Name         string `json:"name"`
	AddressLine1 string `json:"addressLine1"`
	AddressLine2 string `json:"addressLine2"`
	City         string `json:"city"`
	State        string `json:"state"`
	Zip          string `json:"zip"`
	Timezone     string `json:"timezone"`
	Country      string `json:"country"`
	IsVerified   bool   `json:"isVerified"`
}

func (a Address) ToString() string {
	var parts []string

	if a.City != "" {
		parts = append(parts, a.City)
	}
	if a.State != "" {
		parts = append(parts, a.State)
	}
	if a.Zip != "" {
		parts = append(parts, a.Zip)
	}
	if a.Country != "" {
		parts = append(parts, a.Country)
	}

	return strings.Join(parts, ", ")
}

func ToAddressModel(address CompanyCoreInfo) Address {
	return Address{
		Name:         address.Name,
		AddressLine1: address.AddressLine1,
		AddressLine2: address.AddressLine2,

		City:    address.City,
		State:   address.State,
		Zip:     address.Zipcode,
		Country: address.Country,
	}
}

var (
	_ sql.Scanner   = &Stops{}
	_ driver.Valuer = &Stops{}
)

// Implements sql.Scanner interface
func (s *Stops) Scan(value any) error {
	if value == nil {
		*s = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Stop: %T", value)
	}
	var stops []Stop
	if err := json.Unmarshal(val, &stops); err != nil {
		return err
	}
	*s = stops
	return nil
}

// Implement driver.Valuer interface
func (s Stops) Value() (driver.Value, error) {
	if len(s) == 0 {
		return nil, nil
	}

	return json.Marshal(s)
}
