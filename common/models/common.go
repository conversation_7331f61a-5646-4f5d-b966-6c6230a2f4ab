package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type (
	/* general types for suggestions */
	SuggestionStatus   string
	SuggestionPipeline string
	SuggestionCategory string
)

const (
	/* general constants for suggestions */
	Pending SuggestionStatus = "pending"

	// The InFlight status means we successfully returned a list of quotes for the user to review.
	InFlight SuggestionStatus = "inFlight"

	// The Accepted status means the user has chosen a quote and sent it to the customer.
	// AKA - the user fully completed the quick quote flow.
	Accepted SuggestionStatus = "accepted"
	Rejected SuggestionStatus = "rejected"
)

// SuggestionAppliedPair is a generic type that represents a pair of values:
// a suggestion and its applied counterpart.
//
// Type T can be any type, allowing SuggestionAppliedPair to be used with various data types.
//
// Benefits:
//  1. Type safety with JSONB: Despite being stored as JSONB in the database, the generic type parameter ensures type
//     safety at the application level.
//  2. Tracking changes: Useful for models where suggestions are made and may or may not be applied.
type SuggestionAppliedPair[T any] struct {
	Suggestion T `json:"suggestion"`
	Applied    T `json:"applied"`
}

func (sap SuggestionAppliedPair[T]) Value() (driver.Value, error) {
	return json.Marshal(sap)
}

func (sap *SuggestionAppliedPair[T]) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &sap)
}
