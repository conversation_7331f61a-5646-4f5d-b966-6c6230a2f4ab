package models

import "time"

// TimePreference represents the available time slots that can be requested for an appointment.
type TimePreference string

const (
	TimePreferenceAnytime    TimePreference = "Anytime"
	TimePreferenceBeforeNoon TimePreference = "Before Noon"
	TimePreferenceNoonToSix  TimePreference = "Noon - 6pm"
	TimePreferenceAfterSix   TimePreference = "After 6pm"
)

// SchedulingOptions contains configuration for appointment scheduling requests.
type SchedulingOptions struct {
	RequestedDate  time.Time
	TimePreference TimePreference
	Warehouse      Warehouse
	RequestType    RequestType
}

// SchedulingOption defines a function that modifies SchedulingOptions.
type SchedulingOption func(*SchedulingOptions)

// Apply updates SchedulingOptions with the provided options.
func (o *SchedulingOptions) Apply(opts ...SchedulingOption) {
	for _, opt := range opts {
		opt(o)
	}
}

// WithTimePreference sets the preferred time slot for the appointment.
// NOTE: These specific values are specific to Retalix's scheduling system.
// Valid preferences are defined by the TimePreference constants:
// - TimePreferenceAnytime: No specific time preference
// - TimePreferenceBeforeNoon: Before 12:00 PM
// - TimePreferenceNoonToSix: Between 12:00 PM and 6:00 PM
// - TimePreferenceAfterSix: After 6:00 PM
func WithTimePreference(tp TimePreference) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.TimePreference = tp
	}
}

// WithRequestedDate sets the desired date for the appointment. The date should be provided in local time and will be
// formatted appropriately for the scheduling request.
func WithRequestedDate(rd time.Time) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.RequestedDate = rd
	}
}

// WithWarehouse sets the target warehouse for the appointment.
func WithWarehouse(wh Warehouse) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Warehouse = wh
	}
}

// WithRequestType sets the stop type of the warehouse for the appointment.
func WithRequestType(rt RequestType) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.RequestType = rt
	}
}
