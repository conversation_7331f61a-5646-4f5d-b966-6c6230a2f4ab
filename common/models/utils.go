package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/lib/pq"
)

// Drumkit definition of NullTime that implements JSON marshal/unmarshal
type NullTime pq.NullTime

var (
	_ json.Marshaler    = &NullTime{}
	_ json.Unmarshaler  = &NullTime{}
	_ sql.Scanner       = &NullTime{}
	_ driver.Valuer     = &NullTime{}
	_ gofakeit.Fakeable = &NullTime{}
)

func ToValidNullTime(dt time.Time) NullTime {
	return NullTime{Time: dt, Valid: true}
}

func (nt NullTime) Equal(other NullTime) bool {
	if nt.Valid != other.Valid {
		return false
	}
	if !nt.Valid {
		return true
	}
	return nt.Time.UTC().Equal(other.Time.UTC())
}

func (nt NullTime) MarshalJSON() ([]byte, error) {
	if nt.Valid {
		return json.Marshal(nt.Time)
	}

	return []byte(`""`), nil
}

// Returns NullTime{} for "" or "null". Parses RFC 3339 and
// custom time formats defined in the front end https://shorturl.at/bgnqD
func (nt *NullTime) UnmarshalJSON(b []byte) (err error) {
	// Explicitly handle empty strings
	s := strings.TrimSpace(string(b))
	if s == `""` || s == `"null"` {
		return nil
	}

	// Try parsing as RFC 3339
	var temp time.Time
	if err = json.Unmarshal(b, &temp); err == nil {
		nt.Time = temp
		nt.Valid = !temp.IsZero()

		return nil
	}

	// https://go.dev/issue/47353: unescaped chars in JSON string
	nt.Time, err = parseCustomDateTime(strings.ReplaceAll(s, "\"", ""))
	nt.Valid = err == nil && !nt.Time.IsZero()

	return err
}

// Because it's a new type, we must also re-define the DB methods
// Scan implements the Scanner interface. Source: github.com/lib/pq
func (nt *NullTime) Scan(value any) error {
	if value == nil {
		return nil
	}
	nt.Time, nt.Valid = value.(time.Time)
	return nil
}

// Value implements the driver Valuer interface. Source: github.com/lib/pq
func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	return nt.Time, nil
}

// FallbackTime returns first time if it's valid and not zero, otherwise second time
func FallbackTime(primary, secondary NullTime) time.Time {
	if primary.Valid && !primary.Time.IsZero() {
		return primary.Time
	}
	if secondary.Valid && !secondary.Time.IsZero() {
		return secondary.Time
	}
	return time.Time{}
}

// Supports time formats such as:
//
//	10/06/2023
//	10/06/23
//	10/06/2023, 8:00:00 PM/AM
//	10/06/2023, 14:00PM (will be converted to 02:00PM)
//
// See tests for more examples.
var timePattern = regexp.MustCompile(
	`^(?P<month>\d{1,2}/)` + // M or MM
		`(?P<date>\d{1,2}/)` + // D or DD
		`(?P<shortyear>\d{2})` + // YY date
		`(?P<fullyear>\d{2})?` + // full year YYYY
		`(?P<sep1>, )?` + // ", " date-time separator
		`(?P<minute>\d{1,2}:\d{2})?` + // hour:minute
		`(?P<second>:\d{2})?` + // :second
		`(?P<sep2> )?` + // " " time-of-day separator
		`(?P<tod>[AP]{1}[M]{1})?$`, // AM or PM
	// no timezone offsets for now
)

func parseCustomDateTime(timestamp string) (time.Time, error) {
	timestamp = handleHoursOutsideOfRangeGracefully(timestamp)

	groups := timePattern.FindStringSubmatch(timestamp)
	if len(groups) < 10 {
		return time.Time{}, nil
	}

	// groups[0] is the entire timestamp string

	var sb strings.Builder

	// month
	if m := groups[1]; len(m) == 3 {
		sb.WriteString("01/")
	} else {
		sb.WriteString("1/")
	}

	// day
	if m := groups[2]; len(m) == 3 {
		sb.WriteString("02/")
	} else {
		sb.WriteString("2/")
	}

	// Abbrv or full year
	if fy := groups[4]; fy != "" {
		sb.WriteString("2006") // full year YYYY
	} else {
		sb.WriteString("06") // short year YY
	}

	if t := groups[5]; t != "" {
		sb.WriteString(t) // date-time separator: ", "
	}

	if hourmin := groups[6]; hourmin != "" {
		sb.WriteString("3:04")
	}

	if second := groups[7]; second != "" {
		sb.WriteString(":05")
	}

	if sep2 := groups[8]; sep2 != "" {
		sb.WriteString(" ")
	}

	if tod := groups[9]; tod != "" {
		sb.WriteString("PM")
	}

	return time.Parse(sb.String(), timestamp)
}

// Handles 24-hour format with AM/PM indicators
// Returns the timestamp with the hour converted to 12-hour format if it's outside of the range
func handleHoursOutsideOfRangeGracefully(timestamp string) string {
	hourMinutePattern := regexp.MustCompile(`(\d{1,2}):(\d{2})`)
	matches := hourMinutePattern.FindStringSubmatch(timestamp)

	if len(matches) > 2 && strings.Contains(timestamp, "PM") {
		hourStr := matches[1]
		hour, err := strconv.Atoi(hourStr)
		if err != nil {
			return timestamp
		}

		if hour >= 13 && hour <= 23 {
			// Convert 24-hour format to 12-hour when AM/PM is present
			newHour := hour - 12
			timestamp = strings.Replace(
				timestamp,
				matches[0],
				fmt.Sprintf("%d:%s", newHour, matches[2]),
				1,
			)
		}
	}

	return timestamp
}

// For testing purposes
func (nt *NullTime) Fake(*gofakeit.Faker) (any, error) {
	return NullTime{
		Time:  time.Now(),
		Valid: true,
	}, nil
}
