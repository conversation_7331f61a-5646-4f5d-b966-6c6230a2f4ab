package models

import (
	"bytes"
	"fmt"
	"text/template"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"gorm.io/gorm"
)

type EmailTemplateType string

const (
	QuickQuoteReply      EmailTemplateType = "quick_quote_email_template"
	TrackAndTraceCarrier EmailTemplateType = "track_and_trace_carrier"
	// Subtypes for TrackAndTraceCarrier
	TrackAndTraceCarrierDispatch     EmailTemplateType = "track_and_trace_carrier_dispatch"
	TrackAndTraceCarrierPickup       EmailTemplateType = "track_and_trace_carrier_pickup"
	TrackAndTraceCarrierAfterPickup  EmailTemplateType = "track_and_trace_carrier_after_pickup"
	TrackAndTraceCarrierInTransit    EmailTemplateType = "track_and_trace_carrier_in_transit"
	TrackAndTraceCarrierDropoff      EmailTemplateType = "track_and_trace_carrier_dropoff"
	TrackAndTraceCarrierAfterDropoff EmailTemplateType = "track_and_trace_carrier_after_dropoff"
)

type EmailTemplate struct {
	gorm.Model
	ServiceID uint    `gorm:"index:idx_service_id_template_type,unique"`
	Service   Service `json:"-"`
	UserID    uint    `gorm:"index:idx_user_id_template_type,unique"`
	User      User    `json:"-"`

	//nolint:lll
	TemplateType EmailTemplateType `gorm:"index:idx_service_id_template_type,unique;index:idx_user_id_template_type,unique"`

	Subject string
	Body    string
}

type BaseTemplateData struct {
	FreightTrackingID string
	PONumbers         string
	FromCity          string
	FromState         string
	ToCity            string
	ToState           string
	DriverName        string
}

type PickupTemplateData struct {
	BaseTemplateData
	PickupAddress     string
	PickupAppointment string
}

type DropoffTemplateData struct {
	BaseTemplateData
	DropoffAddress     string
	DropoffAppointment string
}

var GenericEmailTemplates = map[EmailTemplateType]EmailTemplate{
	QuickQuoteReply: {
		TemplateType: QuickQuoteReply,
		Subject:      ``, // Email reply does not have a subject
		//nolint:lll
		Body: `Thank you for your {{transportType}} request from {{pickupLocation}} to {{deliveryLocation}}. The rate would be {{rate}}.`,
	},
	TrackAndTraceCarrierDispatch: {
		TemplateType: TrackAndTraceCarrierDispatch,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Dispatch{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Hi, please share the {{if not .DriverName}}driver name, {{end}}phone number, truck number, trailer number, and ETA.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},
	TrackAndTraceCarrierPickup: {
		TemplateType: TrackAndTraceCarrierPickup,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm At-Pickup{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver is on site for the expected pickup{{if .PickupAppointment}} ({{.PickupAppointment}}){{end}}{{if .PickupAddress}} at {{.PickupAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierAfterPickup: {
		TemplateType: TrackAndTraceCarrierAfterPickup,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Loaded{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the truck is loaded for the expected pickup{{if .PickupAppointment}} ({{.PickupAppointment}}){{end}}{{if .PickupAddress}} at {{.PickupAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierInTransit: {
		TemplateType: TrackAndTraceCarrierInTransit,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Location Updates{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver is on the way to the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierDropoff: {
		TemplateType: TrackAndTraceCarrierDropoff,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm At-Dropoff{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver has arrived to the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierAfterDropoff: {
		TemplateType: TrackAndTraceCarrierAfterDropoff,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Delivery{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver has unloaded at the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},
}

func PrepareEmailTemplate(templateType EmailTemplateType, load Load) (string, string, error) {
	tmpl := GenericEmailTemplates[templateType]

	var data any

	switch templateType {
	case TrackAndTraceCarrierDispatch:
		data = BaseTemplateData{
			FreightTrackingID: load.FreightTrackingID,
			PONumbers:         load.PONums,
			FromCity:          load.Pickup.City,
			FromState:         load.Pickup.State,
			ToCity:            load.Consignee.City,
			ToState:           load.Consignee.State,
			DriverName:        load.Carrier.FirstDriverName,
		}

	case TrackAndTraceCarrierPickup, TrackAndTraceCarrierAfterPickup:
		data = PickupTemplateData{
			BaseTemplateData: BaseTemplateData{
				FreightTrackingID: load.FreightTrackingID,
				PONumbers:         load.PONums,
				FromCity:          load.Pickup.City,
				FromState:         load.Pickup.State,
				ToCity:            load.Consignee.City,
				ToState:           load.Consignee.State,
				DriverName:        load.Carrier.FirstDriverName,
			},
			PickupAddress: FormatAddress(
				load.Pickup.AddressLine1,
				load.Pickup.AddressLine2,
				load.Pickup.City,
				load.Pickup.State,
				load.Pickup.Zipcode,
			),
			PickupAppointment: FormatAppointment(load.Pickup.ApptStartTime),
		}

	case TrackAndTraceCarrierInTransit, TrackAndTraceCarrierDropoff, TrackAndTraceCarrierAfterDropoff:
		data = DropoffTemplateData{
			BaseTemplateData: BaseTemplateData{
				FreightTrackingID: load.FreightTrackingID,
				PONumbers:         load.PONums,
				FromCity:          load.Pickup.City,
				FromState:         load.Pickup.State,
				ToCity:            load.Consignee.City,
				ToState:           load.Consignee.State,
				DriverName:        load.Carrier.FirstDriverName,
			},
			DropoffAddress: FormatAddress(
				load.Consignee.AddressLine1,
				load.Consignee.AddressLine2,
				load.Consignee.City,
				load.Consignee.State,
				load.Consignee.Zipcode,
			),
			DropoffAppointment: FormatAppointment(load.Consignee.ApptStartTime),
		}
	}

	subjectTmpl, err := template.New("subject").Parse(tmpl.Subject)
	if err != nil {
		return "", "", err
	}

	bodyTmpl, err := template.New("body").Parse(tmpl.Body)
	if err != nil {
		return "", "", err
	}

	var subject, body bytes.Buffer
	err = subjectTmpl.Execute(&subject, data)
	if err != nil {
		return "", "", err
	}

	err = bodyTmpl.Execute(&body, data)
	if err != nil {
		return "", "", err
	}

	return subject.String(), body.String(), nil
}

func FormatAddress(addressLine1, addressLine2, city, state, zipCode string) string {
	address := titleCase(addressLine1)

	if addressLine2 != "" {
		address += fmt.Sprintf(" %s", titleCase(addressLine2))
	}

	address += fmt.Sprintf(", %s %s %s", titleCase(city), state, zipCode)

	return address
}

func FormatAppointment(apptTime NullTime) string {
	if !apptTime.Valid {
		return ""
	}

	return apptTime.Time.Format("01/02 15:04")
}

func titleCase(s string) string {
	return cases.Title(language.English).String(s)
}
