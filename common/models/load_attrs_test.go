package models

import (
	"fmt"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Test to ensure Load{} and LoadAttributes{} don't drift from one another
// NOTE: that order of the definition of field matters for reflection.
func TestLoadAttributesStructuralEquality(t *testing.T) {
	assert.True(t, haveSameFields(Load{}, LoadAttributes{}))
}

// Test the correctness of haveSameFields()
func TestHaveSameFieldsNestedStructs(t *testing.T) {
	type (
		EmptyStruct struct{}

		NestedStruct struct {
			Field4 string
		}

		Struct1 struct {
			Field1 bool
			Field2 float32
			Field3 NestedStruct
		}

		NestedStructAttributes struct {
			Field4 FieldAttributes
		}

		Struct1Attributes struct {
			Field1 FieldAttributes
			Field2 FieldAttributes
			Field3 NestedStructAttributes
		}

		NestedStructB struct {
			Field4 string
			Field5 int
		}

		Struct2 struct {
			Field1 bool
			Field2 float32
			Field3 NestedStructB // func should check nested structs
		}
	)

	t.Run("Empty struct", func(*testing.T) {
		assert.True(t, haveSameFields(EmptyStruct{}, EmptyStruct{}))
	})

	t.Run("True", func(*testing.T) {
		assert.True(t, haveSameFields(Struct1{}, Struct1Attributes{}))
	})

	t.Run("False", func(*testing.T) {
		assert.False(t, haveSameFields(Struct2{}, Struct1Attributes{}))
	})

}

func TestHaveSameFieldsAnonymousStructs(t *testing.T) {
	type (
		NestedStruct struct {
			Field4 string
		}

		Struct1 struct {
			Field1 bool
			Field2 float32
			Field3 NestedStruct
		}

		NestedStructAttributes struct {
			Field4 FieldAttributes
		}

		Struct1Attributes struct {
			Field1 FieldAttributes
			Field2 FieldAttributes
			Field3 NestedStructAttributes
		}

		WrongStruct1Attributes struct {
			Field1 FieldAttributes
			Field2 FieldAttributes
			NestedStructAttributes
		}
	)

	t.Run("True", func(*testing.T) {
		assert.True(t, haveSameFields(Struct1{}, Struct1Attributes{}))
	})

	t.Run("False", func(*testing.T) {
		assert.False(t, haveSameFields(Struct1{}, WrongStruct1Attributes{}))
	})
}

// Helper function to verify that Load{} and LoadAttributes{} are the same structure
func haveSameFields(s1, s2 any) bool {
	if reflect.TypeOf(s2) == reflect.TypeOf(FieldAttributes{}) {
		return true
	}

	val1 := reflect.ValueOf(s1)
	val2 := reflect.ValueOf(s2)

	type1 := val1.Type()
	type2 := val2.Type()

	// Handle slice types
	if type1.Kind() == reflect.Slice && type2.Kind() == reflect.Slice {
		// Compare the element types instead
		return haveSameFields(
			reflect.Zero(type1.Elem()).Interface(),
			reflect.Zero(type2.Elem()).Interface(),
		)
	}

	len1 := val1.Type().NumField()
	len2 := val2.Type().NumField()

	if len1 != len2 {
		//nolint:forbidigo // Avoid import cycle
		fmt.Printf("diff num fields: %s= %d, %s=%d\n", val1.Type(), len1, val2.Type(), len2)
		return false
	}

	for i := 0; i < val1.NumField(); i++ {
		field1 := val1.Type().Field(i)
		field2 := val2.Type().Field(i)

		// NOTE: Handle special case of CompanyCoreInfo being an anonymous struct in models.Load but field name
		// is reflected as"CompanyCoreInfo" for Load{}, but "CompanyCoreInfoAttributes" in LoadAttributes{}.
		// However, JSON marshaling respects & flattens anonymous structs;
		// anonymous struct flattening is required for front-end attribute logic.
		if field1.Name != strings.TrimSuffix(field2.Name, "Attributes") {
			//nolint:forbidigo // Avoid import cycle
			fmt.Printf("diff names: %s and %s", field1.Name, field2.Name)
			return false
		}

		// NOTE: skipping the special case where Notes is an array. We'll fix this later.
		if field1.Name == "Notes" {
			return true
		}

		if val1.Type().Kind() == reflect.Struct && val1.Interface() != (time.Time{}) {
			isSame := haveSameFields(val1.Field(i).Interface(), val2.Field(i).Interface())
			if !isSame {
				//nolint:forbidigo // Avoid import cycle
				fmt.Printf("equivalency breaks at %s and %s\n", field1.Name, field2.Name)
				return isSame
			}
		}
	}

	return true
}

// Test to ensure that package-level Unsupported structs do not drift from
// struct definition
func TestPackageVariableDrift(t *testing.T) {
	t.Run("SpecificationsAttributes", func(t *testing.T) {
		verifyAllFieldsNotSupported(t, InitUnsupportedSpecs, SpecificationsAttributes{})
	})

	t.Run("RateDataAttributes", func(t *testing.T) {
		verifyAllFieldsNotSupported(t, InitUnsupportedRateData, RateDataAttributes{})
	})

	t.Run("BillToAttributes", func(t *testing.T) {
		verifyAllFieldsNotSupported(t, InitUnsupportedBillTo, BillToAttributes{})
	})
}

// Helper function to verify all fields in a struct are marked as not supported
func verifyAllFieldsNotSupported(t *testing.T, initStruct, emptyStruct any) {
	t.Helper()

	initVal := reflect.ValueOf(initStruct)
	emptyVal := reflect.ValueOf(emptyStruct)

	// Get all fields from the empty struct
	for i := 0; i < emptyVal.NumField(); i++ {
		field := emptyVal.Type().Field(i)
		initField := initVal.FieldByName(field.Name)

		// Skip if field is not of type FieldAttributes
		if field.Type != reflect.TypeOf(FieldAttributes{}) {
			continue
		}

		// Verify the field exists in init struct
		if !initField.IsValid() {
			t.Errorf("Field %s is missing in initialization struct", field.Name)
			continue
		}

		// Verify IsNotSupported is true
		isNotSupported := initField.FieldByName("IsNotSupported").Bool()
		if !isNotSupported {
			t.Errorf("Field %s must have IsNotSupported = true", field.Name)
		}
	}

	// Verify no extra fields in init struct
	for i := 0; i < initVal.NumField(); i++ {
		field := initVal.Type().Field(i)
		emptyField := emptyVal.FieldByName(field.Name)

		if !emptyField.IsValid() {
			t.Errorf("Extra field %s found in struct that doesn't exist in struct definition", field.Name)
		}
	}
}
