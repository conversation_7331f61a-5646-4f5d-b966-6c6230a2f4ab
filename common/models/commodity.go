package models

import (
	"database/sql/driver"
	"errors"
	"strings"

	"gorm.io/gorm"
)

type HazardClass []string

type Commodity struct {
	gorm.Model
	HandlingQuantity             int         `json:"handlingQuantity"`
	PackagingType                string      `json:"packagingType"`
	Length                       float64     `json:"length"`
	Width                        float64     `json:"width"`
	Height                       float64     `json:"height"`
	WeightTotal                  float64     `json:"weightTotal"`
	HazardousMaterial            bool        `json:"hazardousMaterial"`
	Quantity                     int         `json:"quantity"`
	FreightClass                 string      `json:"freightClass"`
	NMFC                         string      `json:"nmfc"`
	Description                  string      `json:"description"`
	AdditionalMarkings           string      `json:"additionalMarkings"`
	UNNumber                     string      `json:"unNumber"`
	PackingGroup                 string      `json:"packagingGroup"`
	ReferenceNumber              string      `json:"referenceNumber"`
	HazmatCustomClassDescription string      `json:"hazmatCustomClassDescription"`
	HazmatPieceDescription       string      `json:"hazmatPieceDescription"`
	HarmonizedCode               string      `json:"harmonizedCode"`
	HazardClasses                HazardClass `gorm:"type:VARCHAR(255)" json:"hazardClasses"`
	LoadID                       uint        `gorm:"index" json:"loadID" validate:"required"`
}

func (h *HazardClass) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("value cannot cast to []byte")
	}

	*h = strings.Split(string(bytes), ",")

	return nil
}

func (h HazardClass) Value() (driver.Value, error) {
	if len(h) == 0 {
		return nil, nil
	}

	return strings.Join(h, ","), nil
}
