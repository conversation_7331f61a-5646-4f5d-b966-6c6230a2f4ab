package models

const (
	CyclopsSchedulingIntegration             = "scheduling"
	CyclopsModeAPI               CyclopsMode = "api"
	CyclopsModeSelenium          CyclopsMode = "selenium"

	ActionGetWarehouse        CyclopsAction = "GetWarehouse"
	ActionGetOpenSlots        CyclopsAction = "GetOpenSlots"
	ActionGetLoadTypes        CyclopsAction = "GetLoadTypes"
	ActionGetAppointment      CyclopsAction = "GetAppointment"
	ActionMakeAppointment     CyclopsAction = "MakeAppointment"
	ActionUpdateAppointment   CyclopsAction = "UpdateAppointment"
	ActionCancelAppointment   CyclopsAction = "CancelAppointment"
	ActionValidateAppointment CyclopsAction = "ValidateAppointment"

	AppointmentStatusCancelled AppointmentStatus = "cancelled"
	AppointmentStatusCompleted AppointmentStatus = "completed"
	AppointmentStatusPending   AppointmentStatus = "pending"

	RequestTypePickup  RequestType = "pickup"
	RequestTypeDropoff RequestType = "dropoff"
)

type (
	CyclopsIntegration string
	CyclopsAction      string
	CyclopsMode        string
	RequestType        string
	AppointmentStatus  string

	CyclopsError struct {
		Message string
		Errors  []string
	}

	CyclopsCredentials struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	CyclopsBaseRequest struct {
		Integration string             `json:"integration"`
		Platform    string             `json:"platform"`
		Action      CyclopsAction      `json:"action"`
		Credentials CyclopsCredentials `json:"credentials"`
		UserID      string             `json:"userId"`
		Mode        CyclopsMode        `json:"mode,omitempty"`
	}

	CyclopsGetSlotsRequest struct {
		CyclopsBaseRequest
		StartDate  string `json:"startDate"`
		EndDate    string `json:"endDate"`
		LocationID string `json:"locationId,omitempty"`
	}

	CyclopsGetAppointmentsRequest struct {
		CyclopsBaseRequest
		Status     AppointmentStatus `json:"status"`
		StartDate  string            `json:"startDate"`
		EndDate    string            `json:"endDate"`
		CustomerID string            `json:"customerId"`
	}

	CyclopsGetAppointmentRequest struct {
		CyclopsBaseRequest
		Appointment CyclopsAppointmentData `json:"appointment"`
	}

	CyclopsCancelAppointmentRequest struct {
		CyclopsBaseRequest
		Appointment CyclopsAppointmentData `json:"appointment"`
	}

	CyclopsMakeAppointmentRequest struct {
		CyclopsBaseRequest
		Appointment CyclopsAppointmentData `json:"appointment"`
	}

	CyclopsBaseResponse struct {
		Success bool     `json:"success"`
		Message string   `json:"message"`
		Errors  []string `json:"errors,omitempty"`
	}

	CyclopsPlatformData struct {
		Appointments []CyclopsAppointment `json:"appointments"`
	}

	CyclopsGetSlotsResponse struct {
		CyclopsBaseResponse
		Appointments []CyclopsAppointmentData `json:"appointments"`
		PlatformData CyclopsPlatformData      `json:"platformData"`
	}

	CyclopsGetAppointmentResponse struct {
		CyclopsBaseResponse
		Appointments []CyclopsAppointment `json:"appointments"`
		PlatformData CyclopsPlatformData  `json:"platformData"`
	}

	CyclopsCancelAppointmentResponse struct {
		CyclopsBaseResponse
		Appointment CyclopsAppointment `json:"appointment"`
	}

	CyclopsMakeAppointmentResponse struct {
		CyclopsBaseResponse
		Appointment CyclopsAppointment `json:"appointment"`
	}

	CyclopsAppointmentData struct {
		AppointmentID string                `json:"appointmentId"`
		Duration      int                   `json:"duration"`
		Notes         string                `json:"notes"`
		PoNums        string                `json:"poNums"`
		ScheduledTime string                `json:"scheduledTime"`
		Status        string                `json:"status"`
		Warehouse     *CyclopsWarehouseInfo `json:"warehouse,omitempty"`
		Extended      any                   `json:"extended,omitempty"`
	}

	CyclopsAppointment struct {
		AppointmentID string `json:"appointmentId"`
		Duration      int    `json:"duration"`
		Location      string `json:"location"`
		ScheduledTime string `json:"scheduledTime"`
		Status        string `json:"status"`
	}

	CyclopsWarehouseSlot struct {
		Duration      int    `json:"duration"`
		ScheduledTime string `json:"scheduledTime"`
	}

	CyclopsWarehouseInfo struct {
		City      string                 `json:"city,omitempty"`
		Country   string                 `json:"country,omitempty"`
		Name      string                 `json:"name"`
		OpenSlots []CyclopsWarehouseSlot `json:"openSlots,omitempty"`
		State     string                 `json:"state,omitempty"`
		StopType  string                 `json:"stopType,omitempty"`
		Website   string                 `json:"website,omitempty"`
		ZipCode   string                 `json:"zipCode,omitempty"`
	}
)

func (e *CyclopsError) Error() string {
	return e.Message
}

func (rt RequestType) IsValid() bool {
	return rt == RequestTypePickup || rt == RequestTypeDropoff
}
