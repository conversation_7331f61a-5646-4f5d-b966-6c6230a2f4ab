package models

import "gorm.io/gorm"

// Unique indices created on rds/migrate.go due to composite unique index on TMS ID and nested External TMS ID.
type TMSCustomer struct {
	gorm.Model
	CompanyCoreInfo
	ExternalID       string      `json:"externalId,omitempty"` // TODO: For backward compatibility, remove in the future
	TMSIntegrationID uint        `json:"tmsIntegrationId,omitempty"`
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`
}
