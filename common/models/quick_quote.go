package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type TransportType string

// For generating lane history charts from different data source types (Greenscreens, Mcleod, etc)
type LaneHistoryRawDataAccessor interface {
	GetPickupDate() (time.Time, error)
	GetTotalCarrierCost() float32
	GetTotalDistance() float32
	GetCarrierName() string
}

const (
	FlatbedTransportType TransportType = "FLATBED"
	VanTransportType     TransportType = "VAN"
	ReeferTransportType  TransportType = "REEFER"

	// Additional transport types
	HotShotTransportType  TransportType = "HOTSHOT"   // aka FLATBED HOTSHOT
	BoxTruckTransportType TransportType = "BOX TRUCK" // aka VAN
	SprinterTransportType TransportType = "SPRINTER"  // aka VAN

	// Generic catch all for excluded special equipment
	SpecialTransportType TransportType = "SPECIAL"

	// TODO: These are load modes, not transport types. Refactor references
	FTLTransportType TransportType = "FTL"
	LTLTransportType TransportType = "LTL"
)

func ListTransportTypes() []TransportType {
	return []TransportType{
		FlatbedTransportType,
		VanTransportType,
		ReeferTransportType,
		HotShotTransportType,
		BoxTruckTransportType,
	}
}

type (
	QuotePipeline     string
	QuoteSource       string
	QuoteTypeInSource string // e.g. Network or BuyPower for GS, Spot for DAT
)

const (
	APIPipeline            QuotePipeline = "API" // e.g. GreenScreens or DAT
	CarrierNetworkPipeline QuotePipeline = "CarrierNetwork"

	GreenscreensSource QuoteSource = "greenscreens"
	TruckStopSource    QuoteSource = "truckStop"
	DATSource          QuoteSource = "dat"
	GlobalTranzSource  QuoteSource = "globaltranz"

	// TMS Lane history
	TmsLaneHistorySource QuoteSource = "tms_lane_history" // e.g. Turvo, McLeod Enterprise

	// used for metrics - if user quoted a value that didn't come from an external source
	BrokerDeterminedSource QuoteSource = "broker_determined"

	GSMixedV1              QuoteTypeInSource = "GS_Mixed" // TODO: remove once V1 get_quick_quote route is unused
	GSNetworkType          QuoteTypeInSource = "gs_network"
	GSBuyPowerType         QuoteTypeInSource = "gs_buypower"
	GSMostConfidentType    QuoteTypeInSource = "gs_mostconfident" // e.g. Wickerpark's website
	DATSpotType            QuoteTypeInSource = "dat_spot"
	GlobalTranzHistoryType QuoteTypeInSource = "globaltranz_history"
	TruckStopBookedType    QuoteTypeInSource = "truckstop_booked"
	TruckStopPostedType    QuoteTypeInSource = "truckstop_posted"

	TurvoQuoteTypeInSource            QuoteTypeInSource = "turvo"
	McleodEnterpriseQuoteTypeInSource QuoteTypeInSource = "mcleodenterprise"
)

// For more info, see https://www.notion.so/drumkitai/Quoting-ADR-1a72b16b087a8093b0fef7dab1bd09f8
type QuickQuote struct {
	gorm.Model
	// IDs
	ExternalID     string       `gorm:"index"` // Prediction ID from the rate prediction
	QuoteRequestID uint         `gorm:"index"`
	QuoteRequest   QuoteRequest `json:"-"`
	// Prospect (aka shipper) of which Drumkit customer (aka broker) that submitted request
	ServiceID uint    `gorm:"index"`
	Service   Service `json:"-"`
	// Axle UserID associated with the email account
	UserID uint `gorm:"default:null;index"`
	User   User `json:"-"`
	// The email/thread the user was looking at during quote submission
	LookedAtEmailID  uint   `gorm:"index"`
	LookedAtEmail    Email  `gorm:"LookedAtEmailID;references:ID"`
	LookedAtThreadID string `gorm:"index"`

	Pipeline     QuotePipeline
	Source       QuoteSource       `json:"source"`
	TypeInSource QuoteTypeInSource `json:"typeInSource"`

	QuoteLoadInfo
	QuoteDATMetadata
	// All the stops in order, including pickup[0] and dropoff[n-1]
	Stops []Stop `gorm:"foreignKey:QuoteID;references:ID" json:"stops"`

	// Pricing
	Currency          string  `json:"currency"`
	TargetBuyRate     float64 `json:"targetBuyRate"`     // Target buy rate from the rate prediction, per mile
	LowBuyRate        float64 `json:"lowBuyRate"`        // per mile
	HighBuyRate       float64 `json:"highBuyRate"`       // per mile
	StartBuyRate      float64 `json:"startBuyRate"`      // per mile
	FuelRate          float64 `json:"fuelRate"`          // per mile
	ConfidenceLevel   float64 `json:"confidenceLevel"`   // Confidence level from the rate prediction
	MinMarkup         float64 `json:"minMarkup"`         // e.g. 1.07 = 7% markup, added to buy (carrier) rate
	MaxMarkup         float64 `json:"maxMarkup"`         // e.g. 1.10 = 10% markup
	TotalCost         float64 `json:"totalCost"`         // Target Buy Rate * distance, no markup applied
	MinTargetSellCost float64 `json:"minTargetSellCost"` // TargetSellCost * MinMarkup
	MaxTargetSellCost float64 `json:"maxTargetSellCost"` // TargetSellCost * MaxMarkup
	Distance          float64 `json:"distance"`          // Distance in miles

	LeadContacted bool `json:"leadContacted"` // Prospect chose to contact with broker after quote; for analytics

	// Final Quote values
	// Deprecated: Use QuoteRequest.Final* fields instead
	FinalQuotePrice  int // Total sell price. If QuoteType is PerMile, this is (FinalCarrierCost + Margin) * Distance
	FinalMargin      int
	MarginType       string // Percent or Amount
	CarrierCostType  string // Flat or PerMile
	FinalCarrierCost int
}

// Basic info about the load for a quote request, such as pickup and dropoff
type QuoteLoadInfo struct {
	CustomerID           uint
	Customer             CompanyCoreInfo `gorm:"embedded;embeddedPrefix:customer_" json:"customer"`
	TransportType        TransportType   `json:"transportType"`
	Commodity            string          `json:"commodity"`
	WeightLbs            float64         `json:"weightLbs"`
	Distance             float64         `json:"distance"` // Total distance of the trip in miles
	FuelSurchargePerMile float64         `json:"fuelSurchargePerMile"`
	FuelSurchargeTotal   float64         `json:"fuelSurchargeTotal"`
	Pallets              Pallets         `gorm:"type:JSONB" json:"pallets"`

	// Legacy fields - mapped to first/last stop
	PickupLocation   Address  `gorm:"embedded;embeddedPrefix:pickup_" json:"pickupLocation"` // Stops[0]
	PickupDate       NullTime `json:"pickupDate"`
	DeliveryLocation Address  `gorm:"embedded;embeddedPrefix:dropoff_" json:"deliveryLocation"` // Stops[n-1]
	DeliveryDate     NullTime `json:"deliveryDate"`

	// New multi-stop support
	Stops Stops `gorm:"type:JSONB" json:"stops,omitempty"`
}

// DAT metadata regarding the timeframe and geographic region of quotes
type QuoteDATMetadata struct {
	DATTimeframe       string `json:"dat_timeframe"`
	DATOriginName      string `json:"dat_origin_name"`
	DATOriginType      string `json:"dat_origin_type"`
	DATDestinationName string `json:"dat_destination_name"`
	DATDestinationType string `json:"dat_destination_type"`
}

type CoreAddress struct {
	City  string `json:"city"`
	State string `json:"state"`
}

// Pallets is a custom type for JSONB array of Pallets
type Pallets []Pallet

type Pallet struct {
	Count            int     `json:"count"` // Can also be crate count
	DeclaredValueUSD float32 `json:"declaredValueUSD"`
	WeightLbs        float32 `json:"weightLbs"`
	LengthInches     float32 `json:"lengthInches"`
	WidthInches      float32 `json:"widthInches"`
	HeightInches     float32 `json:"heightInches"`
}

type CreateQuoteBody struct {
	CustomerID            string        `json:"customerId"`
	QuotePrice            int           `json:"quotePrice"`
	QuoteNumber           string        `json:"quoteNumber"`
	TransportType         TransportType `json:"transportType"`
	PickupLocationZip     string        `json:"pickupLocation"`
	PickupLocationCity    string        `json:"pickupLocationCity"`
	PickupLocationState   string        `json:"pickupLocationState"`
	PickupDate            NullTime      `json:"pickupDate"`
	DeliveryLocationZip   string        `json:"deliveryLocation"`
	DeliveryLocationCity  string        `json:"deliveryLocationCity"`
	DeliveryLocationState string        `json:"deliveryLocationState"`
	DeliveryDate          NullTime      `json:"deliveryDate"`
	CommodityDescription  string        `json:"commodityDescription"`
	CommodityWeight       string        `json:"commodityWeight"`
	UserID                uint          `json:"userId"`
}

type CreateQuoteResponse struct {
	QuoteID int `json:"quoteId"`
}

// Implements sql.Scanner interface
func (p *Pallets) Scan(value any) error {
	if value == nil {
		*p = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Pallets: %T", value)
	}
	var pallets []Pallet
	if err := json.Unmarshal(val, &pallets); err != nil {
		return err
	}
	*p = pallets
	return nil
}

// Implement driver.Valuer interface
func (p Pallets) Value() (driver.Value, error) {
	if len(p) == 0 {
		return nil, nil
	}

	return json.Marshal(p)
}

var (
	_ sql.Scanner   = &Pallets{}
	_ driver.Valuer = &Pallets{}
)
