package models

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// Basic implementation of a rule engine
type EmailForwardingRule struct {
	gorm.Model

	Description string // Concise description of the rule
	ServiceID   uint
	Service     Service `json:"-"`
	// If nil, then rule is applied to all users in service
	// If non-nil, then this rule is applied only to the user with this ID.
	UserID *uint
	User   *User `json:"-"`

	//nolint:lll
	EmailCategories pq.StringArray `gorm:"type:text[]"` // From emails.EmailCategories. If both defined, then categories take precedence
	EmailLabels     pq.StringArray `gorm:"type:text[]"` // From emails.EmailLabel

	Recipients   pq.StringArray `gorm:"type:text[]"` // Array of email addresses
	CCRecipients pq.StringArray `gorm:"type:text[]"` // Array of email addresses

	// If true, then forward all subsequent emails in the thread,
	// regardless of if the specific message matches the rule categories/labels
	ForwardSubsequentEmailsInThread bool

	// If true, then use the sender's signature for the forwarded email
	UseSenderSignature bool
}
