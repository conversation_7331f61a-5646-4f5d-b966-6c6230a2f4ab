package models

type CarrierVerificationResponse struct {
	CarrierName                   string `json:"name"`
	DBA                           string `json:"dba"`
	DOTNumber                     int    `json:"dot_number"`
	DocketNumber                  string `json:"docket_number"`
	RiskRating                    string `json:"risk_rating"`
	TotalIncidentReports          int    `json:"incident_reports"`
	TotalIncidentReportsWithFraud int    `json:"incident_reports_with_fraud"`
	CompletedPacket               bool   `json:"completed_packet"`
	IntegrationName               string `json:"integration_name"`
}
