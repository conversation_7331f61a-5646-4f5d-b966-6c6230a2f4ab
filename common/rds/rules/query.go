package rules

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetForwardingRulesByUserAndService retrieves all email forwarding rules for a given service ID
// or user ID.
func GetForwardingRulesByUserAndService(
	ctx context.Context,
	userID uint,
	serviceID uint,
) ([]models.EmailForwardingRule, error) {

	var rules []models.EmailForwardingRule
	err := rds.WithContextReader(ctx).Where("service_id = ? OR user_id = ?", serviceID, userID).Find(&rules).Error

	return rules, err
}
