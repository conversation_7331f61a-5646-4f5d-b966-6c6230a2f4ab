package rds

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

// NOTE: NOT FOR PRODUCTION. Only for tests
func ClearTestDB(ctx context.Context, t *testing.T) {
	err := WithContext(ctx).Exec("DELETE FROM email_loads").Error
	require.NoError(t, err)

	updateValues := map[string]any{
		"nickname":              nil,
		"quick_quote_config_id": nil,
	}

	// Set both these columns to null first to adhere to check constraints
	err = WithContext(ctx).Session(&gorm.Session{AllowGlobalUpdate: true}).
		Model(&models.Service{}).Updates(updateValues).Error
	require.NoError(t, err)

	// Clear tables in reverse order to respect foreign key constraints
	for i := len(DefaultMigrationOrder) - 1; i >= 0; i-- {
		err = WithContext(ctx).Session(&gorm.Session{AllowGlobalUpdate: true}).
			Unscoped().Delete(DefaultMigrationOrder[i]).Error
		require.NoError(t, err)
	}
}
