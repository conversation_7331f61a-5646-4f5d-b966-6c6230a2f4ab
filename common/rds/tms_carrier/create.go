package tmscarrier

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Refreshes list of TMS carriers by deleting existing carriers from DB and adding current ones.
// This way, Drumkit captures not only new carriers in the TMS but also deleted ones.
func RefreshTMSCarriers(ctx context.Context, serviceID uint, carriers []models.TMSCarrier) error {
	if len(carriers) == 0 {
		return errors.New("empty slice of TMSCarriers")
	}

	for i := range carriers {
		carriers[i].ServiceID = serviceID
	}

	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().
			Where("tms_integration_id = ?", carriers[0].TMSIntegrationID).Delete(&models.TMSCarrier{}).Error
		if err != nil {
			return fmt.Errorf("error deleting existing carriers for TMS %d: %w", carriers[0].TMSIntegrationID, err)
		}

		return tx.CreateInBatches(&carriers, 1000).
			Clauses(clause.Returning{}, clause.OnConflict{
				Columns:   []clause.Column{{Name: "tms_integration_id"}, {Name: "external_tms_id"}},
				UpdateAll: true,
			}).Error
	})
}

// CreateCarrier creates a single carrier in the database
func CreateCarrier(ctx context.Context, carrier *models.TMSCarrier) error {
	return rds.WithContext(ctx).Create(carrier).Error
}
