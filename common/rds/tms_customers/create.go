package tmscustomer

import (
	"context"
	"errors"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

const (
	EmptyTMSCustomersSlice = "empty slice of TMSCustomers"
)

// Refreshes list of TMS customers by deleting existing users from DB and adding current ones.
// This way, Drumkit captures not only new customers in the TMS but also deleted ones.
func RefreshTMSCustomers(ctx context.Context, customers *[]models.TMSCustomer) error {
	if len(*customers) == 0 {
		return errors.New(EmptyTMSCustomersSlice)
	}

	// Upsert customers to db.
	// "UpdateAll: true" can sometimes behave unpredictably with batch
	// operations, so we explicitly list the columns to update instead.
	return rds.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns: []clause.Column{{Name: "tms_integration_id"}, {Name: "external_tms_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"name",
					"address_line1",
					"address_line2",
					"city",
					"state",
					"zipcode",
					"country",
					"contact",
					"phone",
					"email",
					"updated_at",
				}),
			},
		).
		CreateInBatches(&customers, 1000).
		Error
}
