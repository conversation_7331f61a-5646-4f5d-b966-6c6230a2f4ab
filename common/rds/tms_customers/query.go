package tmscustomer

import (
	"context"
	"database/sql"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchCustomersQuery struct {
	TMSID uint `json:"tmsID"`
	models.CompanyCoreInfo
}

// Get customers by TMS ID, ordered by name in ascending order
func GetTMSCustomersByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSCustomer, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", query.TMSID).Order("name ASC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

// GetCustomerNamesByIDs gets multiple customer names by their IDs in a single query
func GetCustomerNamesByIDs(ctx context.Context, customerIDs []uint) (map[uint]string, error) {
	if len(customerIDs) == 0 {
		return map[uint]string{}, nil
	}

	// Define a struct to hold the query results
	type CustomerName struct {
		ID   uint   `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}

	var results []CustomerName

	// Execute a single query with the IN clause
	err := rds.WithContextReader(ctx).
		Model(&models.TMSCustomer{}).
		Where("id IN (?)", customerIDs).
		Select("id, name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Convert the results to a map
	customerNames := make(map[uint]string, len(results))
	for _, result := range results {
		customerNames[result.ID] = result.Name
	}

	return customerNames, nil
}

func GetByExternalTMSID(ctx context.Context, tmsID uint, externalTMSID string) (res models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND external_tms_id = ?", tmsID, externalTMSID).
		First(&res).Error

	return res, err
}

func GetCustomerEmailByID(ctx context.Context, id uint) (res string, err error) {
	var customer models.TMSCustomer

	err = rds.WithContextReader(ctx).Where("id = ?", id).First(&customer).Error
	if err != nil {
		return "", err
	}

	return customer.Email, nil
}

func GetCustomerNameByID(ctx context.Context, id uint) (res string, err error) {
	var customer models.TMSCustomer
	err = rds.WithContextReader(ctx).Where("id = ?", id).First(&customer).Error
	if err != nil {
		return "", err
	}

	return customer.Name, nil
}

// This function is used by the "Name" autocomplete search input on the FE
func FuzzySearchByName(ctx context.Context, query SearchCustomersQuery) (res []models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchName(ctx, query.Name, query.TMSID, 0.1)).
		Find(&res).Error

	log.Info(ctx, "found customers by name fuzzy match", zap.Int("count", len(res)))

	return res, err
}

// This function is used by the "Address Line 1" autocomplete search input on the FE
func FuzzySearchByStreetAddress(
	ctx context.Context,
	query SearchCustomersQuery,
) (res []models.TMSCustomer, err error) {

	var customerAddressSearchThreshold = 0.1
	if len(query.AddressLine1) < 3 {
		return res, nil
	}

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchStreetAddress(ctx, query.AddressLine1, query.TMSID, customerAddressSearchThreshold)).
		Find(&res).Error

	log.Info(ctx, "found customers by street fuzzy match", zap.Int("count", len(res)))

	return res, err
}

// This function is part of the LLM load building extraction process and uses a higher similarity threshold
// than search function to match customers by name
func GetCustomerByName(ctx context.Context, tmsID uint, name string) (res models.TMSCustomer, err error) {
	var customers []models.TMSCustomer
	var customerNameSearchThreshold = 0.4

	if len(name) < 3 {
		return res, nil
	}

	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND name ILIKE '%' || ? || '%'", tmsID, name).
		Find(&customers).Error

	if err == nil && len(customers) == 1 {
		return customers[0], nil
	}

	// If error or multiple results, try fuzzy matching
	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchName(ctx, name, tmsID, customerNameSearchThreshold)).
		First(&res).Error

	return res, err
}

func fuzzyMatchName(
	ctx context.Context,
	name string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "name", name, tmsID, similarityThreshold)
}

func fuzzyMatchStreetAddress(
	ctx context.Context,
	addressLine1 string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "address_line1", addressLine1, tmsID, similarityThreshold)
}

// Helper function to fuzzy match a customer by a column name
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH tms_customer_distances AS (
					SELECT *,
						%[1]s <-> @searchTerm AS trgm_dist,
						%[1]s ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM tms_customers
					WHERE tms_integration_id = @tmsID
					-- Exclude customers whose similarity to searchTerm is lower than threshold
					AND (%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%')
				)
				SELECT *
				FROM tms_customer_distances
				ORDER BY
					is_match DESC,
					trgm_dist
				LIMIT 20;
			`, columnName)

			// NOTE: Scopes() and Raw() cannot be used to chain multiple fuzzy queries together
			// (like load advanced search) because the queries are executed in a single transaction
			// but this usage is fine
			return tx.Raw(query, sql.Named("searchTerm", searchTerm), sql.Named("tmsID", tmsID))
		})
	}
}
