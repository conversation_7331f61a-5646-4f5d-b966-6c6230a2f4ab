package warehouse

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Deprecated; FE now only uses /warehouses/recent and /warehouses/search
func GetWarehousesBySource(
	ctx context.Context,
	serviceID uint,
	source models.WarehouseSource,
) ([]models.Warehouse, error) {

	var warehouseList []models.Warehouse
	err := rds.WithContextReader(ctx).
		Where(`source = ? AND source IN (
			SELECT name FROM integrations WHERE type = 'scheduling' AND service_id = ?
		)
		`, source, serviceID).Find(&warehouseList).Error

	if err != nil {
		return nil, err
	}

	return warehouseList, nil
}

func GetWarehousesBySearch(ctx context.Context, serviceID uint, search string) ([]models.Warehouse, error) {
	// Find exact matches from associated warehouse addresses first
	var warehouseList []models.Warehouse

	err := rds.WithContext(ctx).Raw(`
		SELECT DISTINCT w.*
		FROM warehouses w
		JOIN warehouse_address_associations waa ON waa.warehouse_id = w.id
		JOIN warehouse_addresses wa ON waa.warehouse_address_id = wa.id
		WHERE w.source IN (
			SELECT name FROM integrations
			WHERE type = 'scheduling' AND service_id = @serviceID
		)
		AND (
			wa.address_line1 ILIKE @search
			OR wa.city ILIKE @search
			OR wa.state ILIKE @search
			OR w.warehouse_name ILIKE @search
		)
		LIMIT 100
	`, sql.Named("search", search), sql.Named("serviceID", serviceID),
	).Find(&warehouseList).Error

	if err != nil {
		return nil, fmt.Errorf("exact match query failed: %w", err)
	}

	// If we found exact matches, return them
	if len(warehouseList) > 0 {
		return warehouseList, nil
	}

	// Fall back to trigram search if no exact match
	var warehouseSearchThreshold = 0.1

	err = rds.WithContextReader(ctx).Transaction(func(tx *gorm.DB) error {
		log.Infof(ctx, "Starting transaction for similarity search with search: %s", search)

		if err := rds.SetSimilarityThreshold(ctx, tx, warehouseSearchThreshold); err != nil {
			return err
		}

		// Performing search with updated similarity threshold
		err := tx.WithContext(ctx).Raw(`
			WITH warehouse_distances AS (
				SELECT *,
					warehouse_full_identifier <-> @search AS trgm_dist,
					warehouse_full_identifier ILIKE '%' || @search || '%'  AS is_match
				FROM warehouses
				WHERE (
					warehouse_full_identifier % @search
					OR warehouse_full_identifier ILIKE '%' || @search || '%'
				)
				-- Limit to sources service can access
				AND source IN (
					SELECT name FROM integrations WHERE type = 'scheduling' AND service_id = @serviceID
				)
			)
			SELECT *
			FROM warehouse_distances
			ORDER BY
				is_match DESC,
				trgm_dist;
		`, sql.Named("search", search),
			sql.Named("serviceID", serviceID)).
			Find(&warehouseList).Error
		if err != nil {
			log.Error(ctx, "Error performing similarity search: %v", zap.Error(err))
			return fmt.Errorf("failed to perform similarity search: %w", err)
		}

		return nil
	})

	if err != nil {
		log.Error(ctx, "transaction failed", zap.Error(err))
		return nil, err
	}

	return warehouseList, nil
}

func GetWarehouseByIDAndSource(
	ctx context.Context,
	serviceID uint,
	source models.WarehouseSource,
	warehouseID string,
) (warehouse *models.Warehouse, err error) {

	err = rds.WithContextReader(ctx).
		Where(`
		source = ? AND warehouse_id = ? AND
		-- Only allow warehouses service has access to
		source IN (SELECT name FROM integrations WHERE type = 'scheduling' AND service_id = ?)`,
			source, warehouseID, serviceID).
		First(&warehouse).Error

	return warehouse, err
}

func GetRecentWarehousesByService(
	ctx context.Context,
	serviceID uint,
) ([]models.Warehouse, error) {

	var warehouseList []models.Warehouse
	err := rds.WithContextReader(ctx).Raw(
		`SELECT * FROM warehouses WHERE id IN (
			SELECT pickup_warehouse_id FROM loads WHERE service_id = ?
				AND pickup_warehouse_id IS NOT NULL
				AND updated_at > current_date - interval '90 days'
			UNION
			SELECT dropoff_warehouse_id FROM loads WHERE service_id = ?
				AND dropoff_warehouse_id IS NOT NULL
				AND updated_at > current_date - interval '90 days'
		)`, serviceID, serviceID).
		Find(&warehouseList).Error

	if err != nil {
		return nil, err
	}

	return warehouseList, nil
}

// For matching a load stop to a warehouse; uses a higher similarity threshold.
func GetWarehouseByAddress(
	ctx context.Context,
	serviceID uint,
	address string,
	streetNumber string,
) (*models.Warehouse, error) {

	var warehouseList []models.Warehouse
	err := rds.WithContextReader(ctx).
		Scopes(fuzzyMatchAddress(ctx, serviceID, address)).
		Find(&warehouseList).Error

	if err != nil {
		return nil, err
	}

	if len(warehouseList) == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	if len(warehouseList) == 1 {
		return &warehouseList[0], nil
	}

	for _, wh := range warehouseList {
		if streetNumber != "" && strings.Contains(wh.WarehouseAddressLine1, streetNumber) {
			return &wh, nil
		}
	}

	return &warehouseList[0], nil
}

func fuzzyMatchAddress(ctx context.Context, serviceID uint, address string) func(db *gorm.DB) *gorm.DB {
	const similarityThreshold = 0.3

	return func(tx *gorm.DB) *gorm.DB {
		if address == "" {
			return tx
		}

		if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
			log.Errorf(ctx, "failed to set similarity threshold: %w", err)
			return tx
		}

		return tx.Raw(`
			WITH warehouse_distances AS (
				SELECT *,
					warehouse_full_address <-> @address AS trgm_dist,
					warehouse_full_address ILIKE '%' || @address || '%' AS is_match
				FROM warehouses
				WHERE (
					warehouse_full_address % @address
					OR warehouse_full_address ILIKE '%' || @address || '%'
				)
				-- Limit to sources service can access
				AND source IN (
					SELECT name FROM integrations WHERE type = 'scheduling' AND service_id = @serviceID
				)
			)
			SELECT *
			FROM warehouse_distances
			ORDER BY
				is_match DESC,
				trgm_dist;
		`, sql.Named("address", address),
			sql.Named("serviceID", serviceID))
	}
}
