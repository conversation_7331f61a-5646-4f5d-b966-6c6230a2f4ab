package warehouse

import (
	"context"
	"fmt"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Upsert(ctx context.Context, warehouse *models.Warehouse) error {
	return rds.WithContext(ctx).Clauses(
		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "warehouse_id"},
				{Name: "source"},
			},
			UpdateAll: true,
		},
	).Create(warehouse).Error
}

func OnboardWarehouses(ctx context.Context, warehouses []models.Warehouse) error {
	return rds.WithContext(ctx).CreateInBatches(&warehouses, 1000).
		Clauses(clause.Returning{}, clause.OnConflict{
			Columns:   []clause.Column{{Name: "source"}, {Name: "warehouse_id"}},
			UpdateAll: true,
		}).Error
}

// CreateWarehouseAddress creates an address association from a load
func CreateWarehouseAddress(
	ctx context.Context,
	warehouse models.Warehouse,
	load models.Load,
	requestType models.RequestType,
) error {

	var address models.WarehouseAddress

	switch requestType {
	case models.RequestTypePickup:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Pickup.CompanyCoreInfo),
		}

	case models.RequestTypeDropoff:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Consignee.CompanyCoreInfo),
		}

	default:
		return fmt.Errorf("invalid request type: %s", requestType)
	}

	return rds.WithContext(ctx).
		Model(&warehouse).
		Association("Addresses").
		Append(&address)
}
