package rds

import (
	"context"
	"fmt"
	stdlog "log"
	"os"
	"time"

	"github.com/uptrace/opentelemetry-go-extra/otelgorm"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds/vector"
)

// db is the gorm DB connection after calling Open().
// NOTE: Other packages must use WithContext() for all DB queries instead of accessing this directly.
var db *gorm.DB

// dbReader is the gorm DB connection for read operations.
// NOTE: Other packages must use WithContextReader() for all DB queries instead of accessing this directly.
var dbReader *gorm.DB

// EnvConfig defines env variables for DB configuration
type EnvConfig struct {
	DBHost       string `envconfig:"DB_HOST" required:"true"`
	DBHostReader string `envconfig:"DB_HOST_READER"`
	DBName       string `envconfig:"DB_NAME" required:"true"`

	// DBUser and DBPassword act differently in various environments:
	//   - In development, they store the actual database credentials directly.
	//   - In staging/production, they store the JSON keys for the username and password within AWS Secrets Manager
	//     or Azure Key Vault.
	//
	// Upon reading the configuration, these keys will be replaced with actual values.
	// This provides flexibility, allowing credentials to be stored under arbitrary JSON keys in AWS or Azure, which
	// some of our customers like Redwood already do.
	// Default JSON keys are "username" and "password" if not provided.
	DBUser     string `envconfig:"DB_USER" default:"username"`
	DBPassword string `envconfig:"DB_PASSWORD" default:"password"`

	// For prod only
	DBSecretARN string `envconfig:"DB_SECRET_ARN"`
}

// Open connects to a Postgres database using environment variables for configuration.
func Open(ctx context.Context, cfg EnvConfig, opts ...Option) error {
	options := &Options{
		AppEnv:               "dev",
		CloudRegion:          "us-east-1",
		GormConfig:           defaultGormConfig("dev"),
		IsOnPrem:             false,
		UseAWSSecretsManager: false,
		UseAzureKeyVault:     false,
	}

	for _, opt := range opts {
		opt(options)
	}

	// only used in WithContextReader
	appEnv = options.AppEnv

	if options.AppEnv == "staging" || options.AppEnv == "prod" {
		if options.UseAWSSecretsManager {
			// Fetch credentials from AWS Secret Manager.
			creds, err := readAWSSecret(ctx, cfg.DBSecretARN, options.CloudRegion)
			if err != nil {
				return err
			}

			if username, ok := creds[cfg.DBUser]; ok {
				cfg.DBUser, _ = username.(string)
			} else {
				return fmt.Errorf("username key %s not found in secret", cfg.DBUser)
			}

			if password, ok := creds[cfg.DBPassword]; ok {
				cfg.DBPassword, _ = password.(string)
			} else {
				return fmt.Errorf("password key %s not found in secret", cfg.DBUser)
			}
		}

		if options.UseAzureKeyVault {
			secretNames := []string{cfg.DBUser, cfg.DBPassword}

			// Fetch credentials from Azure Key Vault.
			creds, err := ReadAzureSecret(ctx, cfg.DBSecretARN, secretNames)
			if err != nil {
				return err
			}

			if username, ok := creds[cfg.DBUser]; ok {
				if usernameStr, ok := username.(string); ok {
					cfg.DBUser = usernameStr
				} else {
					return fmt.Errorf("username retrieved is not a string for key %s",
						cfg.DBUser)
				}
			} else {
				return fmt.Errorf("username key %s not found in secret", cfg.DBUser)
			}

			if password, ok := creds[cfg.DBPassword]; ok {
				if passwordStr, ok := password.(string); ok {
					cfg.DBPassword = passwordStr
				} else {
					return fmt.Errorf("password retrieved is not a string for key %s",
						cfg.DBPassword)
				}
			} else {
				return fmt.Errorf("password key %s not found in secret", cfg.DBPassword)
			}
		}
	}

	return OpenDirect(
		ctx,
		WithDBHost(cfg.DBHost),
		WithDBHostReader(cfg.DBHostReader),
		WithDBName(cfg.DBName),
		WithDBCredentials(cfg.DBUser, cfg.DBPassword),
		WithSSLMode(options.SSLMode),
		WithGormConfig(options.GormConfig),
	)
}

// OpenDirect connects to a Postgres database with explicit credentials.
// It establishes a primary connection and an optional reader connection if DBHostReader is specified.
// Reader connection failures are logged as warnings and don't affect the main connection.
// Queries must use WithContext() for the primary connection or WithContextReader() for the reader connection.
// Returns an error if the main connection or required setup steps fail.
func OpenDirect(ctx context.Context, opts ...Option) error {
	options := &Options{
		SSLMode:    "disable",
		GormConfig: defaultGormConfig("dev"),
	}

	for _, opt := range opts {
		opt(options)
	}

	dsn := buildDSN(options.DBHost, options.DBUser, options.DBName, options.DBPassword, options.SSLMode)
	var err error
	db, err = gorm.Open(postgres.Open(dsn), options.GormConfig)
	if err != nil {
		return fmt.Errorf("failed to open main connection: %w", err)
	}

	if err := configureDB(ctx, db, options, false); err != nil {
		return fmt.Errorf("failed to configure main connection: %w", err)
	}

	if options.DBHostReader != "" {
		readerDSN := buildDSN(
			options.DBHostReader,
			options.DBUser,
			options.DBName,
			options.DBPassword,
			options.SSLMode,
		)

		dbReader, err = gorm.Open(postgres.Open(readerDSN), options.GormConfig)
		if err != nil {
			log.Warn(ctx, "failed to open reader connection", zap.Error(err))
		} else if err := configureDB(ctx, dbReader, options, true); err != nil {
			log.Warn(ctx, "failed to configure reader connection", zap.Error(err))
		}
	}

	return nil
}

// buildDSN constructs a Postgres DSN string.
func buildDSN(host, user, dbName, password, sslMode string) string {
	return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
		host, 5432, user, dbName, password, sslMode)
}

// configureDB sets up a GORM connection with telemetry callbacks, and connection pooling.
func configureDB(ctx context.Context, db *gorm.DB, opts *Options, isReader bool) error {
	connType := opts.DBName
	if isReader {
		connType += "_reader"
	}

	if !opts.IsOnPrem {
		err := db.Use(otelgorm.NewPlugin(otelgorm.WithDBName(connType), otelgorm.WithoutQueryVariables()))
		if err != nil {
			log.Warnf(ctx, "failed to initialize Gorm Otel plugin for %s", connType, zap.Error(err))
		}
	}

	if err := sentryCallbacks(db); err != nil {
		return fmt.Errorf("failed to register Sentry DB callbacks for %s: %w", connType, err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get SQL DB connector for %s: %w", connType, err)
	}

	sqlDB.SetConnMaxIdleTime(time.Hour)
	sqlDB.SetConnMaxLifetime(24 * time.Hour)
	sqlDB.SetMaxIdleConns(100)
	sqlDB.SetMaxOpenConns(900)

	var version string

	dbConn := WithContext(ctx)
	if isReader {
		dbConn = WithContextReader(ctx)
	}

	if err := dbConn.Raw("SELECT version()").Scan(&version).Error; err != nil {
		log.Warnf(ctx, "connection test query failed for %s", connType, zap.Error(err))
		return nil
	}

	log.Infof(
		ctx,
		"Connected to %s DB", connType,
		zap.String("user", opts.DBUser),
		zap.String("host", opts.DBHost),
		zap.String("dbName", opts.DBName),
		zap.String("postgresVersion", version),
	)

	return nil
}

// defaultGormConfig returns a default GORM configuration based on the environment.
func defaultGormConfig(appEnv string) *gorm.Config {
	switch appEnv {
	case "dev":
		return &gorm.Config{
			DisableForeignKeyConstraintWhenMigrating: true,
			Logger: newGormLogger(gormlogger.Config{
				Colorful:                  true,
				IgnoreRecordNotFoundError: true,
				LogLevel:                  gormlogger.Warn,
				SlowThreshold:             200 * time.Millisecond,
			}),
			PrepareStmt: true,
		}
	// TODO: foreign key constraints not being made in prod programmatically, see Jin for more info
	case "staging", "prod":
		return &gorm.Config{
			DisableForeignKeyConstraintWhenMigrating: true,
			Logger: newGormLogger(gormlogger.Config{
				IgnoreRecordNotFoundError: true,
				LogLevel:                  gormlogger.Warn,
				// NOTE: It's critical that we don't log PII data.
				ParameterizedQueries: true,
				// Disable slow SQL logging, which can leak sensitive information.
				SlowThreshold: 0,
			}),
			PrepareStmt: true,
		}
	default:
		return &gorm.Config{}
	}
}

// MustOpenTestDB opens a connection to test_db on localhost (for integration tests in CI)
func MustOpenTestDB(ctx context.Context, dbname string) {
	err := OpenDirect(ctx,
		WithDBHost("localhost"),
		WithDBName(dbname),
		WithDBCredentials("postgres", "password"),
		WithSSLMode("disable"),
		WithGormConfig(&gorm.Config{PrepareStmt: true}),
	)

	if err != nil {
		panic(err)
	}

	if err := AutoMigrate(ctx); err != nil {
		panic(err)
	}
}

func newGormLogger(loggerConfig gormlogger.Config) gormlogger.Interface {
	return gormlogger.New(
		stdlog.New(os.Stdout, "\n", stdlog.LstdFlags), // io writer
		loggerConfig,
	)
}

// GetVectorRepository returns a repository for interacting with vector embeddings
func GetVectorRepository(ctx context.Context) *vector.Repository {
	if db == nil {
		log.Error(ctx, "Database connection is not initialized")
		return nil
	}

	return vector.New(WithContext(ctx))
}
