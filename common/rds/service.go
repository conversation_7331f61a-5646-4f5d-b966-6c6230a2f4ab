package rds

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

func GetServiceByID(ctx context.Context, id uint) (service models.Service, err error) {
	isDev := os.Getenv("APP_ENV") == "dev"
	redisKey := fmt.Sprintf("service-%d", id)

	if !isDev {
		cachedService, found, cacheErr := redis.GetKey[models.Service](ctx, redisKey)
		if cacheErr == nil && found {
			return cachedService, nil
		}
	}

	err = WithContextReader(ctx).Where("id = ?", id).First(&service).Error
	if err != nil {
		return service, err
	}

	if !isDev {
		setErr := redis.SetKey(ctx, redisKey, service, 10*time.Minute)
		if setErr != nil {
			log.Warn(ctx, "failed to set service cache in Redis", zap.Error(setErr))
		}
	}

	return service, nil
}

func GetServiceWithPreload(ctx context.Context, id uint) (service models.Service, err error) {
	return service, WithContextReader(ctx).Where("id = ?", id).Preload(clause.Associations).
		Preload("QuickQuoteConfig.Sender").First(&service).Error
}

func GetServiceByNickname(ctx context.Context, nickname string) (service models.Service, err error) {
	// Nickname should already always be lowercase
	return service, WithContextReader(ctx).Where("nickname = ?", nickname).First(&service).Error
}

func GetServiceByNicknameWithPreload(ctx context.Context, nickname string) (service models.Service, err error) {
	return service, WithContextReader(ctx).Where("nickname = ?", nickname).Preload(clause.Associations).
		Preload("QuickQuoteConfig.Sender").First(&service).Error
}

func CreateService(ctx context.Context, service *models.Service) error {
	var lowercaseDomains []string

	for _, domain := range service.EmailDomains {
		lowercaseDomains = append(lowercaseDomains, strings.ToLower(domain))
	}
	service.EmailDomains = lowercaseDomains

	err := WithContext(ctx).Create(service).Error
	if err == nil && os.Getenv("APP_ENV") != "dev" {
		// After successful creation, cache the new service entry
		redisKey := fmt.Sprintf("service-%d", service.ID)
		if setErr := redis.SetKey(ctx, redisKey, *service, 10*time.Minute); setErr != nil {
			log.Warn(ctx, "failed to set service cache in Redis after create", zap.Error(setErr))
		}
	}
	return err
}

func GetServiceByDomain(ctx context.Context, domain string) (service models.Service, err error) {
	return service, WithContextReader(ctx).
		Where("? = ANY(email_domains)", strings.ToLower(domain)).
		First(&service).Error
}

func GetServiceAll(ctx context.Context) (services []models.Service, err error) {
	return services, WithContextReader(ctx).Find(&services).Error
}

func GetFrontServiceByDomain(ctx context.Context, domain string) (service models.Service, err error) {
	return service, WithContextReader(ctx).
		Where("? = ANY(email_domains) AND front_auth_token IS NOT NULL", strings.ToLower(domain)).
		First(&service).Error
}

func GetFrontServiceBySubdomain(ctx context.Context, domain string) (service models.Service, err error) {
	return service, WithContextReader(ctx).
		Where("front_tenant_subdomain = ? AND front_auth_token IS NOT NULL", strings.ToLower(domain)).
		First(&service).Error
}

func UpdateService(ctx context.Context, service *models.Service) error {
	err := WithContext(ctx).Model(service).Updates(*service).Error
	if err == nil && os.Getenv("APP_ENV") != "dev" {
		redisKey := fmt.Sprintf("service-%d", service.ID)
		if delErr := redis.DeleteKey(ctx, redisKey); delErr != nil {
			log.Warn(ctx, "failed to invalidate service cache in Redis after update", zap.Error(delErr))
		}
	}
	return err
}
