// Package tmslocation provides fuzzy search functionality for TMS locations
//
// Benchmarks (requires LIVE_TEST=true and DB environment variables):
//
//	LIVE_TEST=true go test -bench=. -v
//
// Specific benchmark tests:
//
//	LIVE_TEST=true go test -bench=BenchmarkFuzzySearchExactMatch -v      # Exact match performance
//	LIVE_TEST=true go test -bench=BenchmarkFuzzySearchFuzzyMatch -v      # Fuzzy match performance
//	LIVE_TEST=true go test -bench=BenchmarkFuzzySearchComprehensive -v   # All search patterns
//
// Each benchmark tests multiple search patterns using real data:
//   - city+state searches
//   - exact name searches
//   - address line searches
//   - misspelled queries
//   - long combined entries
//   - short partial entries
//
// Required environment variables for integration tests and benchmarks:
//
//	DB_HOST, DB_NAME, DB_USER, DB_PASSWORD
package tmslocation

import (
	"context"
	"math/rand"
	"os"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// BenchmarkSearchVariation represents a single benchmark test case
type BenchmarkSearchVariation struct {
	searchTerm       string
	description      string
	expectExactMatch bool
	location         models.TMSLocation
}

// BenchmarkResult captures both performance and accuracy metrics
type BenchmarkResult struct {
	searchTerm       string
	description      string
	duration         time.Duration
	resultCount      int
	relevanceScore   float64
	foundOriginal    bool
	originalPosition int // 1-based position of original location in results, 0 if not found
}

// TestPreprocessSearchTerm tests the search term preprocessing functionality
func TestPreprocessSearchTerm(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Basic address abbreviations",
			input:    "5353 Broadmoor Ave",
			expected: "5353 broadmoor avenue",
		},
		{
			name:     "Boulevard abbreviation",
			input:    "3959 Oscar Nelson Jr. Blvd",
			expected: "3959 oscar nelson jr. boulevard",
		},
		{
			name:     "Company name with location",
			input:    "Roskam Banking - Broadmoor",
			expected: "roskam banking - broadmoor",
		},
		{
			name:     "Multiple abbreviations in search",
			input:    "National Strand Products 3959 Blvd.",
			expected: "national strand products 3959 boulevard",
		},
		{
			name:     "Business entity normalization",
			input:    "ABC Corp & Co LLC",
			expected: "abc corp and co llc",
		},
		{
			name:     "Extra whitespace cleanup",
			input:    "  National    Strand   Products  ",
			expected: "national strand products",
		},
		{
			name:     "Mixed case with address",
			input:    "ROSKAM Banking 5353 BROADMOOR Ave",
			expected: "roskam banking 5353 broadmoor avenue",
		},
		{
			name:     "Distribution center search",
			input:    "Walmart Dist Ctr",
			expected: "walmart distribution center",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := preprocessSearchTerm(tc.input)
			assert.Equal(t, tc.expected, result,
				"preprocessSearchTerm(%q) = %q, want %q", tc.input, result, tc.expected)
		})
	}
}

// TestCalculateAdaptiveThreshold tests the adaptive threshold calculation
func TestCalculateAdaptiveThreshold(t *testing.T) {
	testCases := []struct {
		name           string
		searchTerm     string
		expectedMin    float64
		expectedMax    float64
		expectedReason string
	}{
		{
			name:           "Very short term",
			searchTerm:     "Na",
			expectedMin:    0.2,
			expectedMax:    0.3,
			expectedReason: "Short terms need lower threshold for more results",
		},
		{
			name:           "Company name search",
			searchTerm:     "Roskam",
			expectedMin:    0.25,
			expectedMax:    0.35,
			expectedReason: "Single word company name",
		},
		{
			name:           "Company with type",
			searchTerm:     "National Strand",
			expectedMin:    0.3,
			expectedMax:    0.4,
			expectedReason: "Two words, medium length",
		},
		{
			name:           "Full company name",
			searchTerm:     "National Strand Products",
			expectedMin:    0.35,
			expectedMax:    0.45,
			expectedReason: "Three words should increase precision",
		},
		{
			name:           "Address search",
			searchTerm:     "5353 Broadmoor Avenue",
			expectedMin:    0.35,
			expectedMax:    0.45,
			expectedReason: "Address search needs good precision",
		},
		{
			name:           "Full location search",
			searchTerm:     "Roskam Banking 5353 Broadmoor Ave",
			expectedMin:    0.4,
			expectedMax:    0.5,
			expectedReason: "Very long terms need high precision",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			threshold := calculateAdaptiveThreshold(tc.searchTerm)

			assert.GreaterOrEqual(t, threshold, tc.expectedMin,
				"Threshold for %q should be >= %f (reason: %s)",
				tc.searchTerm, tc.expectedMin, tc.expectedReason)

			assert.LessOrEqual(t, threshold, tc.expectedMax,
				"Threshold for %q should be <= %f (reason: %s)",
				tc.searchTerm, tc.expectedMax, tc.expectedReason)

			t.Logf("Search term %q (len=%d, words=%d) → threshold=%f",
				tc.searchTerm, len(tc.searchTerm), len(strings.Fields(tc.searchTerm)), threshold)
		})
	}
}

// BenchmarkFuzzySearchExactMatch benchmarks exact match scenarios
// To run: LIVE_TEST=true go test -bench=BenchmarkFuzzySearchExactMatch -v
func BenchmarkFuzzySearchExactMatch(b *testing.B) {
	setupBenchmarkDB(b)
	ctx := context.Background()

	// Get 5 random locations for testing
	testLocations := fetchRandomTMSLocations(ctx, 5)
	if len(testLocations) == 0 {
		b.Skip("No test locations found in database")
	}

	// Generate exact match variations (city+state, exact name)
	var exactMatchVariations []BenchmarkSearchVariation
	for _, location := range testLocations {
		variations := generateBenchmarkSearchVariations(location)
		for _, variation := range variations {
			if variation.description == "city_state" ||
				variation.description == "exact_name" ||
				variation.description == "city_only" {

				exactMatchVariations = append(exactMatchVariations, variation)
			}
		}
	}

	if len(exactMatchVariations) == 0 {
		b.Skip("No exact match variations generated")
	}

	b.Logf("Running exact match benchmark with %d variations", len(exactMatchVariations))

	var totalResults []BenchmarkResult

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, variation := range exactMatchVariations {
			result := runBenchmarkSearch(ctx, variation)
			if i == 0 { // Only collect detailed results on first iteration
				totalResults = append(totalResults, result)
			}
		}
	}
	b.StopTimer()

	// Report detailed results after benchmark
	if len(totalResults) > 0 {
		logBenchmarkResults(b, "Exact Match", totalResults)
	}
}

// BenchmarkFuzzySearchFuzzyMatch benchmarks fuzzy matching scenarios
// To run: LIVE_TEST=true go test -bench=BenchmarkFuzzySearchFuzzyMatch -v
func BenchmarkFuzzySearchFuzzyMatch(b *testing.B) {
	setupBenchmarkDB(b)
	ctx := context.Background()

	// Get 5 random locations for testing
	testLocations := fetchRandomTMSLocations(ctx, 5)
	if len(testLocations) == 0 {
		b.Skip("No test locations found in database")
	}

	// Generate fuzzy match variations (misspellings, partial matches, etc.)
	var fuzzyMatchVariations []BenchmarkSearchVariation
	for _, location := range testLocations {
		variations := generateBenchmarkSearchVariations(location)
		for _, variation := range variations {
			if variation.description == "misspelling" || variation.description == "address_line1" ||
				variation.description == "short_entry" || variation.description == "short_entry_number" {
				fuzzyMatchVariations = append(fuzzyMatchVariations, variation)
			}
		}
	}

	if len(fuzzyMatchVariations) == 0 {
		b.Skip("No fuzzy match variations generated")
	}

	b.Logf("Running fuzzy match benchmark with %d variations", len(fuzzyMatchVariations))

	var totalResults []BenchmarkResult

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, variation := range fuzzyMatchVariations {
			result := runBenchmarkSearch(ctx, variation)
			if i == 0 { // Only collect detailed results on first iteration
				totalResults = append(totalResults, result)
			}
		}
	}
	b.StopTimer()

	// Report detailed results after benchmark
	if len(totalResults) > 0 {
		logBenchmarkResults(b, "Fuzzy Match", totalResults)
	}
}

// BenchmarkFuzzySearchComprehensive benchmarks all search patterns
// To run: LIVE_TEST=true go test -bench=BenchmarkFuzzySearchComprehensive -v
func BenchmarkFuzzySearchComprehensive(b *testing.B) {
	setupBenchmarkDB(b)
	ctx := context.Background()

	// Get 5 random locations for testing
	testLocations := fetchRandomTMSLocations(ctx, 5)
	if len(testLocations) == 0 {
		b.Skip("No test locations found in database")
	}

	// Generate all variations for comprehensive testing
	var allVariations []BenchmarkSearchVariation
	for _, location := range testLocations {
		variations := generateBenchmarkSearchVariations(location)
		allVariations = append(allVariations, variations...)
	}

	if len(allVariations) == 0 {
		b.Skip("No search variations generated")
	}

	b.Logf("Running comprehensive benchmark with %d variations across %d locations",
		len(allVariations), len(testLocations))

	var totalResults []BenchmarkResult

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, variation := range allVariations {
			result := runBenchmarkSearch(ctx, variation)
			if i == 0 { // Only collect detailed results on first iteration
				totalResults = append(totalResults, result)
			}
		}
	}
	b.StopTimer()

	// Report detailed results after benchmark
	if len(totalResults) > 0 {
		logBenchmarkResults(b, "Comprehensive", totalResults)
	}
}

// fetchRandomTMSLocations gets random TMS locations from the database for testing
func fetchRandomTMSLocations(ctx context.Context, count int) []models.TMSLocation {
	var locations []models.TMSLocation

	err := rds.WithContext(ctx).
		Where("name_address IS NOT NULL AND name_address != ''").
		Where("LENGTH(name_address) > 10"). // Ensure we have meaningful data
		Order("RANDOM()").
		Limit(count).
		Find(&locations).Error

	if err != nil {
		log.Error(ctx, "Could not fetch random locations from RDS", zap.Error(err))
		return []models.TMSLocation{}
	}

	log.Info(ctx, "Fetched random locations for testing", zap.Int("count", len(locations)))
	return locations
}

// introduceTypo introduces a small typo into a string for testing fuzzy matching
func introduceTypo(s string) string {
	if len(s) < 3 {
		return s
	}

	runes := []rune(s)
	//nolint:gosec
	pos := 1 + rand.Intn(len(runes)-2) // Don't modify first or last character

	// Common typo types
	typoTypes := []string{"swap", "replace", "omit"}
	//nolint:gosec
	typoType := typoTypes[rand.Intn(len(typoTypes))]

	switch typoType {
	case "swap":
		// Swap two adjacent characters
		if pos < len(runes)-1 {
			runes[pos], runes[pos+1] = runes[pos+1], runes[pos]
		}
	case "replace":
		// Replace with similar looking character
		char := runes[pos]
		switch char {
		case 'a':
			runes[pos] = 'e'
		case 'e':
			runes[pos] = 'a'
		case 'o':
			runes[pos] = '0'
		case 'i':
			runes[pos] = '1'
		case 's':
			runes[pos] = 'z'
		default:
			// Generic replacement
			runes[pos] = 'x'
		}
	case "omit":
		// Remove a character
		runes = slices.Delete(runes, pos, pos+1)
	}

	return string(runes)
}

// calculateRelevanceScore calculates a simple relevance score between search term and result
func calculateRelevanceScore(searchTerm string, result models.TMSLocation) float64 {
	if searchTerm == "" {
		return 1.0
	}

	searchLower := strings.ToLower(searchTerm)
	resultLower := strings.ToLower(result.NameAddress)

	// Simple scoring based on word overlap
	searchWords := strings.Fields(searchLower)
	resultWords := strings.Fields(resultLower)

	if len(searchWords) == 0 {
		return 1.0
	}

	matches := 0
	for _, searchWord := range searchWords {
		if len(searchWord) < 3 { // Skip very short words
			continue
		}

		for _, resultWord := range resultWords {
			if strings.Contains(resultWord, searchWord) || strings.Contains(searchWord, resultWord) {
				matches++
				break
			}
		}
	}

	return float64(matches) / float64(len(searchWords))
}

// generateBenchmarkSearchVariations creates the specific search patterns requested by the user
func generateBenchmarkSearchVariations(location models.TMSLocation) []BenchmarkSearchVariation {
	variations := make([]BenchmarkSearchVariation, 0, 6)

	// 1. City + State search
	if location.City != "" && location.State != "" {
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       location.City + " " + location.State,
			description:      "city_state",
			expectExactMatch: false,
			location:         location,
		})
	} else if location.City != "" {
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       location.City,
			description:      "city_only",
			expectExactMatch: false,
			location:         location,
		})
	}

	// 2. Name search (exact company name)
	if location.Name != "" {
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       location.Name,
			description:      "exact_name",
			expectExactMatch: true,
			location:         location,
		})
	}

	// 3. Address Line 1 search
	if location.AddressLine1 != "" {
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       location.AddressLine1,
			description:      "address_line1",
			expectExactMatch: false,
			location:         location,
		})
	}

	// 4. Misspelling of name (typo)
	if location.Name != "" && len(location.Name) > 5 {
		typoName := introduceTypo(location.Name)
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       typoName,
			description:      "misspelling",
			expectExactMatch: false,
			location:         location,
		})
	}

	// 5. Long entry (name + address combined)
	if location.Name != "" && location.AddressLine1 != "" {
		longEntry := location.Name + " " + location.AddressLine1
		if location.City != "" {
			longEntry += " " + location.City
		}
		variations = append(variations, BenchmarkSearchVariation{
			searchTerm:       longEntry,
			description:      "long_entry",
			expectExactMatch: false,
			location:         location,
		})
	}

	// 6. Short entry (first word of name or first number from address)
	if location.Name != "" {
		words := strings.Fields(location.Name)
		if len(words) > 0 {
			variations = append(variations, BenchmarkSearchVariation{
				searchTerm:       words[0],
				description:      "short_entry",
				expectExactMatch: false,
				location:         location,
			})
		}
	} else if location.AddressLine1 != "" {
		// Extract first number from address for short entry
		addressParts := strings.Fields(location.AddressLine1)
		for _, part := range addressParts {
			if len(part) > 0 && (part[0] >= '0' && part[0] <= '9') {
				variations = append(variations, BenchmarkSearchVariation{
					searchTerm:       part,
					description:      "short_entry_number",
					expectExactMatch: false,
					location:         location,
				})
				break
			}
		}
	}

	return variations
}

// setupBenchmarkDB initializes database connection for benchmarks
func setupBenchmarkDB(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping RDS benchmark in short mode")
	}

	if os.Getenv("LIVE_TEST") != "true" {
		b.Skip("skipping benchmark: run with LIVE_TEST=true to enable")
	}

	ctx := context.Background()

	err := rds.OpenDirect(ctx,
		rds.WithDBHost("example"),
		rds.WithDBName("example"),
		rds.WithDBCredentials("example", "example"),
		rds.WithSSLMode("disable"),
		rds.WithGormConfig(&gorm.Config{PrepareStmt: true}))

	if err != nil {
		b.Fatalf("Failed to connect to database: %v", err)
	}
}

// runBenchmarkSearch executes a single search and captures metrics
func runBenchmarkSearch(ctx context.Context, variation BenchmarkSearchVariation) BenchmarkResult {
	query := SearchLocationsQuery{
		TMSID:       variation.location.TMSIntegrationID,
		NameAddress: variation.searchTerm,
	}

	start := time.Now()
	results, err := FuzzySearchByNameAddress(ctx, query)
	duration := time.Since(start)

	result := BenchmarkResult{
		searchTerm:       variation.searchTerm,
		description:      variation.description,
		duration:         duration,
		resultCount:      len(results),
		relevanceScore:   0.0,
		foundOriginal:    false,
		originalPosition: 0,
	}

	if err != nil {
		return result
	}

	// Calculate relevance score for top result
	if len(results) > 0 {
		result.relevanceScore = calculateRelevanceScore(variation.searchTerm, results[0])
	}

	// Check if original location was found and at what position
	for i, res := range results {
		if res.ID == variation.location.ID {
			result.foundOriginal = true
			result.originalPosition = i + 1 // 1-based position
			break
		}
	}

	return result
}

// logBenchmarkResults provides detailed analysis of benchmark results
func logBenchmarkResults(b *testing.B, benchmarkType string, results []BenchmarkResult) {
	if len(results) == 0 {
		return
	}

	b.Logf("\n=== %s Benchmark Results ===", benchmarkType)

	// Calculate aggregate statistics
	var totalDuration time.Duration
	var totalRelevance float64
	var foundCount int
	var totalResults int
	categoryStats := make(map[string][]BenchmarkResult)

	for _, result := range results {
		totalDuration += result.duration
		totalRelevance += result.relevanceScore
		totalResults += result.resultCount

		if result.foundOriginal {
			foundCount++
		}

		// Group by search type
		categoryStats[result.description] = append(categoryStats[result.description], result)
	}

	// Overall statistics
	avgDuration := totalDuration / time.Duration(len(results))
	avgRelevance := totalRelevance / float64(len(results))
	avgResults := float64(totalResults) / float64(len(results))
	accuracyRate := float64(foundCount) / float64(len(results)) * 100

	b.Logf("Overall Stats:")
	b.Logf("  - Total searches: %d", len(results))
	b.Logf("  - Average duration: %v", avgDuration)
	b.Logf("  - Average relevance score: %.3f", avgRelevance)
	b.Logf("  - Average results per search: %.1f", avgResults)
	b.Logf("  - Accuracy rate (found original): %.1f%%", accuracyRate)

	// Per-category breakdown
	b.Logf("\nPer-Category Stats:")
	for category, categoryResults := range categoryStats {
		if len(categoryResults) == 0 {
			continue
		}

		var catDuration time.Duration
		var catRelevance float64
		var catFound int
		var catResults int

		for _, result := range categoryResults {
			catDuration += result.duration
			catRelevance += result.relevanceScore
			catResults += result.resultCount
			if result.foundOriginal {
				catFound++
			}
		}

		catAvgDuration := catDuration / time.Duration(len(categoryResults))
		catAvgRelevance := catRelevance / float64(len(categoryResults))
		catAvgResults := float64(catResults) / float64(len(categoryResults))
		catAccuracy := float64(catFound) / float64(len(categoryResults)) * 100

		b.Logf("  %s (%d searches):", category, len(categoryResults))
		b.Logf("    - Avg duration: %v", catAvgDuration)
		b.Logf("    - Avg relevance: %.3f", catAvgRelevance)
		b.Logf("    - Avg results: %.1f", catAvgResults)
		b.Logf("    - Accuracy: %.1f%%", catAccuracy)
	}

	// Performance warnings
	slowSearches := 0
	lowRelevanceSearches := 0
	for _, result := range results {
		if result.duration > 500*time.Millisecond {
			slowSearches++
		}
		if result.relevanceScore < 0.3 && result.searchTerm != "" {
			lowRelevanceSearches++
		}
	}

	if slowSearches > 0 {
		b.Logf("\n⚠ Warning: %d searches took longer than 500ms", slowSearches)
	}
	if lowRelevanceSearches > 0 {
		b.Logf("⚠ Warning: %d searches had low relevance scores (<0.3)", lowRelevanceSearches)
	}

	b.Logf("=== End %s Benchmark Results ===\n", benchmarkType)
}
