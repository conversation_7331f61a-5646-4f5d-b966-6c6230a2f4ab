package tmslocation

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

const (
	EmptyTMSLocationsSlice = "empty slice of TMSLocations"
)

// RefreshTMSLocations updates existing TMS locations and inserts new ones in the database.
// It uses an upsert operation to efficiently handle both new and updated locations.
func RefreshTMSLocations(ctx context.Context, locations *[]models.TMSLocation) error {
	if len(*locations) == 0 {
		return errors.New(EmptyTMSLocationsSlice)
	}

	// we require locations to have a FullAddress and NameAddress, so we need to set it if it's not already set
	start := 0
	end := len(*locations) - 1
	mid := end / 2

	if strings.TrimSpace((*locations)[start].FullAddress) == "" ||
		strings.TrimSpace((*locations)[end].FullAddress) == "" ||
		strings.TrimSpace((*locations)[mid].FullAddress) == "" {
		log.Warn(ctx, `manually assigning TMSLocation.FullAddress but TMS.GetLocations() should be updated
		to avoid future slowdowns due to large O(n) loop`)

		for i := range *locations {
			(*locations)[i].FullAddress = models.ConcatAddress((*locations)[i].CompanyCoreInfo)
			(*locations)[i].NameAddress = (*locations)[i].Name + ", " + (*locations)[i].FullAddress
		}
	}

	// Upsert locations to db
	// "UpdateAll: true" can sometimes behave unpredictably with batch
	// operations, so we explicitly list the columns to update instead.
	return rds.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns: []clause.Column{{Name: "tms_integration_id"}, {Name: "external_tms_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"name",
					"address_line1",
					"address_line2",
					"city",
					"state",
					"zipcode",
					"country",
					"contact",
					"phone",
					"email",
					"emails",
					"updated_at",
					"full_address",
					"appt_required",
					"driver_loading_responsibility",
					"is_shipper",
					"is_consignee",
					"tms_carrier_id",
					"latitude",
					"longitude",
					"point",
					"notes",
				}),
			},
		).
		CreateInBatches(&locations, 1000).
		Error
}

func Create(ctx context.Context, location models.TMSLocation) error {
	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(&location).Error
}
