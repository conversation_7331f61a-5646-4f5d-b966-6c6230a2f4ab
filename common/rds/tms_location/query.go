package tmslocation

import (
	"context"
	"database/sql"
	"fmt"
	"regexp"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchLocationsQuery struct {
	TMSID uint `json:"tmsID"`
	models.CompanyCoreInfo
	MileRadius  float64 `json:"mileRadius,omitempty"`
	City        string  `json:"city,omitempty"`
	NameAddress string  `json:"nameAddress,omitempty"`
	State       string  `json:"state,omitempty"`
	Zip         string  `json:"zip,omitempty"`
	Type        string  `json:"type,omitempty"` // to search for locations with an associated "carrier"
}

const MileToMeterConversionFactor = 1609.34 // 1 mile = 1609.34 meters

// Get locations by TMS ID, ordered by name in ascending order
func GetTMSLocationsByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSLocation, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", query.TMSID).Order("name ASC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

// This function is used by the "Name" autocomplete search input on the FE
func FuzzySearchByName(ctx context.Context, query SearchLocationsQuery) (res []models.TMSLocation, err error) {
	var locationNameSearchThreshold = 0.3

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchName(ctx, query.Name, query.TMSID, locationNameSearchThreshold)).
		Find(&res).Error

	return res, err
}

// This function is used by the "Name/Address" autocomplete search input on the FE
func FuzzySearchByNameAddress(ctx context.Context, query SearchLocationsQuery) (res []models.TMSLocation, err error) {
	// Use enhanced search with adaptive strategy
	return EnhancedFuzzySearchByNameAddress(ctx, query)
}

// EnhancedFuzzySearchByNameAddress provides an optimized search implementation with:
// 1. Adaptive similarity thresholds based on search term characteristics
// 2. Multi-stage search strategy (exact -> fuzzy -> fallback)
// 3. Better ranking algorithm considering multiple factors
// 4. Performance optimizations for large datasets
func EnhancedFuzzySearchByNameAddress(
	ctx context.Context,
	query SearchLocationsQuery,
) (res []models.TMSLocation, err error) {

	if query.NameAddress == "" {
		return res, nil
	}

	// Preprocess search term
	searchTerm := preprocessSearchTerm(query.NameAddress)

	// Stage 1: Try exact/prefix matches first (fastest)
	if results, err := exactMatchSearch(ctx, query.TMSID, searchTerm); err == nil && len(results) > 0 {
		log.Info(ctx, "found exact matches for name/address search",
			zap.String("searchTerm", searchTerm), zap.Int("count", len(results)))
		return results, nil
	}

	// Stage 2: Try fuzzy search with adaptive threshold
	threshold := calculateAdaptiveThreshold(searchTerm)

	err = rds.WithContext(ctx).
		Scopes(enhancedFuzzyMatchNameAddress(ctx, searchTerm, query.TMSID, threshold)).
		Find(&res).Error

	if err != nil {
		return res, err
	}

	log.Info(ctx, "found fuzzy matches for name/address search",
		zap.String("searchTerm", searchTerm),
		zap.Float64("threshold", threshold),
		zap.Int("count", len(res)))

	// Stage 3: If still no good results and term is short, try more aggressive fuzzy search
	if len(res) < 3 && len(searchTerm) <= 10 {
		fallbackThreshold := threshold * 0.7 // Lower threshold for more results
		var fallbackResults []models.TMSLocation

		err = rds.WithContext(ctx).
			Scopes(enhancedFuzzyMatchNameAddress(ctx, searchTerm, query.TMSID, fallbackThreshold)).
			Find(&fallbackResults).Error

		if err == nil && len(fallbackResults) > len(res) {
			log.Info(ctx, "using fallback fuzzy search results",
				zap.Float64("fallbackThreshold", fallbackThreshold),
				zap.Int("fallbackCount", len(fallbackResults)))
			res = fallbackResults
		}
	}

	return res, err
}

// preprocessSearchTerm cleans and normalizes the search term for better matching
func preprocessSearchTerm(term string) string {
	// Remove extra whitespace and convert to lower case for normalization
	term = strings.TrimSpace(term)
	termLower := strings.ToLower(term)

	// Replace common abbreviations that might affect matching
	// Note: Order matters - do more specific patterns first
	replacements := []struct {
		pattern     string
		replacement string
	}{
		{" st.", " street"},
		{" st ", " street "},
		{" ave.", " avenue"},
		{" ave ", " avenue "},
		{" blvd.", " boulevard"},
		{" blvd ", " boulevard "},
		{" rd.", " road"},
		{" rd ", " road "},
		{" dr.", " drive"},
		{" dr ", " drive "},
		{" ct.", " court"},
		{" ct ", " court "},
		{" ln.", " lane"},
		{" ln ", " lane "},
		{" dist.", " distribution"},
		{" dist ", " distribution "},
		{" ctr.", " center"},
		{" ctr ", " center "},
		{" cntr.", " center"},
		{" cntr ", " center "},
		{" &", " and"},
	}

	// Apply replacements
	for _, r := range replacements {
		termLower = strings.ReplaceAll(termLower, r.pattern, r.replacement)
	}

	// Handle abbreviations at the end of the string (no trailing space)
	endReplacements := map[string]string{
		" st$":   " street",
		" ave$":  " avenue",
		" blvd$": " boulevard",
		" rd$":   " road",
		" dr$":   " drive",
		" ct$":   " court",
		" ln$":   " lane",
		" ctr$":  " center",
		" cntr$": " center",
	}

	for pattern, replacement := range endReplacements {
		re := regexp.MustCompile(pattern)
		termLower = re.ReplaceAllString(termLower, replacement)
	}

	// Clean up multiple spaces
	termLower = regexp.MustCompile(`\s+`).ReplaceAllString(termLower, " ")

	return strings.TrimSpace(termLower)
}

// calculateAdaptiveThreshold determines the optimal similarity threshold based on search term characteristics
func calculateAdaptiveThreshold(searchTerm string) float64 {
	var baseThreshold float64

	termLength := len(searchTerm)
	words := strings.Fields(searchTerm)
	wordCount := len(words)

	switch {
	case termLength >= 20: // Long terms should have higher precision
		baseThreshold = 0.4
	case termLength >= 10: // Medium terms
		baseThreshold = 0.35
	case termLength >= 5: // Short terms need lower threshold
		baseThreshold = 0.3
	default: // Very short terms
		baseThreshold = 0.25
	}

	// Adjust for word count - more words generally means more specific intent
	if wordCount >= 3 {
		baseThreshold += 0.05
	} else if wordCount == 1 && termLength < 8 {
		baseThreshold -= 0.05
	}

	// Ensure threshold stays within reasonable bounds
	if baseThreshold < 0.2 {
		baseThreshold = 0.2
	} else if baseThreshold > 0.5 {
		baseThreshold = 0.5
	}

	return baseThreshold
}

// exactMatchSearch performs fast exact and prefix matching before falling back to fuzzy search
func exactMatchSearch(ctx context.Context, tmsID uint, searchTerm string) ([]models.TMSLocation, error) {
	var results []models.TMSLocation

	// Use Raw query with named parameters to safely handle the ORDER BY clause
	query := `
		SELECT *
		FROM tms_locations
		WHERE tms_integration_id = @tmsID 
		AND (name_address ILIKE @searchTerm OR name_address ILIKE @prefixTerm)
		ORDER BY 
			CASE WHEN name_address ILIKE @searchTerm THEN 1 ELSE 2 END, 
			name_address
		LIMIT 20
	`

	err := rds.WithContext(ctx).Raw(query,
		sql.Named("tmsID", tmsID),
		sql.Named("searchTerm", searchTerm),
		sql.Named("prefixTerm", searchTerm+"%")).
		Find(&results).Error

	return results, err
}

// escapeRegexMetachars escapes special regex characters to prevent regex injection
func escapeRegexMetachars(s string) string {
	// List of regex metacharacters that need to be escaped
	metachars := []string{
		"\\", "^", "$", ".", "|", "?", "*", "+", "(", ")", "[", "]", "{", "}",
	}

	escaped := s
	for _, char := range metachars {
		escaped = strings.ReplaceAll(escaped, char, "\\"+char)
	}
	return escaped
}

// enhancedFuzzyMatchNameAddress provides an improved fuzzy matching algorithm with better ranking
func enhancedFuzzyMatchNameAddress(
	ctx context.Context,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			// Escape regex metacharacters to prevent regex injection
			escapedSearchTerm := escapeRegexMetachars(searchTerm)

			// Enhanced multi-stage fuzzy search query with ranking system
			//
			// SEARCH STRATEGY:
			// 1. Pre-filter candidates using fast trigram (%) and ILIKE operators to avoid full table scan
			// 2. Calculate multiple similarity scores and ranking bonuses for each candidate
			// 3. Combine all scores into a weighted final ranking
			// 4. Order results by match quality (exact -> prefix -> contains -> fuzzy)
			//
			// SIMILARITY SCORES (PostgreSQL trigram distance, 0.0 = exact match, 1.0 = no similarity):
			// - name_addr_dist: Distance between search term and full name_address field
			// - name_dist: Distance between search term and name field only
			// - addr_dist: Distance between search term and address_line1 field only
			//
			// RANKING BONUSES & PENALTIES (lower scores = higher ranking):
			// - exact_bonus: 0.0 (exact match) -> 0.1 (prefix) -> 0.2 (contains) -> 1.0 (fuzzy only)
			// - word_boundary_bonus: 0.1 (matches word boundaries) vs 0.3 (partial word match)
			// - length_bonus: 0.0 (±5 chars) -> 0.1 (±10 chars) -> 0.2 (>10 chars difference)
			// - business_penalty: +0.05 for common business suffixes (inc, llc, corp, etc.)
			//
			// COMBINED SCORE WEIGHTS (total weighted score, lower = better ranking):
			// - exact_bonus × 0.4     (40% - prioritizes exact/prefix matches)
			// - name_addr_dist × 0.3  (30% - primary similarity score)
			// - name_dist × 0.2       (20% - name field similarity)
			// - addr_dist × 0.1       (10% - address field similarity)
			// - word_boundary_bonus × 0.1 (10% - word boundary preference)
			// - length_bonus × 0.05   (5% - length similarity preference)
			// - business_penalty      (unweighted penalty)
			//
			// MATCH TYPE CLASSIFICATION (for tie-breaking):
			// 1. Exact match (name_address ILIKE search_term)
			// 2. Prefix match (name_address ILIKE search_term || '%')
			// 3. Contains match (name_address ILIKE '%' || search_term || '%')
			// 4. Name fuzzy (name field trigram match)
			// 5. Address fuzzy (address_line1 field trigram match)
			//
			// This approach balances search quality with performance by:
			// - Using fast trigram matching (%) and ILIKE for initial filtering
			// - Computing detailed scores only for promising candidates
			// - Applying multiple ranking factors to surface the most relevant results
			//nolint:lll
			query := `
				WITH enhanced_location_search AS (
					SELECT *,
						-- Primary similarity scores
						name_address <-> @searchTerm AS name_addr_dist,
						name <-> @searchTerm AS name_dist,
						address_line1 <-> @searchTerm AS addr_dist,
						
						-- Exact match bonuses
						CASE WHEN name_address ILIKE @searchTerm THEN 0.0
							 WHEN name_address ILIKE @searchTerm || '%' THEN 0.1
							 WHEN name_address ILIKE '%' || @searchTerm || '%' THEN 0.2
							 ELSE 1.0 END AS exact_bonus,
						
						-- Word boundary matching with escaped search term to prevent regex injection
						CASE WHEN name_address ~* ('\y' || @escapedSearchTerm || '\y') THEN 0.1 ELSE 0.3 END AS word_boundary_bonus,
						
						-- Length matching bonus (prefer similar length matches)
						CASE WHEN ABS(LENGTH(name_address) - LENGTH(@searchTerm)) <= 5 THEN 0.0
							 WHEN ABS(LENGTH(name_address) - LENGTH(@searchTerm)) <= 10 THEN 0.1
							 ELSE 0.2 END AS length_bonus,
							 
						-- Company name priority (common business terms get slightly lower priority)
						CASE WHEN name_address ~* '\y(inc|llc|corp|company|ltd)\y' THEN 0.05 ELSE 0.0 END AS business_penalty
						
					FROM tms_locations 
					WHERE tms_integration_id = @tmsID 
					-- Filter to reasonable matches to avoid scanning entire table
					AND (
						name_address % @searchTerm 
						OR name_address ILIKE '%' || @searchTerm || '%'
						OR name % @searchTerm
						OR address_line1 % @searchTerm
					)
				),
				ranked_results AS (
					SELECT *,
						-- Combined ranking score (lower is better)
						(
							exact_bonus * 0.4 +
							name_addr_dist * 0.3 +
							name_dist * 0.2 +
							addr_dist * 0.1 +
							word_boundary_bonus * 0.1 +
							length_bonus * 0.05 +
							business_penalty
						) AS combined_score,
						
						-- Classification for tie-breaking
						CASE 
							WHEN name_address ILIKE @searchTerm THEN 1 -- Exact match
							WHEN name_address ILIKE @searchTerm || '%' THEN 2 -- Prefix match
							WHEN name_address ILIKE '%' || @searchTerm || '%' THEN 3 -- Contains match
							WHEN name % @searchTerm THEN 4 -- Name fuzzy
							ELSE 5 -- Address fuzzy
						END AS match_type
						
					FROM enhanced_location_search
				)
				SELECT *
				FROM ranked_results
				ORDER BY 
					match_type ASC,
					combined_score ASC,
					name_address ASC
				LIMIT 20;
			`

			return tx.Raw(query,
				sql.Named("searchTerm", searchTerm),
				sql.Named("escapedSearchTerm", escapedSearchTerm),
				sql.Named("tmsID", tmsID))
		})
	}
}

// This function is used by the "Address Line 1" autocomplete search input on the FE
func FuzzySearchByStreetAddress(ctx context.Context, query SearchLocationsQuery) (res []models.TMSLocation, err error) {
	var locationAddressSearchThreshold = 0.3

	if len(query.AddressLine1) < 3 {
		return res, nil
	}

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchStreetAddress(ctx, query.AddressLine1, query.TMSID, locationAddressSearchThreshold)).
		Find(&res).Error

	log.Info(ctx, "found locations by street fuzzy match", zap.Int("count", len(res)))

	return res, err
}

// This function is part of the LLM load building extraction process and as such, uses a higher similarity threshold
func GetLocationByAddress(
	ctx context.Context,
	tmsID uint,
	loc models.CompanyCoreInfo,
) (res models.TMSLocation, err error) {
	var locations []models.TMSLocation
	var locationFullAddressSearchThreshold = 0.5

	if len(models.ConcatAddress(loc)) < 8 {
		return res, nil
	}

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchFullAddress(ctx, models.ConcatAddress(loc), tmsID, locationFullAddressSearchThreshold)).
		Find(&locations).Error

	if err != nil {
		return res, err
	}

	log.Info(ctx, "found locations by address fuzzy match", zap.Int("count", len(locations)))

	if len(locations) == 1 {
		return locations[0], nil
	}

	// TMS may include duplicate, valid locations. And we can't rely on the name because broker's customer may
	// list the same location under different name than broker's TMS does.
	//  So we return the first match that has the same street #.
	streetNumber := extractStreetNumber(loc.AddressLine1)

	for _, location := range locations {
		if streetNumber != "" && strings.HasPrefix(location.AddressLine1, streetNumber) {
			return location, nil
		}
	}

	return res, fmt.Errorf("no unique match found for location")
}

func extractStreetNumber(address string) string {
	re := regexp.MustCompile(`^\d+\b`)
	match := re.FindString(address)
	return match
}

func fuzzyMatchName(
	ctx context.Context,
	name string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "name", name, tmsID, similarityThreshold)
}

func fuzzyMatchFullAddress(
	ctx context.Context,
	fullAddress string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "full_address", fullAddress, tmsID, similarityThreshold)
}

func fuzzyMatchStreetAddress(
	ctx context.Context,
	addressLine1 string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "address_line1", addressLine1, tmsID, similarityThreshold)
}

// Helper function to fuzzy match a location by a column name
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH tms_location_distances AS (
					SELECT *,
						%[1]s <-> @searchTerm AS trgm_dist,
						%[1]s ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM tms_locations
					WHERE tms_integration_id = @tmsID
					-- Exclude locations whose similarity to searchTerm is lower than threshold
					AND (%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%')
				)
				SELECT *
				FROM tms_location_distances
				ORDER BY
					is_match DESC,
					trgm_dist
				LIMIT 20;
			`, columnName)

			// NOTE: Scopes() and Raw/Exec() cannot be used to chain multiple fuzzy queries together
			// (like load advanced search) because they're executed immediately
			// but this usage is fine
			return tx.Raw(query, sql.Named("searchTerm", searchTerm), sql.Named("tmsID", tmsID))
		})
	}
}

// FieldMatch defines a column to search against and the term to search for
type FieldMatch struct {
	ColumnName string
	SearchTerm string
	Weight     float64 // Optional weight for calculating combined similarity score (defaults to 1.0)
}

// SearchLocationsByRadius searches for locations within a specified radius in miles
// from a given latitude and longitude using PostGIS see common/rds/tms_location/README.md for more details
func SearchLocationsByRadius(
	ctx context.Context,
	query SearchLocationsQuery,
	latitude float64,
	longitude float64,
) (res []models.LocationWithDistance, err error) {
	radiusInMeters := query.MileRadius * MileToMeterConversionFactor

	db := rds.WithContextReader(ctx).Preload("TMSCarrier")

	sqlQuery := `
		tms_integration_id = ? AND
		ST_DWithin(
			geography(point),
			geography(ST_SetSRID(ST_MakePoint(?, ?), 4326)),
			?
		)`

	// if strings.EqualFold(query.Type, "carrier") {
	// 	sqlQuery += "AND tms_carrier_id > 0"
	// } else {
	// 	log.Warn(ctx, "unexpected type in location search query", zap.String("type", query.Type))
	// }

	// First get all the base fields into a TMSLocation slice
	var locationsWithDistance []models.LocationWithDistance
	err = db.Model(&models.TMSLocation{}).Select(
		`*,
		ST_Distance(
			geography(point),
			geography(ST_SetSRID(ST_MakePoint(?, ?), 4326))
		) / ? as miles_distance`,
		longitude,
		latitude,
		MileToMeterConversionFactor).
		Where(
			sqlQuery,
			query.TMSID,
			longitude,
			latitude,
			radiusInMeters,
		).
		Order("miles_distance ASC").
		Scan(&locationsWithDistance).Error

	if err != nil {
		return nil, fmt.Errorf("error searching locations by radius: %w", err)
	}

	return locationsWithDistance, nil
}
