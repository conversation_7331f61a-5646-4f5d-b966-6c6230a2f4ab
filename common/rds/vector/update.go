package vector

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (r *Repository) AssociateWithEmail(vector *models.Vector, email *models.Email) error {
	return r.db.Model(vector).Association("Emails").Append(email)
}

func (r *Repository) AssociateWithLoadSuggestion(vector *models.Vector, newSug *models.SuggestedLoadChange) error {
	return r.db.Model(vector).Association("SuggestedLoadChanges").Append(newSug)
}

func (r *Repository) AssociateWithQuoteRequest(vector *models.Vector, newSug *models.QuoteRequest) error {
	return r.db.Model(vector).Association("QuoteRequests").Append(newSug)
}

// UpdateLoadBuildingAssociations updates vector embeddings with associations to load building suggestions
// and possible quote request suggestions.
// If the attachmentExternalID is provided, it will be used to find the attachment vector embedding.
// Otherwise, the emailID will be used to find the email vector embedding.
func (r *Repository) UpdateLoadBuildingAssociations(
	ctx context.Context,
	email models.Email,
	results []models.SuggestedLoadChange,
	i int) {

	if r != nil {
		if results[i].S3Attachment.ExternalID != "" {
			attachErr := r.updateAssociations(
				ctx,
				&email,
				results[i].S3Attachment.ExternalID,
				models.AttachmentContent,
				&results[i],
				nil,
			)
			if attachErr != nil {
				log.WarnNoSentry(ctx, "error updating vector embedding associations",
					zap.Error(attachErr),
					zap.Int("index", i),
					zap.String("attachmentID", results[i].S3Attachment.ExternalID))
			}
		} else if results[i].EmailID != 0 {
			attachErr := r.updateAssociations(
				ctx,
				&email,
				"",
				models.EmailBodyContent,
				&results[i],
				nil,
			)
			if attachErr != nil {
				log.WarnNoSentry(ctx, "error updating vector embedding associations",
					zap.Error(attachErr),
					zap.Int("index", i),
					zap.String("emailID", fmt.Sprintf("%d", results[i].EmailID)))
			}
		}
	}
}

// UpdateQuoteRequestAssociations updates vector embeddings with associations to quote requests.
// If the attachmentExternalID is provided, it will be used to find the attachment vector embedding.
// Otherwise, the emailID will be used to find the email vector embedding.
func (r *Repository) UpdateQuoteRequestAssociations(
	ctx context.Context,
	email models.Email,
	dedupedResults []models.QuoteRequest,
	i int) {

	if r != nil {
		if dedupedResults[i].Attachment.ExternalID != "" {
			attachErr := r.updateAssociations(
				ctx,
				&email,
				dedupedResults[i].Attachment.ExternalID,
				models.AttachmentContent,
				nil,
				&dedupedResults[i],
			)
			if attachErr != nil {
				log.WarnNoSentry(ctx, "error updating vector embedding associations",
					zap.Error(attachErr),
					zap.Int("index", i),
					zap.String("attachmentID", dedupedResults[i].Attachment.ExternalID))
			}
		} else if dedupedResults[i].EmailID != 0 {
			attachErr := r.updateAssociations(
				ctx,
				&email,
				"",
				models.EmailBodyContent,
				nil,
				&dedupedResults[i],
			)
			if attachErr != nil {
				log.WarnNoSentry(ctx, "error updating vector embedding associations",
					zap.Error(attachErr),
					zap.Int("index", i),
					zap.String("emailID", fmt.Sprintf("%d", dedupedResults[i].EmailID)))
			}

		}

	}
}

func (r *Repository) updateAssociations(
	ctx context.Context,
	email *models.Email,
	attachmentExternalID string,
	contentType models.ContentType,
	suggestion *models.SuggestedLoadChange,
	quoteRequest *models.QuoteRequest,
) error {

	var vector models.Vector

	query := r.db.Where("email_id = ? AND service_id = ? AND content_type = ?", email.ID, email.ServiceID, contentType)

	if attachmentExternalID != "" && contentType == models.AttachmentContent {
		query = query.Where("attachment_external_id = ?", attachmentExternalID)
	}

	result := query.First(&vector)

	if result.Error != nil {
		return fmt.Errorf("failed to find vector embedding: %w", result.Error)
	}

	if suggestion != nil {
		if err := r.db.Model(&vector).Association("SuggestedLoadChanges").Append(suggestion); err != nil {
			return fmt.Errorf("failed to associate vector with suggestion: %w", err)
		}

		// If there's a quote request suggestion, associate it as well
		if suggestion.QuoteRequestSuggestion != nil {
			if err := r.db.Model(&vector).
				Association("QuoteRequests").
				Append(suggestion.QuoteRequestSuggestion); err != nil {

				return fmt.Errorf("failed to associate vector with quote request: %w", err)
			}
		}
	}

	if quoteRequest != nil {
		if err := r.db.Model(&vector).Association("QuoteRequests").Append(quoteRequest); err != nil {
			return fmt.Errorf("failed to associate vector with quote request: %w", err)
		}
	}

	log.Debug(ctx, "Updated embedding associations",
		zap.Uint("emailID", email.ID),
		zap.String("attachmentExternalID", attachmentExternalID),
	)

	return nil
}
