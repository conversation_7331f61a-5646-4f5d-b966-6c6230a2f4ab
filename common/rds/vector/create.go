package vector

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Repository provides methods for interacting with the vectors table
type Repository struct {
	db *gorm.DB
}

// New creates a new vector repository
func New(db *gorm.DB) *Repository {
	return &Repository{db: db}
}

// StoreEmailEmbedding stores a vector embedding for an email body
func (r *Repository) StoreEmailEmbedding(
	ctx context.Context,
	email *models.Email,
	embedding models.VectorEmbedding,
) error {
	vector := models.Vector{
		EmailID:     email.ID,
		ServiceID:   email.ServiceID,
		UserID:      email.UserID,
		ContentType: models.EmailBodyContent,
		Embedding:   embedding,
		Emails:      []models.Email{*email},
	}

	err := r.db.Create(&vector).Error
	if err != nil {
		return fmt.Errorf("failed to store email embedding: %w", err)
	}

	log.Debug(ctx, "Stored email embedding",
		zap.Uint("emailID", email.ID),
		zap.Uint("vectorID", vector.ID),
	)

	return nil
}

// StoreAttachmentEmbedding stores a vector embedding for an attachment
func (r *Repository) StoreAttachmentEmbedding(
	ctx context.Context,
	email *models.Email,
	attachmentExternalID string,
	embedding models.VectorEmbedding,
) error {
	vector := models.Vector{
		EmailID:              email.ID,
		ServiceID:            email.ServiceID,
		UserID:               email.UserID,
		AttachmentExternalID: attachmentExternalID,
		ContentType:          models.AttachmentContent,
		Embedding:            embedding,
		Emails:               []models.Email{*email},
	}

	err := r.db.Create(&vector).Error
	if err != nil {
		return fmt.Errorf("failed to store attachment embedding: %w", err)
	}

	log.Debug(ctx, "Stored attachment embedding",
		zap.Uint("emailID", email.ID),
		zap.Uint("vectorID", vector.ID),
	)

	return nil
}
