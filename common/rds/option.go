package rds

import "gorm.io/gorm"

type Options struct {
	AppEnv               string
	CloudRegion          string
	DBHost               string
	DBHostReader         string
	DBName               string
	DBUser               string
	DBPassword           string
	GormConfig           *gorm.Config
	IsOnPrem             bool
	MigrationOrder       []any
	UseAWSSecretsManager bool
	UseAzureKeyVault     bool
	SSLMode              string
}

type Option func(*Options)

// WithAppEnv sets the application environment.
func WithAppEnv(env string) Option {
	return func(o *Options) {
		o.AppEnv = env

		// Update the default GORM config based on the new app environment
		o.GormConfig = defaultGormConfig(o.AppEnv)

		if o.AppEnv == "staging" || o.AppEnv == "prod" {
			o.SSLMode = "require"
		}
	}
}

// WithAWSSecretsManager sets whether we should read secrets from AWS Secrets Manager.
func WithAWSSecretsManager(useAWSSecretsManager bool) Option {
	return func(o *Options) {
		o.UseAWSSecretsManager = useAWSSecretsManager
	}
}

// WithAzureKeyVault sets whether we should read secrets from Azure Key Vault.
func WithAzureKeyVault(useAzureKeyVault bool) Option {
	return func(o *Options) {
		o.UseAzureKeyVault = useAzureKeyVault
	}
}

// WithCloudRegion sets the cloud provider's region for the organization.
func WithCloudRegion(region string) Option {
	return func(o *Options) {
		o.CloudRegion = region
	}
}

// WithDBCredentials sets the database user and password.
func WithDBCredentials(user, password string) Option {
	return func(o *Options) {
		o.DBUser = user
		o.DBPassword = password
	}
}

// WithDBHost sets the database host.
func WithDBHost(dbHost string) Option {
	return func(o *Options) {
		o.DBHost = dbHost
	}
}

// WithDBHost sets the database host for the read replica.
func WithDBHostReader(dbHostReader string) Option {
	return func(o *Options) {
		o.DBHostReader = dbHostReader
	}
}

// WithDBName sets the database name.
func WithDBName(dbName string) Option {
	return func(o *Options) {
		o.DBName = dbName
	}
}

// WithGormConfig sets the GORM configuration.
func WithGormConfig(cfg *gorm.Config) Option {
	return func(o *Options) {
		o.GormConfig = cfg
	}
}

// WithMigrationOrder sets the migration order of the models.
func WithMigrationOrder(order []any) Option {
	return func(opts *Options) {
		opts.MigrationOrder = order
	}
}

// WithOnPrem sets whether the customer hosts the environment on-premises.
func WithOnPrem(isOnPrem bool) Option {
	return func(o *Options) {
		o.IsOnPrem = isOnPrem
	}
}

// WithSSLMode sets the SSL mode for the database connection.
func WithSSLMode(sslMode string) Option {
	return func(o *Options) {
		o.SSLMode = sslMode
	}
}
