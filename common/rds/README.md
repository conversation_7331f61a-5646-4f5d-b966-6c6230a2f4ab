# RDS (Relational Database Service)

RDS is a package that provides a simple interface for interacting with a relational database. It is designed to be used in conjunction with the `gorm` ORM library, which provides a convenient way to interact with the database.

# Setup

1. Create a `drumkit_dev` DB in Postgres

```sql
$ ~ psql
postgres=# CREATE DATABASE drumkit_dev;
postgres=# \c drumkit_dev
```

2. Running AutoMigrate when starting the server (either through Docker or individual servers) should automatically create the necessary tables, indexes, and extensions.

## Read Replica

### Consistency Model

- Aurora read replicas are **eventually consistent**, not immediately synchronous
- There is typically a small lag (milliseconds) between primary and replica
- Consistency is not guaranteed across replicas immediately after writes

### Usage Guidelines

#### ✅ Recommended Use Cases

- General querying where immediate consistency is not required
- Non-critical reads (analytics, search, background jobs)
- Improving read scalability under load

#### 🚫 Avoid Using For

- Critical reads immediately after writes
- Session-specific or transactional data (e.g. `rds.WithContext(ctx).Transaction()`, _not_ `rds.WithContextReader(ctx).Transaction()`)
- Operations requiring immediate consistency

## Extensions

- We use the `pg_tgrm` PostgreSQL extension in order to support fuzzy matching used in some queries.
- We use the `btree_gist` PostgreSQL extension in order to support GIN and GiST indexes, used to speed up text-based searches.
- We use the `postgis` PostgreSQL extension to support geospatial data types and functions, used for location-based queries. Unlike other extensions, this is not installed by default when you `brew install postgresql`; you must run a separate `brew install postgis` command. Additional resources:
- We use the `vector` extension to perform vector similarity search.

  - **Setup**:

    - If you installed PostgreSQL via [Postgres.app](https://postgresapp.com/downloads.html), then versions 15+ include the pgvector extension by default. Check if you already have it by running `SELECT * FROM pg_available_extensions WHERE name ilike '%vector%'`. If not, then download a newer version of Postgres.app (you can have multiple simultaneously).

      - To backup and restore your local DB from your old Postgres service to the new one, do the following:

      ```shell
      # Open the old version (e.g., PostgreSQL 14) in Postgres.app. Run a terminal and export your database:
      pg_dump -Fc -U your_user -d your_dbname > backup.dump # e.g. pg_dump -Fc -U postgres -d drumkit_dev > backup.dump

      # Open the new Postgres version (e.g., PostgreSQL 16).
      # Create a new database with the same name, then restore:
      pg_restore -U your_user -d your_dbname -Fc backup.dump # e.g. pg_restore -U postgres -d drumkit_dev -Fc backup.dump
      ```

    - Otherwise, install by following the instructions [here](https://github.com/pgvector/pgvector?tab=readme-ov-file#installation-notes---linux-and-mac). If you run into issues, be sure to check the [Installation Notes](https://github.com/pgvector/pgvector?tab=readme-ov-file#installation-notes---linux-and-mac) section or just do the above

## Fuzzy matching with PSQL's pg_trgm extension

PostgreSQL's pg_trgm module provides functions and operators for determining the similarity of text based on trigram matching.

Though more readable, we should always avoid using the `SIMILARITY()` function in queries and instead use the `%` operator, since the latter is able to internally make use of the GIN indexes created to speed up text-based searches.

## How Similarity Is Calculated

The similarity between two strings is computed by:

- Counting the trigrams they have in common
- Dividing by the total number of unique trigrams across both strings
- The result is a number between 0 (completely different) and 1 (identical)

For example, comparing "hello" and "hella" would yield a relatively high similarity because they share many trigrams (" h", " he", "hel", "ell").

## The Similarity Threshold Parameter

The pg_trgm.similarity_threshold parameter determines how similar strings need to be for the % operator to consider them a match. This is a critical setting that affects query behavior:

- Higher values (closer to 1.0) require strings to be very similar
- Lower values (closer to 0.0) allow more fuzzy matching
- Typical values range from 0.1 to 0.6 depending on your needs

## Why Set the Threshold Per Transaction?

The similarity threshold must be set within the same transaction as your search queries for several reasons:

- Isolation: Prevents other sessions from affecting your similarity matching behavior
- Flexibility: Allows different parts of your application to use different thresholds without conflict

## Examples

### Basic Usage with `%`

```sql
SELECT * FROM example WHERE search % columnValue
```

### Sorting Matches

When returning the results of a search, it's important to order them by relevance as that provides a much better user experience.

For that purpose, we can use the `<->` operator to calculate the numerical distance between matches, as well as the `ILIKE` clause to prioritize exact case-insensitive matches. This is implemented through the usage of a Common Table Expression (CTE) to calculate said values, and then later use them to filter the results.

```sql
WITH example_distances AS (
    SELECT *,
      arg1 <-> arg2 AS trgm_dist,
      arg1 ILIKE arg2 AS is_match
    FROM example_table
)
SELECT *
FROM example_distances
ORDER BY
  is_match DESC,
  trgm_dist;
```

The example above guarantees that we first show exact case-insensitive matches, and then the rest of results sorted by the ascending distance (lower distances mean more similarity).

For more details, please refer to
https://github.com/drumkitai/drumkit/pull/796 and https://github.com/drumkitai/drumkit/pull/1091
