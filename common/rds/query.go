package rds

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/util/otel"
)

// appEnv specifies the application environment (e.g., "dev", "staging", "prod").
// It controls whether WithContextReader logs a warning when falling back to the main database connection.
// Defaults to "dev", where fallback warnings are suppressed.
var appEnv = "dev"

// WithContext returns a GORM DB instance for the main connection with tracing.
func WithContext(ctx context.Context) (res *gorm.DB) {
	var spanName string

	// Attach calling function to trace
	pc, _, _, ok := runtime.Caller(1)
	details := runtime.FuncForPC(pc)

	if ok && details != nil {
		longName := details.Name()
		lastSlashIndex := strings.LastIndex(longName, "/")

		if lastSlashIndex != -1 {
			spanName = longName[lastSlashIndex+1:]
		} else {
			spanName = longName
		}
	}

	ctx, metaSpan := otel.StartSpan(ctx, spanName, nil)
	defer func() {
		// Record errors (but not gorm.ErrRecordNotFound warnings)
		if res != nil && res.Error != nil && !errors.Is(res.Error, gorm.ErrRecordNotFound) {
			metaSpan.End(res.Error)
		} else {
			metaSpan.End(nil)
		}
	}()

	if db == nil {
		log.Error(ctx, "main database connection is not initialized")
		return nil
	}

	return db.WithContext(ctx)
}

// WithContextReader returns a GORM DB instance for the reader connection with tracing,
// falling back to the main connection if the reader is not initialized.
func WithContextReader(ctx context.Context) (res *gorm.DB) {
	var spanName string

	// Attach calling function to trace
	pc, _, _, ok := runtime.Caller(1)
	details := runtime.FuncForPC(pc)

	if ok && details != nil {
		longName := details.Name()
		lastSlashIndex := strings.LastIndex(longName, "/")

		if lastSlashIndex != -1 {
			spanName = longName[lastSlashIndex+1:]
		} else {
			spanName = longName
		}
	}

	ctx, metaSpan := otel.StartSpan(ctx, spanName, nil)
	defer func() {
		// Record errors (but not gorm.ErrRecordNotFound warnings)
		if res != nil && res.Error != nil && !errors.Is(res.Error, gorm.ErrRecordNotFound) {
			metaSpan.End(res.Error)
		} else {
			metaSpan.End(nil)
		}
	}()

	if dbReader != nil {
		return dbReader.WithContext(ctx)
	}

	if appEnv == "prod" {
		log.Warn(ctx, "reader connection not initialized, falling back to main connection")
	}

	if db == nil {
		log.Error(ctx, "main database connection is not initialized")
		return nil
	}

	return db.WithContext(ctx)
}

// GetSimilarityThreshold retrieves the current pg_trgm similarity threshold value from the database.
// The similarity threshold (0 to 1) determines how closely strings must match for the pg_trgm %
// operator to consider them similar, with higher values requiring closer matches.
//
// The function handles the case where the parameter hasn't been initialized, returning a specific
// error containing "parameter not initialized" that callers can check for. It executes within
// the provided transaction and respects context for logging and otel tracing.
//
// It returns the current threshold value if successful, or an error if the parameter isn't
// initialized or if database operations fail.
func GetSimilarityThreshold(ctx context.Context, tx *gorm.DB) (float64, error) {
	var currentThreshold float64

	err := tx.WithContext(ctx).Raw("SHOW pg_trgm.similarity_threshold").Scan(&currentThreshold).Error
	if err != nil {
		if strings.Contains(strings.ToLower(err.Error()), "unrecognized configuration parameter") {
			log.Infof(ctx, "pg_trgm.similarity_threshold parameter not initialized")
			return 0, fmt.Errorf("similarity threshold parameter not initialized: %w", err)
		}

		log.Infof(ctx, "Error getting current similarity threshold: %v", err)
		return 0, fmt.Errorf("failed to get current similarity threshold: %w", err)
	}

	log.Infof(ctx, "Current similarity threshold: %f", currentThreshold)
	return currentThreshold, nil
}

// SetSimilarityThreshold manages the pg_trgm similarity threshold setting.
// It first checks the current threshold and only performs an update if the desired value differs.
//
// The function automatically handles uninitialized parameters by setting the initial value
// without requiring separate initialization logic. It executes within a transaction to
// maintain consistency with related operations.
//
// Returns nil if successful or an error if initialization/update fails.
func SetSimilarityThreshold(ctx context.Context, tx *gorm.DB, newThreshold float64) (err error) {
	// NOTE: It's best to always reset the similarity thresold (even if it's already set to the desired value)
	// because if it isn't initialized yet, GetSimilarityThreshold will thrown an error and Postgres will
	// abort the transaction.
	log.Debugf(ctx, "Setting similarity threshold parameter with value %f", newThreshold)

	err = tx.WithContext(ctx).Exec(
		fmt.Sprintf(`SET pg_trgm.similarity_threshold = %f`, newThreshold),
	).Error
	if err != nil {
		log.Debugf(ctx, "Error setting similarity threshold: %v", err)
		return fmt.Errorf("failed to set similarity threshold: %w", err)
	}

	log.Debugf(ctx, "Successfully set similarity threshold to %f", newThreshold)

	return nil
}

// Generic CRUD
type GenericGetQuery struct {
	TMSID        uint `json:"tmsID"`
	Limit        int  `json:"limit"`
	ForceRefresh bool `json:"forceRefresh"`
	// AWS API Gateway times out after 30 seconds, so we need to support async requests.
	// This parameter is currently NOT used by the FE; primarily for devs to onboard large list of objects,
	// like Mcleod's 200k locations which take ~60 seconds to load.
	// QN: Or should we load async in FE? Hmmmmmm
	Async bool `json:"async"`
	// ChangeHostName: optional parameter to change the host name for TMS api call
	// e.g. to run getLocations() for Turvo pointing to app.turvo.com/api instead of publicapi.turvo.com/api
	ChangeHostName bool `json:"changeHostName"`
}

type GenericSearchQuery struct {
	TMSID  uint   `json:"tmsID"`
	Search string `json:"search"`
}

func GetByID(ctx context.Context, id uint, out any) error {
	if out == nil {
		return errors.New("nil out struct")
	}

	return WithContextReader(ctx).Where("id = ?", id).First(out).Error
}

func GetByIDPreloadAssociations(ctx context.Context, id uint, out any) error {
	if out == nil {
		return errors.New("nil out struct")
	}

	return WithContextReader(ctx).Preload(clause.Associations).Where("id = ?", id).First(out).Error
}

func Update(ctx context.Context, updatedModel any) error {
	return WithContext(ctx).Model(updatedModel).Updates(updatedModel).Error
}
