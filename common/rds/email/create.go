package email

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Create(ctx context.Context, email *models.OnPremEmail) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "external_id"}},
			UpdateAll: true}).
		Create(&email).Error
}

// Update emails and with associated load ID. See create_test for detailed behavior.
func UpsertEmailAndLoads(ctx context.Context, email *models.Email) error {
	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return tx.WithContext(ctx).
			// Optimization: We use `UpsertLoad` logic because it contains warehouse-matching logic.
			// Don't re-upsert loads, just update the email and its load associations
			// IMPORTANT NOTE: Omit("Loads") does not create the association at all, Omit("Loads.*")
			// creates the association but does not update the Load record again.
			Omit("Loads.*").
			Omit("Services.*").
			Omit("Integrations.*").
			Clauses(
				clause.Locking{
					Strength: "UPDATE",
					Options:  "NOWAIT",
				},
				clause.Returning{},
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "external_id"}},
					UpdateAll: true}).
			Create(&email).Error
	})
}
