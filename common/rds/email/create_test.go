package email

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Sub-tests must run sequentially
func TestLiveUpsertEmailAndLoads(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping service TestLiveUpsertEmailAndLoads: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()
	rds.MustOpenTestDB(ctx, "drumkit_test_db")

	rds.ClearTestDB(ctx, t)
	t.Cleanup(func() { rds.ClearTestDB(ctx, t) })

	service := models.Service{Name: "Test Service"}
	err := rds.CreateService(ctx, &service)
	require.NoError(t, err)

	email := models.Email{
		ExternalID: "email1",
		ServiceID:  service.ID,
	}
	loads := []models.Load{
		{
			FreightTrackingID: "load1",
			ServiceID:         service.ID,
		},
		{
			FreightTrackingID: "load2",
			ServiceID:         service.ID,
			IsPlaceholder:     true,
		},
	}
	email.Loads = loads

	var emailID, load1ID, load2ID uint

	t.Run("Email + 2 loads", func(t *testing.T) {
		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)

		emailID = email.ID
		load1ID = loads[0].ID
		load2ID = loads[1].ID

		assert.NotEmpty(t, emailID)
		assert.NotEmpty(t, load1ID)
		assert.NotEmpty(t, load2ID)
	})

	// Tests a bug we had where email upsert with FullSaveAssociationsMode: true upserts
	// loads based on conflict with ID, not FreightTrackingID x ServiceID
	t.Run("Update load without gorm.Model", func(t *testing.T) {
		email.Loads[0].Model = gorm.Model{}
		email.Loads[0].Customer.AddressLine1 = "99 Newbury Street"

		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		assert.Equal(t, emailID, email.ID)
		assert.Equal(t, load1ID, loads[0].ID)

		assert.Equal(t, "99 Newbury Street", loads[0].Customer.AddressLine1)
	})

	t.Run("Update existing email and upsert loads", func(t *testing.T) {
		email.Body = "new body"
		email.Loads[0].Customer.Name = "load1Customer"
		email.Loads[1].Customer.Name = "load2Customer"
		email.Loads[1].IsPlaceholder = false
		// New load
		email.Loads = append(email.Loads, models.Load{FreightTrackingID: "load3", ServiceID: service.ID})

		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		assert.Equal(t, emailID, email.ID)
		assert.Equal(t, load1ID, loads[0].ID)
		assert.Equal(t, load2ID, loads[1].ID)

		assert.Equal(t, "new body", email.Body)
		assert.Equal(t, "load1Customer", loads[0].Customer.Name)
		assert.Equal(t, "load2Customer", loads[1].Customer.Name)
		assert.False(t, loads[1].IsPlaceholder)

		assert.NotEmpty(t, email.Loads[2].ID)

	})

	t.Run("Add new load to existing email", func(t *testing.T) {
		// Check that upsert preserves other existing 3 loads even if we upsert with only 1 in the slice
		email.Loads = []models.Load{{FreightTrackingID: "load4", ServiceID: service.ID}}

		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)

		// Upserting without the other associated loads doesn't automatically add them...
		assert.Equal(t, emailID, email.ID)
		assert.Len(t, email.Loads, 1)

		// ...but the new load is indeed added to the existing list of associations
		getEmail, err := GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		assert.Len(t, getEmail.Loads, 4)
	})

	t.Run("Email, no loads", func(t *testing.T) {
		email := models.Email{
			ExternalID: "email0",
			ServiceID:  service.ID,
		}
		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)

		assert.NotEmpty(t, email.ID)
		assert.Empty(t, email.Loads)
	})

	t.Run("Duplicate loads", func(t *testing.T) {
		dupeLoad := models.Load{ServiceID: service.ID, FreightTrackingID: "dup"}
		newEmail := models.Email{
			ExternalID: "email0",
			ServiceID:  service.ID,
			Loads:      []models.Load{dupeLoad, dupeLoad},
		}

		err := UpsertEmailAndLoads(ctx, &newEmail)
		require.NoError(t, err)

		assert.NotEmpty(t, newEmail.ID)
		assert.Len(t, email.Loads, 2)
		assert.Equal(t, email.Loads[0].ID, email.Loads[0].ID)
	})
}
