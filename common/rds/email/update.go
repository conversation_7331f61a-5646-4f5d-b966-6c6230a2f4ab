package email

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// NOTE: Updates all email fields using Select("*"), but not associations
func Update(ctx context.Context, email *models.Email) error {
	return rds.WithContext(ctx).
		Clauses(
			clause.Locking{
				Strength: "UPDATE",
				Options:  "NOWAIT",
			},
		).
		Select("*").
		Updates(email).Error
}

// UpdateProcessingMetadata updates the processing duration (in seconds) and lambda request ID for an email
func UpdateProcessingMetadata(
	ctx context.Context,
	externalID string,
	processingStartTime time.Time,
	lambdaRequestID string,
) error {
	processingDuration := models.ProcessingDuration(time.Since(processingStartTime))

	return rds.WithContext(ctx).Model(&models.Email{}).
		Where("external_id = ?", externalID).
		Updates(map[string]any{
			"processing_start_time":       processingStartTime,
			"processing_duration_seconds": processingDuration,
			"lambda_request_id":           lambdaRequestID,
		}).Error
}

// AssociateLoadWithEmail associates a load with an email.
func AssociateLoadWithEmail(ctx context.Context, emailID uint, loadID uint) error {
	if emailID == 0 || loadID == 0 {
		return fmt.Errorf("email ID and load ID must be non-zero")
	}

	email := models.Email{}
	email.ID = emailID

	load := models.Load{}
	load.ID = loadID

	return rds.WithContext(ctx).Debug().Model(&email).Association("Loads").Append(&load)
}
