package quoterequest

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateQuoteRequestStatus updates the status of a quote request.
// If the new status is inFlight, we save the applied fields.
// Passing nil for appliedFields will not overwrite the existing applied fields.
func UpdateQuoteRequestStatus(
	ctx context.Context,
	quoteRequestID uint,
	appliedFields *models.QuoteLoadInfo,
	user *models.User,
	status models.SuggestionStatus,
	finalQuoteData *models.FinalQuoteData,
	selectedQuickQuoteID *uint,
) error {

	if quoteRequestID == 0 {
		return fmt.Errorf("invalid quote request ID")
	}

	var existingRequest models.QuoteRequest
	if err := rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", quoteRequestID).
		First(&existingRequest).Error; err != nil {

		return fmt.Errorf("quote request not found: %w", err)
	}

	existingRequest.Status = status
	existingRequest.QuotedByUserID = &user.ID

	if status == models.InFlight && appliedFields != nil {
		existingRequest.AppliedRequest = *appliedFields
	}

	if status == models.Accepted && finalQuoteData != nil {
		existingRequest.FinalQuotePrice = finalQuoteData.FinalQuotePrice
		existingRequest.FinalMargin = finalQuoteData.FinalMargin
		existingRequest.MarginType = finalQuoteData.MarginType
		existingRequest.CarrierCostType = finalQuoteData.CarrierCostType
		existingRequest.FinalCarrierCost = finalQuoteData.FinalCarrierCost
		if finalQuoteData.CustomerID != 0 {
			existingRequest.AppliedRequest.CustomerID = finalQuoteData.CustomerID
		}

		existingRequest.FinalQuoteHistory = append(existingRequest.FinalQuoteHistory, models.QuoteHistoryLog{
			UserID:    user.ID,
			UserEmail: user.EmailAddress,
			Timestamp: time.Now(),
			Quote:     *finalQuoteData,
		})
	}

	existingRequest.SelectedQuickQuoteID = selectedQuickQuoteID

	// Single database update for all cases
	return rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", existingRequest.ID).
		Updates(&existingRequest).Error
}

// AssociateLoadWithQuoteRequest associates a load with a quote request.
// We only associate the load with accepted quote requests.
// If the suggestion is not nil, we look for a quote request with the same load suggestion ID.
// If the suggestion is nil, we look for a quote request with the same pickup/dropoff/date/transport type/customer.
//
// TODO: Quoting portals often include the customer ref #, so we should use that if available.
func AssociateLoadWithQuoteRequest(
	ctx context.Context,
	suggestion *models.SuggestedLoadChange,
	newLoad models.Load,
) error {

	if suggestion != nil && suggestion.ID != 0 {
		quoteRequest, err := GetQuoteRequestByLoadSuggestionID(ctx, suggestion.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error associating load with quote request by load suggestion ID, trying to match by field similarity",
				zap.Error(err),
			)
		} else {
			quoteRequest.CreatedLoadID = &newLoad.ID
			quoteRequest.MatchedToLoadBy = models.MatchedBySuggestion

			return rds.WithContext(ctx).Save(&quoteRequest).Error
		}
	}

	quoteRequest, err := GetQuoteRequestByFieldSimilarity(ctx, newLoad)
	if err != nil {
		log.WarnNoSentry(ctx, "error associating load with quote request by field similarity", zap.Error(err))
	} else {
		quoteRequest.CreatedLoadID = &newLoad.ID
		quoteRequest.MatchedToLoadBy = models.MatchedByFieldSimilarity

		return rds.WithContext(ctx).Save(&quoteRequest).Error
	}

	return fmt.Errorf("unable to associate load with quote request")
}

// UpdateQuoteRequestBraintrustLogID updates the Braintrust log ID for a quote request.
func UpdateQuoteRequestBraintrustLogID(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", quoteRequest.ID).
		Update("braintrust_log_ids", quoteRequest.BraintrustLogIDs).Error
}

func UpdateSuggestedRequest(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", quoteRequest.ID).
		Clauses(clause.Returning{}).
		Update("suggested_request", quoteRequest.SuggestedRequest).Error
}
