package quoterequest

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmscustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/util"
)

func GetRequestByID(ctx context.Context, id uint) (res models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("id = ?", id).
		First(&res).Error
}

func GetRequestByEmailID(ctx context.Context, emailID uint) (res []models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("email_id = ?", emailID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Vector associations are not preloaded by default clause.Associations as they are a many2many association.
func GetRequestByEmailIDWithVectors(ctx context.Context, emailID uint) (res []models.QuoteRequest, err error) {
	return res, rds.WithContext(ctx).
		Preload(clause.Associations).
		Preload("Vectors").
		Where("email_id = ?", emailID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Gets quote request by threadID and preloads associations
func GetRequestByThreadID(ctx context.Context, threadID string) (res []models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("thread_id = ? AND (status = 'pending' OR status = 'inFlight')", threadID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Gets quote request and preloads carrier quoting associations (emails + quotes).
// Carrier Quoting supports shared/delegated inboxes, so this function maps the user's thread ID to the shared inbox's
// thread ID so that User B can review a CQ initiated by User A. For details, see
// https://linear.app/drumkit/issue/ENG-3280/allow-other-users-to-view-cq-initiated-by-other-user
//
// Note that for the CarrierEmails associations, this function returns
// only the first message of each thread that Drumkit sent to the carriers on behalf of the user.
func GetRequestByThreadIDPreloadAssociation(
	ctx context.Context,
	userID uint,
	threadID string,
	service models.Service,
) (res models.QuoteRequest, err error) {

	if !service.IsDelegatedInboxEnabled {
		log.Info(ctx, "delegated inbox disabled, fetching quote request only by user")

		err = rds.WithContextReader(ctx).
			Preload(clause.Associations).
			// clause.Associations doesn't preload nested associations, so we need to do it manually
			Preload("CarrierQuotes.CarrierLocation").
			Preload("CarrierQuotes.CarrierLocation.TMSCarrier").
			First(&res, "thread_id = ?", threadID).Error

		return res, err
	}

	log.Info(ctx, "delegated inbox enabled, fetching quote request by user/service")
	// If service has delegated/shared inboxes, then User B should be able to see in-progress CQ initiated by User A.
	// https://linear.app/drumkit/issue/ENG-3280/allow-other-users-to-view-cq-initiated-by-other-user#comment-78da709e
	err = rds.WithContextReader(ctx).
		Raw(`
		-- Even though subqueries fetch pool of RFC IDs/threads in order to map User B's thread to User A's version,
		-- QR is associated with only 1 of those thread IDs.
		SELECT * FROM quote_requests qr
		WHERE qr.thread_id IN (
			-- Returns 1+ thread IDs from the user's service
			SELECT thread_id FROM emails
			WHERE service_id = ? AND rfc_message_id IN (
				-- A thread contains 1+ emails and thus 1+ RFC message IDs for the thread
				SELECT rfc_message_id FROM emails
				WHERE user_id = ? AND thread_id = ?
			)
		)
		-- But this filters to QRs that
		-- 1) has carrier emails associated with it and
		-- 2) QR is associated with thread belonging to user or the service
		AND EXISTS (
			SELECT 1 FROM quote_request_carrier_emails_1tomany ce WHERE ce.quote_request_id = qr.id
		)
		LIMIT 1
	`, service.ID, userID, threadID).
		Scan(&res).Error

	if err != nil {
		return res, err
	}

	if res.ID == 0 {
		log.Info(ctx, "quote request ID is 0, returning gorm.ErrRecordNotFound")
		return res, gorm.ErrRecordNotFound
	}

	// Re-fetch with preloaded associations
	err = rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Preload("CarrierQuotes.CarrierLocation").
		Preload("CarrierQuotes.CarrierLocation.TMSCarrier").
		First(&res, "id = ?", res.ID).Error

	return res, err
}

func GetQuoteRequestBySourceExternalID(
	ctx context.Context,
	serviceID uint,
	sourceExternalID string,
) (res models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Where("service_id = ? AND source_external_id = ?", serviceID, sourceExternalID).
		First(&res).Error
}

func GetThirdPartyURLsByEmailID(ctx context.Context, emailID uint) (res models.ThirdPartyQuoteURLs, err error) {
	return res, rds.WithContextReader(ctx).
		Model(&models.QuoteRequest{}).
		Where("email_id = ?", emailID).
		Select("third_party_quote_urls").
		First(&res).Error
}

func GetConfigByServiceID(ctx context.Context, serviceID uint) (res models.QuickQuoteConfig, err error) {
	return res, rds.WithContextReader(ctx).
		Model(&models.QuickQuoteConfig{}).
		Where("service_id = ?", serviceID).
		First(&res).Error
}

//
// Metric queries
//

func GetQuoteRequests(ctx context.Context, serviceID uint, opts ...Option) (res []models.QuoteRequest, err error) {
	options := &queryOptions{
		dateRange: DateRangeOption{
			StartDate: nil,
			EndDate:   nil,
			NoLimit:   false,
		},
		userID:     nil,
		customerID: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	query := rds.WithContextReader(ctx).
		Model(&models.QuoteRequest{}).
		Where("service_id = ?", serviceID)

	if options.userID != nil {
		query = query.Where("user_id = ?", options.userID)
	}

	if options.customerID != nil {
		query = query.Where(
			"applied_customer_id = ? OR suggested_customer_id = ?",
			options.customerID,
			options.customerID,
		)
	}

	if !options.dateRange.NoLimit {
		if options.dateRange.StartDate != nil {
			query = query.Where("created_at >= ?", options.dateRange.StartDate)
		}
		if options.dateRange.EndDate != nil {
			query = query.Where("created_at <= ?", options.dateRange.EndDate)
		}
	}

	return res, query.Find(&res).Error
}

// GetQuoteRequestByLoadSuggestionID returns a quote request by load suggestion ID.
func GetQuoteRequestByLoadSuggestionID(
	ctx context.Context,
	loadSuggestionID uint,
) (res models.QuoteRequest, err error) {

	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("load_suggestion_id = ?", loadSuggestionID).
		First(&res).Error
}

// Score and prioritize matches
type scoredRequest struct {
	request models.QuoteRequest
	score   int
}

// GetQuoteRequestByFieldSimilarity returns a quote request that matches the load by field similarity.
// It prioritizes matches by customer ID, date proximity, and location.
// Future TODO: use the new stops model here instead of the legacy fields.
func GetQuoteRequestByFieldSimilarity(ctx context.Context, newLoad models.Load) (res *models.QuoteRequest, err error) {
	// First try to match using applied fields
	quoteRequests, err := findQuoteRequestsMatchingLoad(ctx, newLoad, true)
	if err != nil {
		return nil, err
	}

	if len(quoteRequests) > 0 {
		scoredRequests := matchAppliedFields(ctx, quoteRequests, newLoad)

		// If we have a good match with applied fields, return it
		if len(scoredRequests) > 0 && scoredRequests[0].score > 5 {
			match := scoredRequests[0].request
			log.Info(ctx,
				"found quote request match by applied field similarity",
				zap.Uint("quoteRequestId", match.ID),
				zap.Uint("loadId", newLoad.ID),
				zap.Int("score", scoredRequests[0].score),
			)
			return &match, nil
		}
	}

	// If we didn't find a good match with applied fields, try suggested fields
	suggestedQuoteRequests, err := findQuoteRequestsMatchingLoad(ctx, newLoad, false)
	if err != nil {
		return nil, err
	}

	if len(suggestedQuoteRequests) == 0 {
		return nil, errors.New("no quote requests match the load criteria")
	}

	suggestedScoredRequests := matchSuggestedFields(ctx, suggestedQuoteRequests, newLoad)

	// If we have a good match with suggested fields, return it
	if len(suggestedScoredRequests) > 0 && suggestedScoredRequests[0].score > 3 {
		match := suggestedScoredRequests[0].request
		log.Info(ctx,
			"found quote request match by suggested field similarity",
			zap.Uint("quoteRequestId", match.ID),
			zap.Uint("loadId", newLoad.ID),
			zap.Int("score", suggestedScoredRequests[0].score),
		)
		return &match, nil
	}

	return nil, errors.New("no quote requests with sufficient match confidence found")
}

// findQuoteRequestsMatchingLoad performs the base query to find quote requests that might match the load
func findQuoteRequestsMatchingLoad(
	ctx context.Context,
	newLoad models.Load,
	useAppliedFields bool,
) ([]models.QuoteRequest, error) {

	query := rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("service_id = ?", newLoad.ServiceID).
		Where("created_load_id IS NULL").
		Where("status = ?", models.Accepted)

	newLoadPickupStart := util.OrNullTime(newLoad.Pickup.ReadyTime, newLoad.Pickup.ApptStartTime)

	if useAppliedFields {
		if newLoadPickupStart.Valid {
			query = query.Where(
				"applied_pickup_date BETWEEN ? AND ?",
				newLoadPickupStart.Time.AddDate(0, 0, -7),
				newLoadPickupStart.Time.AddDate(0, 0, 7),
			)
		}
		query = query.Where("applied_pickup_state = ?", newLoad.Pickup.State)
		query = query.Where("applied_dropoff_state = ?", newLoad.Consignee.State)

		// Additional location filters - more specific but using OR to be less strict
		locationFilter := "((applied_pickup_city = ? OR applied_pickup_zip = ?) AND " +
			"(applied_dropoff_city = ? OR applied_dropoff_zip = ?))"
		query = query.Where(
			locationFilter,
			newLoad.Pickup.City,
			newLoad.Pickup.Zipcode,
			newLoad.Consignee.City,
			newLoad.Consignee.Zipcode,
		)

	} else {
		if newLoadPickupStart.Valid {
			query = query.Where(
				"suggested_pickup_date BETWEEN ? AND ?",
				newLoadPickupStart.Time.AddDate(0, 0, -7),
				newLoadPickupStart.Time.AddDate(0, 0, 7),
			)
		}
		query = query.Where("suggested_pickup_state = ?", newLoad.Pickup.State)
		query = query.Where("suggested_dropoff_state = ?", newLoad.Consignee.State)

		// Additional location filters for suggested fields
		locationFilter := "((suggested_pickup_city = ? OR suggested_pickup_zip = ?) AND " +
			"(suggested_dropoff_city = ? OR suggested_dropoff_zip = ?))"
		query = query.Where(
			locationFilter,
			newLoad.Pickup.City,
			newLoad.Pickup.Zipcode,
			newLoad.Consignee.City,
			newLoad.Consignee.Zipcode,
		)
	}

	var quoteRequests []models.QuoteRequest
	err := query.Find(&quoteRequests).Error
	if err != nil {
		return nil, fmt.Errorf("error querying for matching quote requests: %w", err)
	}

	return quoteRequests, nil
}

// matchAppliedFields scores quote requests based on applied field matching
func matchAppliedFields(
	ctx context.Context,
	quoteRequests []models.QuoteRequest,
	newLoad models.Load,
) []scoredRequest {

	var scoredRequests []scoredRequest
	newLoadPickupStart := util.OrNullTime(newLoad.Pickup.ReadyTime, newLoad.Pickup.ApptStartTime)

	for _, qr := range quoteRequests {
		score := 0

		// Customer match is most important - highest score
		if qr.AppliedRequest.CustomerID != 0 {
			customer, err := tmscustomerDB.GetByExternalTMSID(ctx, newLoad.TMSID, newLoad.Customer.ExternalTMSID)
			if err == nil && customer.ID == qr.AppliedRequest.CustomerID {
				score += 10
			}
		}

		// Transport type matching - using strings.Contains for partial matches
		// This handles cases where frontend has generalized values (e.g. "VAN")
		// and TMS has more specific values (e.g. "VAN (DAT)")
		if qr.AppliedRequest.TransportType != "" && newLoad.Specifications.TransportType != "" {
			loadTransportType := strings.ToLower(newLoad.Specifications.TransportType)
			quoteTransportType := strings.ToLower(string(qr.AppliedRequest.TransportType))

			if strings.Contains(loadTransportType, quoteTransportType) ||
				strings.Contains(quoteTransportType, loadTransportType) {
				score += 3 // Add a moderate score for transport type match
			}
		}

		// Date proximity is next most important
		if newLoadPickupStart.Valid && qr.AppliedRequest.PickupDate.Valid {
			pickupTime := newLoadPickupStart.Time
			quotePickupTime := qr.AppliedRequest.PickupDate.Time

			// Check if dates are the same (ignoring time)
			if pickupTime.Year() == quotePickupTime.Year() &&
				pickupTime.Month() == quotePickupTime.Month() &&
				pickupTime.Day() == quotePickupTime.Day() {
				score += 5
			} else {
				// Calculate days difference
				diff := math.Abs((pickupTime.Sub(quotePickupTime).Hours() / 24))
				// If within a week, still give some points, fewer as difference increases
				if diff < 7 {
					score += int(5 - math.Round(diff))
				}
			}
		}

		if (qr.AppliedRequest.PickupLocation.City == newLoad.Pickup.City ||
			qr.AppliedRequest.PickupLocation.Zip == newLoad.Pickup.Zipcode) &&
			(qr.AppliedRequest.DeliveryLocation.City == newLoad.Consignee.City ||
				qr.AppliedRequest.DeliveryLocation.Zip == newLoad.Consignee.Zipcode) {
			score += 4
		}

		scoredRequests = append(scoredRequests, scoredRequest{
			request: qr,
			score:   score,
		})
	}

	sort.Slice(scoredRequests, func(i, j int) bool {
		return scoredRequests[i].score > scoredRequests[j].score
	})

	return scoredRequests
}

// matchSuggestedFields scores quote requests based on suggested field matching
func matchSuggestedFields(
	ctx context.Context,
	quoteRequests []models.QuoteRequest,
	newLoad models.Load,
) []scoredRequest {

	var scoredRequests []scoredRequest
	newLoadPickupStart := util.OrNullTime(newLoad.Pickup.ReadyTime, newLoad.Pickup.ApptStartTime)

	for _, qr := range quoteRequests {
		score := 0

		if qr.SuggestedRequest.CustomerID != 0 {
			customer, err := tmscustomerDB.GetByExternalTMSID(ctx, newLoad.TMSID, newLoad.Customer.ExternalTMSID)
			if err == nil && customer.ID == qr.SuggestedRequest.CustomerID {
				score += 5 // Lower weight than applied customer match
			}
		}

		if qr.SuggestedRequest.TransportType != "" && newLoad.Specifications.TransportType != "" {
			loadTransportType := strings.ToLower(newLoad.Specifications.TransportType)
			quoteTransportType := strings.ToLower(string(qr.SuggestedRequest.TransportType))

			if strings.Contains(loadTransportType, quoteTransportType) ||
				strings.Contains(quoteTransportType, loadTransportType) {
				score += 2 // Lower weight than applied transport type match
			}
		}

		// Date proximity
		if newLoadPickupStart.Valid && qr.SuggestedRequest.PickupDate.Valid {
			pickupTime := newLoadPickupStart.Time
			quotePickupTime := qr.SuggestedRequest.PickupDate.Time

			// Check if dates are the same (ignoring time)
			if pickupTime.Year() == quotePickupTime.Year() &&
				pickupTime.Month() == quotePickupTime.Month() &&
				pickupTime.Day() == quotePickupTime.Day() {
				score += 3 // Lower weight than applied date match
			} else {
				// Calculate days difference
				diff := math.Abs((pickupTime.Sub(quotePickupTime).Hours() / 24))
				// If within a week, still give some points, fewer as difference increases
				if diff < 7 {
					score += int(3 - math.Round(diff*0.5)) // Lower weight than applied date match
				}
			}
		}

		if (qr.SuggestedRequest.PickupLocation.City == newLoad.Pickup.City ||
			qr.SuggestedRequest.PickupLocation.Zip == newLoad.Pickup.Zipcode) &&
			(qr.SuggestedRequest.DeliveryLocation.City == newLoad.Consignee.City ||
				qr.SuggestedRequest.DeliveryLocation.Zip == newLoad.Consignee.Zipcode) {
			score += 2 // Lower weight than applied location match
		}

		scoredRequests = append(scoredRequests, scoredRequest{
			request: qr,
			score:   score,
		})
	}

	// Sort by highest score
	sort.Slice(scoredRequests, func(i, j int) bool {
		return scoredRequests[i].score > scoredRequests[j].score
	})

	return scoredRequests
}

type DateRangeOption struct {
	StartDate *time.Time
	EndDate   *time.Time
	NoLimit   bool // if true, ignore dates completely
}

type DateCondition struct {
	Query  string
	Params []any
}

func getDateCondition(opt *DateRangeOption) DateCondition {
	// Default to 7 day filter
	if opt == nil {
		return DateCondition{
			Query:  "AND qr.created_at >= NOW() - INTERVAL '7 days'",
			Params: nil,
		}
	}

	// No date filter
	if opt.NoLimit {
		return DateCondition{
			Query:  "",
			Params: nil,
		}
	}

	if opt.StartDate != nil && opt.EndDate != nil {
		// Add one day to end date to include the full last day
		endDate := opt.EndDate.Add(24 * time.Hour)
		return DateCondition{
			Query: "AND qr.created_at >= ? AND qr.created_at < ?",
			Params: []any{
				opt.StartDate.Format(time.RFC3339),
				endDate.Format(time.RFC3339),
			},
		}
	}

	if opt.StartDate != nil {
		return DateCondition{
			Query:  "AND qr.created_at >= ?",
			Params: []any{opt.StartDate.Format(time.RFC3339)},
		}
	}

	if opt.EndDate != nil {
		// Add one day to include the full end date
		endDate := opt.EndDate.Add(24 * time.Hour)
		return DateCondition{
			Query:  "AND qr.created_at < ?",
			Params: []any{endDate.Format(time.RFC3339)},
		}
	}

	return DateCondition{
		Query:  "AND qr.created_at >= NOW() - INTERVAL '7 days'",
		Params: nil,
	}
}

// GetValidRequestsWithQuotes retrieves valid quote requests with their associated Greenscreens quotes.
// It filters out false positives and deduplicates across accounts using RFC message IDs.
//
// A quote request is considered valid when it contains:
//   - Complete pickup location (either city+state OR zip)
//   - Complete dropoff location (either city+state OR zip)
//   - Valid pickup date
//   - Optional: Valid delivery date
//
// Invalid requests are included if they:
//   - Have an accepted Greenscreens quote
//   - Match the quote's location and dates exactly
//   - Were created by the same user
//
// The function performs several operations:
//  1. Filters valid quote requests excluding test users aka Drumkit accounts associated with the service
//  2. Matches requests with accepted carrier quotes based on location and dates
//  3. Deduplicates results by RFC message ID (global identifier across accounts)
//  4. Joins with related tables to include email, user, and service information
//
// Location matching is done either by:
//   - Exact city/state match (case insensitive)
//   - Exact ZIP code match (when available)
//
// Date Filtering:
// By default, returns data from the last 7 days. This can be customized using options:
//   - WithStartDate: Set minimum creation date
//   - WithEndDate: Set maximum creation date
//   - WithDateRange: Set both start and end dates
//   - WithNoDateLimit: Retrieve all historical data
//
// The results are ordered by creation date and include:
//   - Quote request details
//   - Email metadata (RFC ID, subject, sender, etc.)
//   - User information
//   - Service details
//   - Associated quote information (pricing, margins, status)
//
// Note: This endpoint is used by Trident to estimate request volume. LLM-generated
// false positives are filtered out by requiring complete location and date information.
//
// Parameters:
//   - ctx: Context for the database operation
//   - serviceID: ID of the service to fetch quotes for (only Trident for now)
//   - opts: Optional date filtering parameters (variadic Option functions)
//
// Returns:
//   - []models.QuoteRequest: Slice of valid quote requests with their associated data
//   - error: Any error encountered during the operation
//
// Examples:
//
//	// Get last 7 days (default)
//	GetValidRequestsWithQuotes(ctx, serviceID)
//
//	// Get specific date range
//	GetValidRequestsWithQuotes(ctx, serviceID, WithDateRange(startTime, endTime))
//
//	// Get all historical data
//	GetValidRequestsWithQuotes(ctx, serviceID, WithNoDateLimit())
//
//	// Get from specific start date
//	GetValidRequestsWithQuotes(ctx, serviceID, WithStartDate(startTime))
func GetValidRequestsWithQuotes(
	ctx context.Context,
	serviceID uint,
	opts ...Option,
) ([]models.QuoteRequestWithDetails, error) {

	options := &queryOptions{}
	for _, opt := range opts {
		opt(options)
	}

	var quoteRequests []models.QuoteRequestWithDetails

	const (
		// ValidQuoteRequestsCTE filters quote requests with valid location data
		ValidQuoteRequestsCTE = "valid_quote_requests"
		// MatchedQuotesCTE joins quote requests with their matching carrier quotes
		MatchedQuotesCTE = "matched_quotes"
		// FinalResultsCTE deduplicates results by RFC message ID
		FinalResultsCTE = "final_results"
	)

	dateCondition := getDateCondition(&options.dateRange)

	query := fmt.Sprintf(`
    WITH %s AS MATERIALIZED (
        -- First CTE: Filter valid quote requests
        SELECT * FROM quote_requests qr
        --
        WHERE service_id = ?
        %s -- Dynamic date condition
        --
        -- For each quote request, check if there EXISTS a user that:
        --   1. Has an ID matching this quote request's user_id.
        --   2. Is marked as a test user
        -- The NOT EXISTS means "only keep this quote request if we can't find a matching test user"
        -- If the user is a test user -> EXISTS becomes true -> NOT EXISTS becomes false -> exclude row
        -- If the user is NOT a test user -> EXISTS becomes false -> NOT EXISTS becomes true -> keep row
        --
        AND NOT EXISTS (
            SELECT 1 FROM users u
            WHERE u.id = qr.user_id
            AND u.is_test_user = true
        )
        AND (
            -- Valid requests (complete location + dates)
            (
                -- Ensure pickup location is valid (either city+state OR zip)
                (
                    (qr.suggested_pickup_city IS NOT NULL
                     AND qr.suggested_pickup_city != ''
                     AND qr.suggested_pickup_state IS NOT NULL
                     AND qr.suggested_pickup_state != '')
                    OR
                    (qr.suggested_pickup_zip IS NOT NULL
                     AND qr.suggested_pickup_zip != '')
                )
                -- Ensure dropoff location is valid (either city+state OR zip)
                AND (
                    (qr.suggested_dropoff_city IS NOT NULL
                     AND qr.suggested_dropoff_city != ''
                     AND qr.suggested_dropoff_state IS NOT NULL
                     AND qr.suggested_dropoff_state != '')
                    OR
                    (qr.suggested_dropoff_zip IS NOT NULL
                     AND qr.suggested_dropoff_zip != '')
                )
                -- Ensure pickup date is valid (delivery date is optional)
                AND qr.suggested_pickup_date IS NOT NULL
            )
            OR
            -- Include invalid requests that were actually applied (have matching accepted quotes)
            EXISTS (
                -- "Is there at least one quote that matches these conditions?"
                SELECT 1
                FROM quick_quotes q
                JOIN quote_request_quick_quotes_1tomany cnq ON cnq.quote_id = q.id
                WHERE cnq.quote_request_id = qr.id
                AND q.user_id = qr.user_id
                AND qr.status = 'accepted'
                AND (
                    -- Match by city/state
                    (LOWER(q.pickup_city) = LOWER(qr.suggested_pickup_city)
                    AND LOWER(q.pickup_state) = LOWER(qr.suggested_pickup_state)
                    AND LOWER(q.dropoff_city) = LOWER(qr.suggested_dropoff_city)
                    AND LOWER(q.dropoff_state) = LOWER(qr.suggested_dropoff_state))
                    OR
                    -- Match by ZIP if available
                    (q.pickup_zip = qr.suggested_pickup_zip
                    AND qr.suggested_pickup_zip != ''
                    AND q.dropoff_zip = qr.suggested_dropoff_zip
                    AND qr.suggested_dropoff_zip != '')
                )
                AND DATE(q.pickup_date) = DATE(qr.suggested_pickup_date)
                AND DATE(q.delivery_date) = DATE(qr.suggested_delivery_date)
            )
        )
    ),
    email_subset AS MATERIALIZED (
        -- Optimization: Filter emails before joining
        SELECT * FROM emails
        WHERE id IN (SELECT DISTINCT(email_id) FROM %s)
    ),
    %s AS (
        -- Second CTE: Match with accepted quotes
        SELECT vr.*, q.id as matched_quote_id
        FROM %s vr
        LEFT JOIN quick_quotes q ON (
            q.user_id = vr.user_id
            AND (
                -- Match by city/state
                (q.pickup_city ILIKE vr.suggested_pickup_city
                AND q.pickup_state ILIKE vr.suggested_pickup_state
                AND q.dropoff_city ILIKE vr.suggested_dropoff_city
                AND q.dropoff_state ILIKE vr.suggested_dropoff_state)
                OR
                -- Match by ZIP if available
                (q.pickup_zip = vr.suggested_pickup_zip
                AND vr.suggested_pickup_zip != ''
                AND q.dropoff_zip = vr.suggested_dropoff_zip
                AND vr.suggested_dropoff_zip != '')
            )
            -- Match dates exactly
            AND DATE(q.pickup_date) = DATE(vr.suggested_pickup_date)
            AND DATE(q.delivery_date) = DATE(vr.suggested_delivery_date)
        )
    ),
    %s AS (
        -- Third CTE: Deduplicate by RFC message ID
        -- When multiple requests exist for the same email (RFC ID):
        --   1. Keep the most recently created request
        --   2. If multiple quotes exist, keep the one with highest price
        SELECT DISTINCT ON (e.rfc_message_id)
            mq.*,
            e.rfc_message_id,
            e.subject,
            e.sent_at,
            e.sender,
            e.recipients,
            e.classification_method,
            e.thread_id,
            u.email_address as user_email,
            s.name as service_name,
            q.distance,
            q.currency,
            q.total_cost,
            q.final_quote_price,
            q.final_margin,
            q.final_carrier_cost,
            q.target_buy_rate,
            q.low_buy_rate,
            q.high_buy_rate,
            q.start_buy_rate,
            q.fuel_rate,
            q.confidence_level,
            q.min_markup,
            q.max_markup,
            q.total_cost
        FROM %s mq
        JOIN email_subset e ON e.id = mq.email_id
        JOIN users u ON u.id = mq.user_id
        JOIN services s ON s.id = mq.service_id
        LEFT JOIN quick_quotes q ON q.id = mq.matched_quote_id
        ORDER BY
            e.rfc_message_id,
            mq.created_at DESC,
            q.final_quote_price DESC
    )
    -- Final select: Get complete result set with all needed fields
    SELECT
        fr.*,
        cnq.quote_id,
        fr.status as quote_request_status,
        q.pipeline as quote_pipeline
    FROM %s fr
    LEFT JOIN quote_request_quick_quotes_1tomany cnq ON cnq.quote_request_id = fr.id
    LEFT JOIN quick_quotes q ON q.id = cnq.quote_id
    ORDER BY fr.created_at DESC`,
		ValidQuoteRequestsCTE,
		dateCondition.Query,
		ValidQuoteRequestsCTE,
		MatchedQuotesCTE,
		ValidQuoteRequestsCTE,
		FinalResultsCTE,
		MatchedQuotesCTE,
		FinalResultsCTE,
	)

	params := []any{serviceID}
	if dateCondition.Params != nil {
		params = append(params, dateCondition.Params...)
	}

	return quoteRequests, rds.WithContextReader(ctx).Raw(query, params...).Find(&quoteRequests).Error
}
