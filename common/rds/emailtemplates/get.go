package emailtemplates

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetEmailTemplatesByTypesAndServiceID(
	ctx context.Context,
	serviceID uint,
	templateTypes []models.EmailTemplateType,
) (*[]models.EmailTemplate, error) {

	var emailTemplates []models.EmailTemplate

	query := rds.WithContextReader(ctx).Where("service_id = ?", serviceID)

	if len(templateTypes) > 0 {
		query = query.Where("template_type IN ?", templateTypes)
	}

	return &emailTemplates, query.Find(&emailTemplates).Error
}

func GetEmailTemplateByTypeAndIDs(
	ctx context.Context,
	userID uint,
	serviceID uint,
	templateType models.EmailTemplateType,
) (template *models.EmailTemplate, err error) {
	// First try to find a user-specific template
	err = rds.WithContextReader(ctx).
		Where("user_id = ?", userID).
		Where("template_type = ?", templateType).
		First(&template).Error

	// If no user template found, fall back to service template
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = rds.WithContextReader(ctx).
			Where("service_id = ?", serviceID).
			Where("template_type = ?", templateType).
			First(&template).Error
	}

	return template, err
}
