# https://github.com/golangci/golangci-lint-action#how-to-use
name: golangci-lint

on:
  pull_request:
    branches: ["*"]

permissions:
  contents: read

jobs:
  golangci:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v7
