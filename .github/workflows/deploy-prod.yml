name: Deploy all lambda functions to prod

on:
  push:
    branches:
      - release
  # allow manual trigger from the actions tab
  workflow_dispatch:

jobs:
  deploy-rds-migrate:
    name: beacon-rds-migrate
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-rds-migrate
      working-directory: fn/migrate
    secrets: inherit

  run-rds-migrate:
    needs: deploy-rds-migrate
    name: beacon-run-rds-migrate
    uses: ./.github/workflows/invoke-fn.yml
    with:
      function-name: beacon-rds-migrate
    secrets: inherit

  deploy-ingestion-gmail:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion
      working-directory: fn/ingestion/gmail
    secrets: inherit

  deploy-ingestion-outlook:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion-outlook
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-outlook
      working-directory: fn/ingestion/outlook
    secrets: inherit

  deploy-ingestion-front:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion-front
    if: github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-front
      working-directory: fn/ingestion/front
    secrets: inherit

  deploy-processor:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-processor
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-processor
      working-directory: fn/processor
    secrets: inherit

  deploy-api:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-api
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-api
      working-directory: fn/api
    secrets: inherit

  deploy-send-email:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-send-email
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-send-email
      working-directory: fn/sendemail
    secrets: inherit

  deploy-poller:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: drumkit-poller
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: drumkit-poller
      working-directory: fn/poller
    secrets: inherit

  deploy-watch-inbox:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-watch-inbox
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-watch-inbox
      working-directory: fn/watchinbox
    secrets: inherit
